version: '3.8'

services:
  openai-wrapper:
    build:
      context: .
      dockerfile: Dockerfile
    image: openai-gaia-wrapper:latest
    container_name: openai-gaia-wrapper
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-https://api.openai.com}
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - .:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload 