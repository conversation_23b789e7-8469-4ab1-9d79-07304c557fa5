{"task_id": "t3_evaluate_data", "input_parameters": {"input_data": [{"count": 9, "engines": ["google", "bing"], "query": "difference between variational mode decomposition and empirical mode decomposition", "results": [{"result_index": 0, "engine": "Google", "title": "A Comparative Study of Empirical and Variational Mode ...", "link": "https://electricajournal.org/Content/files/sayilar/28/72-77.pdf", "description": "", "source": "https://electricajournal.org › Content › files › sayilarhttps://electricajournal.org › Content › files › sayilar", "main_content": "%PDF-1.6 %���� 358 0 obj > endobj 383 0 obj >/Filter/FlateDecode/ID[ ]/Index[358 49]/Info 357 0 R/Length 122/Prev 748763/Root 359 0 R/Size 407/Type/XRef/W[1 3 1]>>stream h�bbd`\u0010``b``6\u0007� � ��\u0003,r\u001cDr��H��`�� ��-X\u001c�f�\u000f\"�ր�σ�@�� >stream h�b```e``�b`g`��� Ā\u0000B@\u0019V\u0006\u0016\u0006\u000e\u00153�\u0000\u0007�\u0019\u001f.��]�M����\u0003\u0003��B�\u000eX� �|���y?�k$�^�\\S\u001c?#^J��}\u001b��!\u000f�w�]\u0004'~ȋa`��``t�\u0000\u0001\u0006\u0010\u0007L�0�h@5\u001d\b�\u0018�լ��8\u0010˃E\u0002\u0018\u0004\u0019�0�=�r�􃨅V��\u001f[?�\u001e���\u000e =\u001f| > endobj 360 0 obj >/ExtGState >/Font >/ProcSet[/PDF/Text]/XObject >>>/Rotate 0/TrimBox[0.0 0.0 552.756 765.354]/Type/Page>> endobj 361 0 obj >stream H�\\�ˎ�0\u0014\u0006�}��˙�(�}| u�j�]���n��C��\u0019�k��*�\u0019�����^#�8�\u000ey�s��\u0006�R��y��������'�m��\u0016�Y� ���\u001e���\u0015yƌ���y��f��ґ�R��\u0016{�\\����n�g�T�؛��\u0016�\u001e\u0007�\u0013fA�����\u0019{q�9 ��`v4;�ݒy�L��Sh\u0010\u0018�\u0006�Ah\u0010\u0018�\u0006�Ah\u0010\u0018�\u0006�Ah\u0010\u0018�\u0006�Ah\u0010\u0018�\u0006�AXCA �5\u0014�Ph�lc \u00055\u0014�PPC�\u001az�������{�=�\u001e~O��������{�=�\u001e~O�����a�4{�=�\u001eN�AaP\u001a\u0014\u0006�AaP\u001a\u0014\u0006�AaP��Kd\u001a\u0014\u0006e \u00155Tz�s��( >stream h��Yis�8\u0012�\u0005�\u000f��Ԗ� .�[S����;��J&�㸦h ���H��2���} ���X>v���R�\u0000qv7�~� ��HD*5Q\u001c��m\u0014K~O\"��=����,RFG*\u0013���,����e�\b�\\E�6\u0018��H�Ġ`x\u0006 ��M$�'�y�O\u001a�XrM\u0016��$�\u0016\"�\u0002\u001d��#�R,�I��\\P�Mb��Q�\\0�M!�O?���Cz���P��9\u001e��\u0011\u001d\u001e��I]4Ӳ������aS�\u0015Pà�������{����(�����3���JWM���ǏO�8�>Ř=/\u000e\u000f؟�u���˥�������G\u0002C�ˉ@?qJ��\u0015\u001d��?���M`/��]�`7+wc\u0005{*�k3sJ��Yϓ�(��aڽ�nzH@G\u0007\u0007�y禑J�Z��It�W\u001dVI��v�Wt�\u0017Ż�^���v吿�\u0004M����e�}D�څٟ\\��&}�;*r?�Y���\u001fo�]�*#���Dh�5⻭�\u001e�����V\u001a���\"�\"W����]@��z�����ay~�ZW\u0017�;����8���U] ��Ѣ�W\u001d�*�ڮ�-r�A��M˪�i����:��5���}vt���(\\ݟ\u000e\"\u000e\u000b\u0006�;n�2e� _>w�l��\u001e��y�N\\��\u0007�A�2/\u001c}Z5���U�t���\u0017(uy= M\u001dd�\u001c-sؤr�}(�,!�]��\u0012�_V\u0010�h\u0016\u0010c~����g+�)uU���/�6�\u001f����~�:G�ͪ��\u0012\u0012u�\u0005u\u0010�&�6�%:\u0016M�Ԩ^����\u0001�O+l���=ڧ\u0003:�'����0\u001d�?�gzA�*����/ṫ��ѯ��~���t���G�{����\u0001�QAS�@4�9��/�H\u0015-������Zꨧ\u0015}�?��.��`�0|4��x| M����E�� \u0015�\u0015\u001d�\u001f����\u001e�J\u0018m�Z�U�[.l�\u001di\u0004=��Y\u0017)����~sq�\u0013�+q[\u0014\u000bmyӜ�Ƨ���.\u001f��z�4z�6�\u000b^�Qh,+��,�s��W�\u0002\u0007l��͓��1\u0018�����Iߺ���\u0007��އ�����>��b��\u0006;��-~�t\u001a�6we\u001d�r�7-�6�g�����.�\u0016�r��\u0016/m�v=��\b\u0007���=�p�\u0017���ȵ�dibzۼ����.R6�굔8��~ޝ$V1�D��\u00078��H`��@9\u0000�ȼ��F\u0019�#\u0001g�>���(QId�Pq�\u0018i|����b�g ��\\\u0001\u00165p�.���޵�,~\u0015@�\u0001:\u001b��I\u0000���\u0016�Z\u0000�\u0004$*�eS�D��U�Q\u0002�����\u0018'��x�4����=�R ��\\��*�\u001a���?�\u001dg l�}4ۇ���l����1��B.�\u0014��z��\u001f�v��\u0003؆Jp\u001b,��\u0004دag�\u0003��{��m�O�_�m�M�--F�;�7����Ƿ��v������=a+�\u0002�Y�Nyč ����\u000f�#E@���o\u0002�4�\u0006Z�[�\u0001���� \u0010U��5h]\u000bO?\b�\u0002\u0012�\u000eC\u001f\u001el\u0002я���\b-p�\u0001Z$�z|�-��%��[@e����`J�\u0005Sb���A%�\u0005T��\u001b���Ɣ�[L1����zL1�i�4Q�~�}& g�\u0000�r����\u0006��C��Z�\u0016\u0003=mf��32\u0001\u0002\u0002!��XZ $�X� O��l6\"P\u001cr�[\u00185�0\u000e�>M�3Z�\u0010�� yx��e�Q�%\u0013���\u0015���4\u0010*��y\u001c�\u0002�1��\\�s�8��\u0013BH�,K�X\u0005� sG$`���=1d���c-��+Knlg�ފ\u001d&��T��ء�ǭ\u0006�q������y^R�/Φ9M ���بy��\u001dC��\u0007�v�����mz �/VU_.�Kz���\u0007}�\\�@���f���`��b��\u0010\"�`\u000f8Vw \u0011�-!±��*\u0018��(Ao�\u0012�ۘ��%H\u0018�߈\u0013���\u0004�m���'LPׇ )B\u0004\u000f�\u0000\"\u0006_\u000fc�@���T��]�\u0000݋\u0015\u0002%��1��Z\u0006Ԍ铇C���!��V �� ԀH�ûdz�qRy0�j& �Vxʗj�� \u0015���|�1S|�$�1^3\u001fxha=Q�q��\u0006Pn��bm�&^>k��O%\"Po�!��^�\u0006�(9��׋�g\u001al��A�az�!\u0005�&�u6?� ��:�@�bl�ɱ�e (�̤��\u000f��B:\u0004\u0012�C\u001a\u000e�8�ҙ\u001fg�\u0019\u0015B%���0��\b�\u0014\u0013q�rx��/X�͠k��b+K\u001dM�o� \u0006�ža�u\u0001Ƹ��G��z\u001b��e1�|�g�����Vl�!0I�O�47������-��\u000f�f ��\u001bQ� ���5:�!�Kwp�\u0015\\w�������+\u001a.�`l��˳�.�gG��>ۉ��\u0002M�\u000b���+�oB�+wf�����{cßű��ϒ�z���_���z�����.P��\u0006ұ�\u0019R�\u000f��\"\u001fjS�\"8�a�\u001cL��U,���7��C������\u0007>�e��Y۬���\u0014� \u0017�WJ\u0018�zC�l�[��{�/���;>��p��˿\u0013��(��M;#\u001cS��$w�I�\u001dx\b��k�n\u0010��\u0002 \u0000�^�� endstream endobj 363 0 obj >stream H�\\��n�0\u0018\u0005�=O�e�����6�\u0010\u0012�I,����\u0003��t��\u0010\u0005�����'SکD����\u001d�$�jW���5[~���s�f���O�r~��������P:���u>J���~\\,�����\u001aN��p^����G \\�}\u0017���\u001a\u0016� %3�\u0007l�,% %�%�\u0015�V�[s\u001fj�k�ש�k�X��Z �j�� ��a�\u0006�\u001b�U��-������-�o9g������0�����l�6M�\u0005�^��͊w�q\b���� ��\u0002 \u0000w{�\u000f endstream endobj 364 0 obj >stream H�\\��n�0\u0014\u0005�}���vQ����\u0013 !A\u0000���h� �a��W���>>���m\u0018��K�'���ʴ�\u001c\u001f��4|;]�Y�mO�6����S�����>\u0004S�yALsm�m85a �E��-ݼ7��m\u001cc\u000fN}?5_�ݮ\u000f��\u001a��`�.|��\u0002 \u0000I\u0019l� endstream endobj 365 0 obj >stream H��W�r�F\u0012}�W�#�e@\u0018 �ޗ�E����vm�y�\\)\b�D�A�\u0016\b)�����\u0019\u0010\u0014%'[v�\u0018`�/��O��^-��| U5������\u0016g?��Ͱ8[��C���z��A\u0018�U���^����'�� �#�� � � ��;��a̽�Cx(_� ���@y\u0000$R˕����V�+��\u001c��,\u0017\u0007O�'k��4�|�>�m\u0003�9\u0001���%�����[�_�\u001e�������\u0011\u0006u�i��*�1u7�]}�%jY^��b�z����p\u0015����O�@Id�S'N�\u001f�\u0017\u0006�:��&��{�)e����J\u0010{ᶗ7�/6͍;mm�>�\u001d9� \u0014��\"�9�v3)��_��$BŇ%2�\u0014\u0019\u0007���xyG�Z[�~�\\ڏ���Lج�\u0001\b6\u001c�;+᪮;e��w��\u0014\u0015\u0006g��=Z����\u001c�y�\"K�\u00100���3p�P{�^t\u001c��A]D~�5p�V������)���*y�z\u0006U{���Ms��U�w~�tuy;��{Fg���Ib;��[�~������c� #c�ͶF�r^�Cm\u0003\u0007gfV��� �\u000f���8����ݡ��-�����\u000e�榴�A^\u001d�i\u0012�اY�w�����Y���\\�o��l� �\u0010��bV\u000fe�KG ��\u00101X\u001e\u0007j�:�\u000f��\u0000v�,�n�\u0006���$��\u001c���a\u000f\u0006\u001d�ǜA�g�� J� H�K�I����y�t7l��DS\u0017��} \u0014z\u0011I��t�\u000f�&�\u001d�%v �M٢�\\)I\u0002r�\u001f\u0003��ˁ��.����a�:�&�&F�b&/���\u0012��K�&�,�.����'�*��n�L�}\u001c 6�i:{�����Z\"�U4��2�\u0012F�>\u0018sP�G�����+���t梵�X��T7���g J�R9��g���q� ��'�\u000e$e�}�\u000b� s�ޮ�5�m��թ×��\u000fo�4\u0010'r�������y�O`ۗI ||��\u0005�)&�}N\b�\u0015�3la�������T���oz��B���\u0002���sn�\u001d�*�I\u0016w��W�\u001b�ϑ����\u0012�o�\u001bV8�g b��q��TwS \u001do>����-��\"�j���$���n6\u0013�*[|`.�\\���\u001d� �I_'��ݮE1\u001cXQ�\u0013�%&�YR�0�|®��a(!ѦXYm�`���l���\u0002�#�o�F02�\b`da�l���8\u0018N�i\u0018�\u0017����н�� ̶��L������\u0017�B!;\\�\u001d�u)��h�L\u0006�v\\\u0000P@��\" ��\u0016�\u0011�P%I\u0014d`�,M\u0002����^��:��\"\u001a����O���\u0019���gﶡz�/���W����=T�����&�!��Z\u0007&� ��Ѐ��\u0016؝ӕ��~��_��\u0011���B��S��r�/��*��,c�I���lv�4�� c�\u001b�Q�����W\u001f)�o�3\u0004�����o~=��,9\u0018�\u0005&\u0016{3eR���r�fb�+��Ӆ\u001b4�\\��\u0012ʤO��4�r��bpl*�})㭇�\u0019�\u001bGTp�=̵��[Y�Q�4�X\u001a�Dә%������\u00029`� �����f3�ҷ����F�\u0003��>q�U�c�5;l� -b\u001b\u001bq\u0017���S��C�C�X�>#�`ȟc'F#��v�{)\u0015\u0006i\u0002�A��\u001e\u0006Q\u0014)5T\u001deVD��]Y��N� N# �\u00121�ڲ���\u0004��6�O\u000b�~fr�,����(\u0003�Kه�B�\u001e����?\u001c��%�s%��L���5'?%�>�B':(4!���\u001c�fH�\u0018N\u001b���/�n�A\u0004�AH�8� �\u000f\u0010\u0000:\u001c�30� �0���o�p�,\"�ywPD\u0005��ߊ\u0018��ڟ���E�s-��z�`np\u0016\u0014i\u0004{5\u0018�0\u0007�}����\u0001�(�P�a�iRP�\u0016VY��*Q'��.춄��|˱�Dx�E���Ɯ�!�|�\u0004\u0016qƋ�7��/Kb^i���r9���0����X�eT� ̈�\u0018 ����^Y`c\u001c��}kݒ6�\u0017b\u001d�@^��X\u001b��n\u0001�)J���R\u0016\u001bR��b\u001c �V�\"fLBk��B��)�tV��\b�hOl)���-[�$���\u0015���$G � �T�M}�5�\u0006\u0003'a\u000f�%-&�l�\u001d�N���Le\u0011��4e*{�\u0017�z'�E��m뎯5�\u000e\u0014�\u0007lɸ\u0003 k?��9��uR�cB�iԫ��u����\b�����]���ܡMdr\u001f\u0005kK�{@�@N�P�������\u0002�h�4��b��#N��7\u0011^��a���9� ���MT�1u>�e^����{E=�,ɗ!�|c���l\u001b��4زҗ�\u000f�\u000b���@@\u0003��#���ɝ%��B]�HO��i;�4{� Mb��>&���#=�x�mG���\u001c`#\u0007\u0002\\� >stream H��W�r��\u0011}�WL�\u0013�\" ���7���ޔ�M�(\u000f�T \u0002!\u0011� \u0013�����t� \b���l��\"��=}9}�g��\u0013\bO�Jl>9��>�2\u0016���n���\u0012]��m%�A����䬄�����\u0001\u001bw�����b[\u001d�ܗb��fbU?\u0016\u0007���o��\u0002��\u001f:�Թa��p����VUź.6��RH���\u0006�!����ׯ\u001f�\u0014Z\u000f��/+!\u0003?��� 0ę� d��LJ��y�\\\\^\u0007��Z\u0004�O\\_�Ł�x\u0016��\u0012�7��y\u001c\u001bg~��P�\u0011\u0014�*\u0015�ޡ%���Ra2\u0014�s����q3�5c �\b\u0013_�\"\u000e\u0014~`�\u001eF^���\u001e��J ��\u0017\u0014 \u0015��X u�p\u0016�����o���ٕ!����!y��c��\u0006a�\u001cݜ�pS����t\u0013���\bs1�OnJ{+q=\u001c�/��\u0017���5�L�ĺ,\u001aQ�[��0��U\u0019m&�R,B�k�����m+���b��J����6 ����Ɉ\u0015>�F]dE3\u0014\u000f\u0010��rWX\u0010T�\u000f�ML&������MU\u000e��te¦��b-�\u0013�v�\u001cu0:$��\u0000��Z��+9Zr-\u001e\u001f]ɞ�\"U^��\u0005�� b~�,���\u001f�\u0002�Rxʏ\u0015�[B��כ��\u001e\u001c�誘4^��b�Y�\u0019�\"�'))���H빮�\u0014(�.ao��X4@�'\u0019\b/f\u0016��\u001fî2_(\u001ar�r�h��?�\u0006�����;�}Q�\u0003�����V\u0001-���R�a|\u0010:��G��g�:أ\u000fE��q\u0002�Z\u001fQ�Va3*�!J��ՠ\u0017Yag���i�=_�������\u0007�Y\u0016}E{)�k3W�\u001d\u0003�\u0007\u0006\u0005 0m���� �\u000b�[�,�ڍa�⨶v�����D!�|�s���顂Kj?���3�mU\u001e�� \u000e�\u0011צ��{6\u0018?��9�;�C��c۶��]іUo�\u000bs�\u0018Ѷ���y������\u0010��z��\u0017�A�N�1��x�գ;ۊ�{(��\u0010f\u0007�E/���\"��!���i������e�4��7��\u0007�\\��H����\u001b\u0015ߎUK`�t\u00011A�U;��y�\u0004�c����}W��\u0003���\u0007Mx�ǹ�2�@\u0010�\u0015\u0016� #Ó=�����2a�ٽ'��զa\\�]��)K\u00054��5[Y-�yO�;\u0002�p�$\b�\u0019\u000f߮>_}\u0002�)4OK\u000egU��8\u000b,r�R�E\u000f���q���؝r�\"��ĉ§1 ܄o{&6\u001bF�{_\u001dz�J!Пu�?\u0017�\u0004��S4 ;W\u001e��1�\u001d4�& ; �s� P�b~4{��h}3����-��\u001e��ן֯��W�\u0019���e%�22pj��\u0016���)F���rc\u000e�� �b[��\u0005L�*�\u001a\u0000d�A�\"ˣe&j��>�މ\u000b\u001ee�\u0004 ����a\u0000��\u0015j� ߸��c��q\"�$��8\u0012����hq1�\u0019Z}?\u000bSj��L��l��?]+��O^\u0012x?$J����(J��\u001d��iB�\u0005I/�H���*\u001fG\br�SKg�H�4\u000b�����sIGد���K�B�;���O��A�O���v��4\u0003ȇ�?'�C4O��-���|�B\u001d;�'R�(��� ٤`�b�&+�Blif���Iv ?�aQ \u0000�؏,��q�O\u000f�؏�D\u0018q\u0013���9\u0018Gٱ��\u0018��rf@�2��^02�\\��@}�\u001d\u0007VL������\u001aO��(����N~���\"��!��\u000ba�����\u0014a� ��\u001a -�&�O\"� ��\u0010���7\u0013�\u0016:�ف�5à\u0007֢q3���8��\u0019mNS��PF\u001a;��3����)#ox\b�Bv����\\��|�;�%o.�-��q�ᎀ�\u0000�J��&Z�\u000b6Њ\" \u0000�4\u0019GHk N�\u0005�\u00138 �r!1�8H�i�A\u00153���,U�\u000e�f��C��{60 r1�\u0001��(���8��I\u0013���X\u0018�\u0000S����l �)$#��Wyl\u0007t^��Q`���{�%p�\\�d�R����\u0006'W!���j�\u0012�B8�P�\u0019S�/Ɓ���z%oRޑ�O �4�evT�^���� \u001f%�\u000e7\u001e�\u0019*�\u001d�{t O�4}\u0017\b\u001a]��(�oe���s�5I�V��?�\"_����\u0012��\u0018��w䒂�(\u0017���\u000bB �h�l�\u0006����P�C/I�?FS�\u0002�U�+)z)~v�����7 �*�Q!�5Y�bI�W�\u0001\u0000\u0006L2� endstream endobj 367 0 obj >stream H��W�n�6\u0010��+�\u0003���h��\u001crJ��~�\u0001'\u0007'@\u0010���}���\u0019�\u001f�\u0000�\u0005 \u0003c�Z$�U�ʹ��׋mf�>-�������.���7;�*�B(V��\u0005bN\u0019b뱢P�ʤ\u000e+�t�ׄ���q���ʿ��Ͽ-iK��ߵl������ �������k�y������B ~ϻ�\u000e���m9���k��=��N��\u0014�?��\bǝ��G�ˈG�g�u\u001e7���[�z-�??�\u000bǙ���:V�Y_��r��0�����\u0015\u000f9t�\u0015���G3�U�+��R`Y�a\u001f���\u001f��7b�\u000e �j'g\u0018Ld��� lqv��\u0003�\u0017�@\u0004��m;�u\u001eLF��$Q��%67\u0000�`K~�e�1�E����|E���Da B\u000bW��\u0018A�u�LfԊW��s�2EH l��U�4�ҕ�����%&\u001c\u0016G��2�~���TI&X����� �A[$��U�L4}\u0018I��\u0012��\"aI\u001e}FE\u0001nO\u00115�\u0016@K��L(#3yR���&�Q�ѵ\u001aV L\u0001�R�{���y�\u0003�K{�L\u0018-�v\u001dc�+��w�>��'��\u0003q�7�\u0001Q��>Uz������\u0014�и�k��2�{�+Ijsɣ�\u0016���Ç�����M�NPg�`A�\u0000;eX�Dc�S\u0013zg��C&\u0019:V�$Q\u0013�D����}Nz��jE\u0010�3���\u001e �ڈ\u0013F����o��xzp�P��Q�g�^��]dd���ؾ�\u001a��A��_5I����\u0011�u�>�\u0017�l�\u0011�mr=\u0018:��\u0016�\u0015��h3�o�%�\u0017��N�͌��$x����~|�e��8�1 =��$ř$`�kL(T�K�\u0003�\u0007A�\u000fQ�n�2����I��z� �4E��|�S\u0018\u001fW-�\u001e�|��� N'�Ů]M&�0R-1i$xR�\u001d\u001ao�V�T������t,�؊�(�����\u000e� ��M1D�=0���� M��e�0� �ͽ������ޛzo齡�v�� 0\u0000�D/� endstream endobj 368 0 obj >stream H��WK�\\9\u000e��)�\u0002�\u0010IQ��ѫ9�1�^�\u0007���b\u0018\u0011JW\u0016��a �яO\u001f2\u0018 �gd �ϻ���}�/�����٥�K�����\u000f�%�\u001d�`���\u0011,���\u0015-i\u0013�\u0017c�`9�c�Ѥ�\u0014�ɺ�\u0012%d��:�\u0014�2��$�D�9S�y���\u0019|��\u00165\u001f�\b���8ڬ���c �\u000e��\u0007��ج��g\u001f�w\u0015T�QW�S4��Iy��TǦ� [���;%�`�y����V\u0012͹� \u00179p\u0011{�\u000e�l\u0011�(h�d�qS$\u001f�fɁ�v5$�\b��1� .{ض��5����I�A\u001e`!��-�\" [\u0013\u0015��*T\u0010��'�Zh��\u0014�,J�I�&~Vm�\u0006hQn)��C���b�'�\u0017\u001d��_�霝a܊o���N�su�n�]ql:M5����I����� ���\u0018GB���� )M4���U\u0012�����ł�5�8�d�Ԣ��I�F�I�.��\u000f��f�b���,���Q�\"��u�^�2� �\b���IQRy� )���\u0012�\u0007qq����6\u0007�)�\u000e�����ؚ��_q��p�:�r\u0006��j�\u0003`6�1#\u0014TgJ\u0019Q�\u001fI-d 'Dߤ�\u0002\u0014�ؒaA�^ U� ��\"2�\u0006��P\u0016��4�b�^�H���\u0014��������c�\u000e���T�ґ��*\u0001S�L0����\u0002A�]����갼qG�z���\b�+�BJp�@��lS��$�>:\u0016Z\u001cE\u0006����р�c�;TM-\u0018�T\u000e\u000e;�R#�G1+�r�A���cl��\u0018\u0002��\u0006K䬯�&��GL����%���h&�/����AI\u001f\u0002�B�ٷ:�\u001c%��\u0016�yG�N$\u001a֕��g�'� �\"5%\u0006U�Q\"�.ב��\u0006F 2.ݏ�+!���s�Vu���������G�J'jM\u0017��j���wT��e|U�K��v6g�\u001dR�� ��u�b\u000b22��.�\u001fb��4\u0002b��� :Nj�.���!ވA^¯\u0000�Tk�g��vOj�%\u0011\u0007�C�\u0014\b�\u000b��M�i� ��:�K�Mi!��!�b�%s\b�P��I���$\u0011`�\u001f��� �\u0007�Vc�HI\u0018oxã� �iMx��&�Põ�cbpr�\u000e��g鮼B]�\u0016d���0�z��j�\u000bTd!�� Br�\u0000ϥ�Y�r˔�.7]ˑ�B\"'C�aW\u000e1�G{�d��\u0005�\bZNB�2\u0016f��`\u0001���u��\u0005�,04��� �\u001f� � %�!�é\u0000\u0010`\u0000��2C endstream endobj 369 0 obj >stream H�lWK�%� ��)�\u0002�M��\u0013x���\u0007萬Ō\"lE��Bf�Uo\u0014\u0013�h�\u0011\u0005��$��s�q���\u0018���Q>���˾��8֭ ���P���>B쓢�\u0006\u0015��N\u0003nSz�����W)X���ɝRa0�f\u0017$�\u001b�i��\u0015�oa�}^0Ӯ�!աo��m�l:����]_N\b�\u0007V�2t�\u001a\u001b�5 �\u0010tX�E�\u0019�@�P��>�K\u0007�ݩ��b0p��$0\b{P�)�{It8��\"�GWD\u0014���VY4RtI:�*�cU'��\u0017�،ί\u001f����\u0015��gW_�}�k��ܵ����\u0003;�\u0011֪x��(\u0017��JW4\u0015�iri�\u0010z\u0018�� u�\u0011� �b\u001d#���뫑��# ��℃�)q��b�'6�t�$*[m��\u0014���D��5G.\u001b�\u0016�$��7\"���\u0004o���\"\u0006��^e�p0\u001c������tj1{m\u0001��t�V.�\u0005�@A*�ac �u��;��)�,�/���w��\u0007\u0012��C\u00137/�����Xg7�Ӟ�᥺ AO�E�]{�\u000e�9x\u001c\bm`\u0010x�\u0010��Af�iF��!����L5\"5{����Ie�H�X.\"n\u001c �\u0018Q��v�#�=�$)G,���[�ZD�5����s�/\u0011��0�����Hu\u0016n\u0015yC�;+����6�i�������\u0006T�^�jI/Uf�2�\u000b\b���l\u000e����\u0010�G���a`b����\u0010G�f>�\u0000�!8\u001d\\vv䠣�W>#Z�o��$p�nK�[H�\u001f�ʗJ��\u0003�rǉ�fK��\u0011\u0010��,�S�l&�;�M��\u0011S�-�'O\b�\u0002�Q\\,����T�\u0011\u001b�=h��������\u001b�\u001c�ff*:�\b\u0005��V,;��\u00105�Z�S\u0011h\u001a�tS \u0018ɠl�k׫\u0016R#E�:�:{�ң��L���\u0012����\u0007\u001b�I�d���u?�W��\u001eV�5��`��\u0000\u000e...", "page_scrape_duration": "1.850059997s"}, {"result_index": 0, "engine": "Google", "title": "Empirical mode decomposition vs. variational ...", "link": "https://ieeexplore.ieee.org/document/7732196", "description": "by U Maji · 2016 · Cited by 48 — This paper aims to study the performance of Empirical Mode Decomposition (EMD) and the Variational Mode Decomposition (VMD) technique over the popular ECG ...", "source": "https://ieeexplore.ieee.org › documenthttps://ieeexplore.ieee.org › document", "main_content": "A not-for-profit organization, IEEE is the world's largest technical professional organization dedicated to advancing technology for the benefit of humanity. &copy; Copyright 2025 IEEE - All rights reserved. Use of this web site signifies your agreement to the terms and conditions.\n\n", "page_scrape_duration": "1.230408622s"}, {"result_index": 0, "engine": "Google", "title": "Empirical mode decomposition vs. variational ...", "link": "https://www.researchgate.net/publication/309775818_Empirical_mode_decomposition_vs_variational_mode_decomposition_on_ECG_signal_processing_A_comparative_study", "description": "... The variational mode decomposition (VMD) defines a bandwidth constraint and non-recursively forces the decomposed modes to oscillate around their central ...", "source": "https://www.researchgate.net › publication › 30977581...https://www.researchgate.net › publication › 30977581...", "main_content": "Home Biomedical Signal Processing Time-Frequency Analysis Biosignals Medicine Physiology Empirical Mode Decomposition Conference Paper Empirical mode decomposition vs. variational mode decomposition on ECG signal processing: A comparative study September 2016 DOI: 10.1109/ICACCI.2016.7732196 Conference: 2016 International Conference on Advances in Computing, Communications and Informatics (ICACCI) Authors: <AUTHORS>", "page_scrape_duration": "26.807693043s"}, {"result_index": 0, "engine": "Google", "title": "Comparison of EMD, VMD and EEMD Methods in ...", "link": "https://iopscience.iop.org/article/10.1088/1742-6596/1577/1/012040/pdf", "description": "by S Hadiyoso · 2020 · Cited by 28 — Decomposition methods which are applied in this study include empirical mode decomposition (EMD), variational mode decomposition (VMD) and ensemble empirical ...", "source": "https://iopscience.iop.org › article › pdfhttps://iopscience.iop.org › article › pdf", "main_content": "To ensure we keep this website safe, please can you confirm you are a human by ticking the box below.\n\nIf you are unable to complete the above request please contact us using the below link, providing a screenshot of your experience.\n\nhttps://ioppublishing.org/contacts/\n\n", "page_scrape_duration": "834.092285ms"}, {"result_index": 0, "engine": "Google", "title": "A Comparative Study of Four Kinds of Adaptive ...", "link": "https://pmc.ncbi.nlm.nih.gov/articles/PMC6068995/", "description": "by <PERSON> · 2018 · Cited by 111 — In this paper, we present a comparative study of four kinds of adaptive decomposition algorithms, including some algorithms deriving from empirical mode ...", "source": "https://pmc.ncbi.nlm.nih.gov › articles › PMC6068995https://pmc.ncbi.nlm.nih.gov › articles › PMC6068995", "main_content": "Sensors (Basel) . 2018 Jul 2;18(7):2120. doi: 10.3390/s18072120 Search in PMC Search in PubMed View in NLM Catalog Add to search A Comparative Study of Four Kinds of Adaptive Decomposition Algorithms and Their Applications Tao <PERSON> 1 State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing 100084, China; <EMAIL> (T.L.); <EMAIL> (Z.L.); <EMAIL> (J.H.) 2 High-Tech Institute, Qingzhou 262500, China Find articles by <PERSON> 1, 2 , <PERSON><PERSON><PERSON> 1 State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing 100084, China; <EMAIL> (T.L.); <EMAIL> (Z.L.); <EMAIL> (J.H.) Find articles by <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> 1 State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing 100084, China; <EMAIL> (T.L.); <EMAIL> (Z.L.); <EMAIL> (J.H.) Find articles by Jiahong Huang 1 , Shaoze Yan Shaoze Yan 1 State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing 100084, China; <EMAIL> (T.L.); <EMAIL> (Z.L.); <EMAIL> (J.H.) Find articles by Shaoze Yan 1, * Author information Article notes Copyright and License information 1 State Key Laboratory of Tribology, Department of Mechanical Engineering, Tsinghua University, Beijing 100084, China; <EMAIL> (T.L.); <EMAIL> (Z.L.); <EMAIL> (J.H.) 2 High-Tech Institute, Qingzhou 262500, China * Correspondence: <EMAIL> Received 2018 May 12; Accepted 2018 Jun 20; Collection date 2018 Jul. © 2018 by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license ( http://creativecommons.org/licenses/by/4.0/ ). PMC Copyright notice PMCID: PMC6068995  PMID: 30004429 Abstract The adaptive decomposition algorithm is a powerful tool for signal analysis, because it can decompose signals into several narrow-band components, which is advantageous to quantitatively evaluate signal characteristics. In this paper, we present a comparative study of four kinds of adaptive decomposition algorithms, including some algorithms deriving from empirical mode decomposition (EMD), empirical wavelet transform (EWT), variational mode decomposition (VMD) and Vold–Kalman filter order tracking (VKF_OT). Their principles, advantages and disadvantages, and improvements and applications to signal analyses in dynamic analysis of mechanical system and machinery fault diagnosis are showed. Examples are provided to illustrate important influence performance factors and improvements of these algorithms. Finally, we summarize applicable scopes, inapplicable scopes and some further works of these methods in respect of precise filters and rough filters. It is hoped that the paper can provide a valuable reference for application and improvement of these methods in signal processing. Keywords: signal processing, non-stationary signal, narrow-band signal, adaptive decomposition algorithm 1. Introduction At present, a great number of scholars conduct investigations about adaptive decomposition algorithms. It is difficult to find a rigorous definition of the adaptive decomposition algorithm; however, we think that such a type of method can form a series of sparse representations in the decomposition process, which is different with “rigid” methods, such as the Fourier or wavelets transforms, corresponding to the use of some basis (or frame) designed independently of the processed signal [ 1 , 2 ]. As many kinds of signals in engineering problems are non-linear and non-stationary, such as fault signals of mechanical equipment [ 3 , 4 , 5 , 6 , 7 , 8 ], some modal test signals [ 9 ], acoustic signals of non-destructive testing [ 10 , 11 ] and condition monitoring signals for rail track [ 12 , 13 , 14 ], the adaptive decomposition algorithm has superiority for analyzing these signals, because of decomposition flexibility. Currently, empirical mode decomposition (EMD), empirical wavelet transform (EWT), variational mode decomposition (VMD) and Vold–Kalman filter order tracking (VKF_OT) are popular adaptive decomposition algorithms. These methods show excellent capacity of processing non-linear and non-stationary signals. Some important improvements have been done for EMD in some other algorithms such as complementary ensemble empirical mode decomposition (CEEMD), complementary ensemble empirical mode decomposition with adaptive noises (CEEMDAN) and improved complementary ensemble empirical mode decomposition with adaptive noises (improved CEEMDAN), which are more competent at processing non-linear and non-stationary signals. However, these adaptive decomposition algorithms have their own characteristics, which affect performances. Therefore, a comparative study that illustrates factors to consider when applying these adaptive decomposition algorithms will be welcome to researchers processing non-linear and non-stationary signals. Techniques that further process decomposition results of these methods are also valuable, so we will summarize this kind of technology in this paper. Furthermore, we present some further works that can be done for these methods in this paper, hoping some improved versions can be proposed to solve problems when processing non-linear and non-stationary signals. The remainder of this paper is organized as follows. Section 2 presents the principles and influence factors of the decomposition result, improvements of algorithms deriving from EMD and investigations of theory and application of EMD. Section 3 , Section 4 and Section 5 present principles, advantages and disadvantages of EWT, VMD and VFK_OT and investigations of theory and application of these algorithms. Section 6 summarizes the characteristics of these adaptive decomposition methods and points out areas for future work. 2. Algorithms Deriving from Empirical Mode Decomposition In 1998, Huang [ 1 ] proposed EMD, which takes intrinsic mode functions (IMFs) that are narrow-band components to act as basic functions, to obtain sparse representation of analyzed signals, as mentioned above. Decomposing signals into narrow-band components can result in advantages of time-frequency analysis. For example, multi-component signals can be decomposed into amplitude and frequency modulated (AM and FM) components, which makes it feasible for obtaining instantaneous frequency (IF) and instantaneous amplitude (IA) by using Hilbert transform (HT). Valuable components can be extracted by EMD, which is helpful for obtaining the necessary features of signals. Therefore, numerous researches of theory and application were done for EMD. Among these works, ensemble empirical mode decomposition (EEMD), complementary ensemble empirical mode decomposition (CEEMD), and complementary ensemble empirical mode decomposition with adaptive noise (CEEMDAN) are remarkable. Therefore we present the principles of these methods, and the corresponding superiorities over EMD. The issue of the limitation of frequency resolution of these algorithms and the influence of sampling frequency for decomposition results are discussed to provide a reference for employing these algorithms. 2.1. Empirical Mode Decomposition 2.1.1. Principle of Empirical Mode Decomposition EMD [ 1 ] decomposes a signal f ( t ) into a small number of IMFs. To be considered as an IMF, a signal must fulfill two conditions: (1) the number of extrema (maxima and minima) and the number of zero-crossings must be equal or differ at most by one; and (2) the local mean, defined as the mean of the upper and lower envelopes (The definition of “envelope” can be found in Ref. [ 1 ]), must be zero. The algorithm can be described as follows [ 1 ]: (1) Set k = 0, and find all extrema of r 0 ( t ) = f ( t ) . (2) Interpolate between minima (maxima) of r k ( t ) to obtain the lower (upper) envelope e min ( t ) ( e max ( t ) ). (3) The mean envelope is calculated by, m ( t ) = ( e min ( t ) + e max ( t ) ) / 2 (1) (4) The IMF candidate is obtained by, d k + 1 ( t ) = r k ( t ) − m ( t ) (2) (5) Repeat Steps (2)–(4) on d k + 1 ( t ) , until m ( t ) is close to zero. Then d k + 1 ( t ) is an IMF noted as c k + 1 ( t ) . (6) Compute the residue by r k + 1 ( t ) = f ( t ) − c k + 1 ( t ) (3) and do k = k + 1 . (7) Residue r k + 1 ( t ) is taken as f ( t ) , and repeat Steps (1)–(6) to generate the next IMF and residue, until the final r ( t ) satisfies the predefined stopping criterion. Therefore, the original signal f ( t ) can be represented by the following formula: f ( t ) = ∑ i = 1 n c i ( t ) + R ( t ) (4) where c i ( t ) is the i th IMF and R ( t ) is the final residue. The distribution of extreme values of a signal depends on the IA and IF of corresponding mono-components. It can learn from the principle of EMD that EMD utilizes it to extract IMFs in the process of sifting and iteration. Therefore, the method inevitably suffers from limitations in some domains, such as frequency resolution and influence of sampling frequency. These issues are presented in the following sections. 2.1.2. Limitation of Frequency Resolution Frequency resolution is important for the adaptive decomposition algorithm, as it is a crucial parameter determining the scope of application. Refs. [ 15 , 16 , 17 , 18 ] revealed that frequency resolution was related to the number of sifting iterations, stopping criterion threshold setting and the amplitude ratio between different mono-components. For stopping criterion threshold setting, at present, there is no authoritative statement, which may result from EMD’s lack of theoretical basis, and the parameter is set according to experience of a specific question [ 16 ]. Refs. [ 17 , 18 ] tried to improve frequency resolution of EMD using different masking operations. For the amplitude ratio between different mono-components, Ref. [ 15 ] pointed out the frequency resolution would decrease when amplitude ratio is greater than a threshold. 100 was taken as a reasonable number of sifting iterations in [ 15 ]. A great amount of computation work will be done with several iterations greater than 100, and we set this parameter as 2000, which may not be an ideal choice in a specific question. In the following section, research is performed concerning frequency resolution of EMD with open source codes, setting the number of sifting iterations as 2000, setting the stopping criterion threshold as 0.05, and this parameter is also set as 2000 in the following algorithms deriving from EMD. As indicated in Ref. [ 15 ], for a reasonable number of iterations, when the ratio between a relatively low frequency and a relatively high frequency is larger than 0.75, the two components of a signal cannot be separated. To illustrate the conclusion, we construct a sample signal f sig 1 , f s i g 1 = s 1 ( t ) + s 2 ( t ) + s 3 ( t ) s 1 ( t ) = sin ( 75 × 2 π t ) , 0 ≤ t ≤ 1 s 2 ( t ) = sin ( 100 × 2 π t ) , 0 ≤ t ≤ 1 s 3 ( t ) = sin ( 200 × 2 π t ) , 0 ≤ t ≤ 1 (5) The sampling frequency is 2 kHz. When the amplitudes of tone components are equal, different components tend to be isolated, so we set it as 1 in components of the sample signal f sig 1 . The waveform of the sample signal f sig 1 in the time domain is presented in Figure 1 . Ref. [ 19 ] reveals that greater correlation coefficients lead to more important corresponding IMFs for the original signals. Therefore, we pick out the IMFs, whose correlation coefficients with the sample signal f sig 1 are greater than 0.2. The coefficients of correlation between different IMFs and the sample signal f sig 1 are shown in Figure 2 . The correlation coefficients of IMFs 1 and 2 are greater than 0.2, so these IMFs are kept, and shown in Figure 3 . As illustrated in Figure 3 , the component s 3 of f sig 1 is extracted. IMF2 includes the components s 1 and s 2 , which mix together, as shown in Figure 3 b. The decomposition result above demonstrates the conclusion about frequency resolution of EMD. Furthermore, this conclusion just tells us that, when the ratio is greater than 0.75, the two tones will be taken as a single component, for a reasonable number of iterations. Figure 1. Open in a new tab The waveform of the sample signal f sig 1 between in time domain. Figure 2. Open in a new tab The coefficients of correlation different IMFs and the sample signal. Figure 3. Open in a new tab The waveforms of the IMFs 1 and 2 of f sig 1 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. 2.1.3. Influence of Sampling Frequency on Decomposition Result As mentioned above, EMD utilizes the distribution of extreme values of a signal to extract IMFs in a process of sifting and iteration. Generally, the distribution of extreme values depends on the IF and the IA of corresponding mono-components of the signal. However, for discrete signals, the true extreme value may be different with the theoretical value. Increasing the sampling frequency is advantageous for decreasing the difference, as shown in Figure 4 . The first maximum extreme value of the signal of 200 Hz is in a time of 0.00125 s, and the value is 1. With different sampling frequencies, the times of the extreme value are 0.00125, 0.001 and 0.002 s, and the corresponding values are about 1, 0.95 and 0.58, corresponding to sampling frequencies of 0.5, 2 and 20 kHz. Some false components may be generated from the error of the envelope calculation of cubic spline interpolation and calculation of mean value for the extreme values. To demonstrate it, we apply EMD on the signals with sampling frequencies of 0.5 and 2 kHz, and the signal with a sampling frequency of 20 kHz is taken as the original continuous signal. The decomposition results are shown in Figure 5 and Figure 6 , respectively. As shown in Figure 5 , when the sampling frequency is 0.5 kHz, the result of EMD is different to the original signal, and false components occur, as can be seen in Figure 5 b. It is deduced that, because the sampling frequency is not great enough, based on the extreme values, the envelope calculation of cubic spline interpolation and the further calculation of the mean value cannot generate the original signal. As shown in Figure 6 , when the sampling frequency is 2 kHz, the result of EMD corresponds to the original signal. It is deduced that, because the sampling frequency is great enough, based on the extreme values, the envelope calculation of cubic spline interpolation and the calculation of the mean value can generate the original signal in sifting and iteration process. Figure 4. Open in a new tab The distributions of extreme values of a signal of 200 Hz with sampling frequencies of 0.5, 2 and 20 kHz. Figure 5. Open in a new tab The EMD result of the signal of 200 Hz with a sampling frequency of 0.5 kHz and the corresponding Fourier spectrum: ( a ) the results of EMD and ( b ) the Fourier spectrum. Figure 6. Open in a new tab The EMD result of the signal of 200 Hz with a sampling frequency of 2 kHz and the corresponding Fourier spectrum: ( a ) the result of EMD and ( b ) the Fourier spectrum. The conclusion above suggests that, when EMD is applied to the process signal, a relatively higher sampling frequency is advantageous for generating correct decomposition result. Otherwise, an insufficient sampling frequency will result in false components occurring in IMFs. 2.1.4. Phenomenon of Mode-Mixing Caused by Intermittent Signals In the process of an IMF generation, EMD extracts the component with the highest frequency in every time section. Therefore, every component cannot be intermittence; otherwise, mode-mixing will occur. To demonstrate this, we construct a sample signal f sig 2 , f s i g 2 = s 1 ( t ) + s 2 ( t ) + s 3 ( t ) s 1 ( t ) = { 0 sin ( 50 × 2 π t ) 0 0 ≤ t ≤ 0.1 0.1 < t ≤ 0.6 0.6 < t ≤ 1 , s 2 ( t ) = { 0 sin ( 100 × 2 π t ) 0 0 ≤ t ≤ 0.2 0.2 < t ≤ 0.7 0.7 < t ≤ 1 , s 3 ( t ) = { 0 sin ( 200 × 2 π t ) 0 0 ≤ t ≤ 0.3 0.3 < t ≤ 0.8 0.8 < t ≤ 1 (6) The sampling frequency is 2 kHz. The waveform of the sample signal f sig 2 in the time domain is presented in Figure 7 , and the corresponding short-time Fourier transform (STFT) representation is shown in Figure 8 . We pick out the IMFs, whose correlation coefficients with the sample signal f sig 2 are greater than 0.2. The coefficients of correlation between different IMFs and the sample signal f sig 2 are shown in Figure 9 . The correlation coefficients of IMFs 1, 2 and 3 are greater than 0.2, so these IMFs are kept, and shown in Figure 10 . If the conclusion mentioned above is correct (in the process of an IMF generation, EMD extracts the component with the highest frequency in every time section), the time-frequency distributions of IMFs 1–3 should be as follows: for IMF 1, the frequency of signal in time interval [0.1 0.2] s is 50 Hz; and the frequency of signal in time interval [0.2 0.3] s is 100 Hz; and the frequency of signal in time interval [0.3 0.8] s is 200 Hz. For IMF 2, the frequency of signal in time interval [0.2 0.3] s is 50 Hz; the frequency of signal in time interval [0.3 0.7] s is 100 Hz; for IMF 3, the frequency of signal in time interval [0.3 0.6] s is 50 Hz, as shown in Figure 11 . As can be seen in Figure 10 , the frequencies in different time sections of IMFs 1 and 2 signed by different red rectangles seem different. To make the time-frequency distribution visible, we do STFT for the IMFs 1–3, as shown in Figure 12 , which verifies the ideal time-frequency distributions of IMFs 1–3. Therefore, our deduction is correct. Figure 7. Open in a new tab The waveform of the sample signal f sig 2 in time domain. Figure 8. Open in a new tab The STFT of the sample signal f sig 2 . Figure 9. Open in a new tab The coefficients of correlation between different IMFs and the sample signal f sig 2 . Figure 10. Open in a new tab The waveforms of the IMFs 1–3. Figure 11. Open in a new tab The ideal time-frequency distributions of the IMFs 1–3: ( a ) IMF 1; ( b ) IMF 2 and ( c ) IMF 3. Figure 12. Open in a new tab The STFT representations of the IMFs 1–3: ( a ) IMF 1; ( b ) IMF 2 and ( c ) IMF 3. As revealed in the discussion above, if components of a signal are intermittent, mode-mixing will occur. To resolve this problem, EEMD was proposed. 2.2. Ensemble Empirical Mode Decomposition 2.2.1. Resolving the Problem of Mode-Mixing Caused by Intermittent Signals Wu and Huang [ 19 ] proposed EEMD, which is a marked milestone in the development of EMD. White noise can provide a uniformly distributed scale in the time-frequency space. It can provide similar scales of reference gridings to automatically associate with the intrinsic oscillations in the signal with different scales. Therefore, all the intrinsic oscillations become continuous in the whole signal. As mentioned above, EMD extracts the component with the highest frequency in every time section. Since no intermittence occurs in each intrinsic oscillation, the mode-mixing caused by intermittent signals can be avoided. Afterwards, the mean operation “forces” the mode to stick to the original signal in those portions where new extrema are created, while it remains unmodified in the rest of the signal (where no creation of extrema occurred). Therefore, the mode-mixing caused by intermittent signals is solved. To illustrate this, we employ EEMD on the sample signal f sig 2 . According to the coefficients of correlation between different IMFs and the sample signal f sig 2 , we extract valuable IMFs 1–3, and present them in Figure 13 . As can be seen in Figure 13 , the mode mixing caused by intermittent signals is resolved (especially in Figure 13 b). Figure 13. Open in a new tab The waveforms of the IMFs 1–3 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. 2.2.2. Principle of Ensemble Empirical Mode Decomposition In EEMD [ 19 ], the “true” modes are defined as the average of the corresponding IMFs obtained from an ensemble of the original signal plus white noise with different strengths. Let f be the analyzed signal. The principle of EEMD can be described as follows: (1) Signals f i ( t ) are generated by f i ( t ) = f ( t ) + β ω ( i ) ( t ) (7) where β is the variance of added white noise, and ω ( i ) ( t ) ( i = 1, ..., and i is the number that EMD is conducted) denotes a zero mean unit variance white noises N ( 0 , 1 ) . (2) Employ EMD to decompose completely each f i ( t ) , and obtain the IMFs d k i ( t ) ( k = 1, ..., k is the number of IMFs of EMD). (3) Calculate each final IMF by, c k ( t ) = 1 I ∑ i = 1 I d k i ( t ) (8) where c k ( t ) ( k = 1, ..., k is the number of IMFs of EEMD) is the k th IMF of EEMD. The extraction of every d k i ( t ) requires a different number of sifting iterations in EMD. 2.2.3. Limitation of Frequency Resolution Because EEMD derives from EMD, they suffer a similar frequency resolution. When the ratio between a relatively low frequency and a relatively high frequency is larger than 0.75, the two components of a signal cannot be separated by EEMD, for a reasonable number of iterations. To demonstrate this, we decompose the sample signal f sig 1 by EEMD, and the valuable IMFs are shown in Figure 14 . As illustrated in Figure 14 b, the components of 75 and 100 Hz cannot be separated. Therefore, this basic limitation of EMD is also suitable for EEMD. Figure 14. Open in a new tab The waveforms of the IMFs 1–3 of f sig 1 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. 2.2.4. Influence of Sampling Frequency on Decomposition Result As mentioned above, the decomposition result of EMD is influenced by the sampling frequency. The similar conclusion can also be obtained for EEMD. To illustrate this, we employ EEMD on a signal of 200 Hz with a sampling frequency of 0.5 kHz. IMF 1 is the valuable IMF for the decomposition result and is shown in Figure 15 . As can be seen in Figure 15 , false components also occur as a result of EEMD. Therefore, when the sampling frequency is not great enough for EEMD, correct extreme values cannot be obtained, which results in the envelope calculation of cubic spline interpolation and the calculation of mean value for the extreme values not being able to generate the original signal, either. A higher sampling frequency is also welcome in the process of EEMD. Figure 15. Open in a new tab The EEMD result of the signal of 200 Hz with a sampling frequency of 2 kHz and the corresponding Fourier spectrum: ( a ) the result of EEMD and ( b ) the Fourier spectrum. 2.3. Complementary Ensemble Empirical Mode Decomposition Although EEMD can avoid mode-mixing resulting from intermittent signals, which is crucial in the application of adaptive decomposition algorithms, adding white noise can introduce residue into the signal reconstructed by decomposition results of EEMD. The residue of the added white noise in EEMD can be extracted from the mixture of data and white noise via the ensemble IMFs with the positive added white noise (it should be noted that the residue mentioned this section is defined as the difference between the original and the reconstructed signals, and this is different from the residue (or trend) generated in the iterative calculation process of EMD). To suppress the residue, Yeh and Shieh [ 20 ] proposed CEEMD. In CEEMD, white noise is added in pairs to the analyzed signal (i.e., one positive and one negative) to generate two sets of ensemble IMFs. Therefore, two mixtures composed of the original data and added noise can be derived by [ M 1 M 2 ] = [ 1 1 1 − 1 ]   [ S N ] (9) where S is the original data; N is the added white noise; M 1 is the sum of the original data with positive noise, and M 2 is the sum of the original data with the negative noise. Then, the ensemble IMFs obtained from those positive mixtures contribute to a set of IMFs with positive residues of the added white noises. Similarly, the ensemble IMFs obtained from those negative mixtures contribute to another set of ensemble IMFs with negative residue of the added white noises. Thus, the final IMF is the ensemble of both the IMFs with the positive and negative noises. This operation can suppress the residue result from adding the white noise. To illustrate this, we construct a sample signal f sig 3 , and it is shown in Figure 16 , Figure 16. Open in a new tab The waveform of the sample signal f sig 3 . f s i g 1 = s 3 ( t ) + s 2 ( t ) + s 3 ( t ) s 1 ( t ) = sin ( 50 × 2 π t ) , 0 ≤ t ≤ 1 s 2 ( t ) = sin ( 100 × 2 π t ) , 0 ≤ t ≤ 1 s 3 ( t ) = sin ( 200 × 2 π t ) , 0 ≤ t ≤ 1 (10) We employ EEMD and CEEMD to process the signal f sig 3 . The coefficients of correlation between different IMFs and the sample signal f sig 3 show that IMFs 1–3 are valuable IMFs in EEMD and CEEMD. We present these IMFs in Figure 17 . A visual comparison of the results from EEMD and CEEMD shows almost no significant difference. However, differences between the reconstructed signals via the IMFs obtained by EEMD and CEEMD and the original signal are very large. There is a significant different between the final residues derived from EEMD and CEEMD, defined as the differences between the original and the reconstructed signals and are shown in Figure 18 . While the residue from EEMD has an average amplitude of around 0.03, the corresponding residue from CEEMD has an average amplitude close to 0 (of the order of 10 −15 ). Such an error could be very well attributed to the numerical error generated in the calculation. Thus, CEEMD can improve the decomposition results by removing the residue of the added white noise. Figure 17. Open in a new tab The waveforms of the IMFs 1–3 of f sig 3 by EEMD and CEEMD: ( a ) EEMD and ( b ) CEEMD. Figure 18. Open in a new tab Residues of added white noises derived by EEMD and CEEMD: ( a ) EEMD and ( b ) CEEMD. 2.4. Complementary Ensemble Empirical Mode Decomposition with Adaptive Noise The computation quantity of an algorithm is an important performance index. As shown in Figure 2 and Figure 9 , some useless IMFs are generated in EMD and EEMD, which degrades performance of these algorithms. Therefore, reducing the number of these useless IMFs is advantageous for improving the computation efficiency of these techniques. Torres [ 21 ] proposed CEEMDAN, and Colominas [ 22 ] proposed an improved version of CEEMDAN. Fewer IMFs may be generated on the premise of successfully separating different components of a signal by using the two algorithms, which can reduce the computational cost. 2.4.1. Principle of Complementary Ensemble Empirical Mode Decomposition with Adaptive Noise In CEEMDAN, the decomposition modes will be noted as d 1 ˜ and is proposed to calculate a unique first residue as: r 1 ( t ) = f ( t ) − d 1 ˜ ( t ) (11) where d 1 ˜ is obtained in the same way of EEMD. Then, the first EMD mode is computed over an ensemble of r 1 plus different realizations of a given noise obtaining d 1 ˜ by an averaging calculation. The next residue is defined as: r 2 ( t ) = r 1 ( t ) − d 2 ˜ ( t ) . This procedure continues with the rest of the modes until reaching the stopping criterion. The operator E j ( ⋅ ) is defined which generates the j th mode obtained by EMD. ω i is denoted as the white noise with N ( 0 , 1 ) . If f ( t ) is the analyzed signal, the method can be described by the following steps [ 21 ]: (1) I realizations f ( t ) + ε 0 ω i ( t ) are decomposed by EMD to obtain their first modes by d 1 ˜ ( t ) = 1 I ∑ i = 1 I d 1 i ( t ) = d 1 ¯ ( t ) (12) (2) At the first stage ( k = 1), the first residue is calculated as in Equation (11): r 1 ( t ) = f ( t ) − d 1 ˜ ( t ) (13) (3) Decomposition of realizations r 1 ( t ) + ε 1 E 1 ( ω i ( t ) ) , i = 1 , … , I is done, until their first EMD mode. The second mode is defined as: d 2 ˜ ( t ) = 1 I ∑ i = 1 I E 1 ( r 1 ( t ) + ε 1 E 1 ( ω i ( t ) ) ) (14) (4) The k th residue is calculated by ( k = 2 , … , K ): r k ( t ) = r k − 1 ( t ) − d k ˜ ( t ) (15) (5) Decompose realizations r k ( t ) + ε k E k ( ω i ( t ) ) , i = 1 , … , I , until their first EMD mode and define the ( k + 1)th mode as, d ( k + 1 ) ˜ ( t ) = 1 I ∑ i = 1 I E 1 ( r k ( t ) + ε k E k ( ω i ( t ) ) ) (16) (6) Go to step 4 for next k . Steps 4 to 6 are conducted until the obtained residue is no longer feasible to be decomposed (the residue does not have at least two extrema). The final residue satisfies: R ( t ) = f ( t ) − ∑ k = 1 K d ˜ k ( t ) (17) with k is the number of modes. Therefore, the analyzed signal can be expressed as: f ( t ) = ∑ k = 1 K d ˜ k ( t ) + R ( t ) (18) Equation (18) makes the proposed decomposition complete and provides an exact reconstruction of the original signal. According to Equations (14) and (16), the coefficients ε k can be adjusted to select the signal:noise ratio (SNR) at each stage. For the amplitude of the added noise, Ref. [ 21 ] suggested that small-amplitude values are adopted for signals dominated by high-frequency signals, and vice versa. In CEEMDAN, a few hundreds of realizations are done with a fixed SNR for all the stages. This value might depend on the characteristics of the analyzed signal. 2.4.2. Principle of Improved Complementary Ensemble Empirical Mode Decomposition with Adaptive Noises In the original CEEMDAN [ 21 ], the first mode is obtained in the same way as in EEMD. To extract the rest of the modes, a different noise must be added to the current residue. That particular noise is an EMD mode of white noise. For example, to extract the second mode, different copies of r 1 ( t ) + ε 1 E 1 ( ω i ( t ) ) must decomposed, where r 1 is the first residue. This operation generates a strong overlapping in the scales, and we are focusing in for the first two modes (first one extracted adding white noise and the second one adding E 1 ( ω i ( t ) ) . To reduce this overlap, Colominas [ 22 ] proposed an improved version of CEEMDAN to make no direct use of white noise but use E k ( ω i ( t ) ) to extract the k th mode. In the improved version of CEEMDAN, the operation M ( ⋅ ) is denoted as the operator, which produces the local mean of the upper envelope and the lower envelope, and the operation E k ( ⋅ ) is defined which generates the k th mode obtained by EMD, and ω i is denoted as the white noise with N ( 0 , 1 ) . The steps of the algorithm are as follows: (1) The local means of I realizations f ( i ) = f + β 0 E 1 ( ω ( i ) ) are calculate by EMD to obtain the first residue r 1 = 〈 M ( f ( i ) ) 〉 (19) (2) At the first stage ( k = 1), calculate the first mode by d ˜ 1 ( t ) = f ( t ) − r 1 ( t ) (20) (3) The second residue is calculated as the average of local means of the realizations r 1 ( t ) + β 1 E 2 ( ω i ( t ) ) . The second mode is defined as, d ˜ 2 ( t ) = r 1 ( t ) − r 2 ( t ) = r 1 ( t ) − 〈 M ( r 1 ( t ) + β 1 E 2 ( ω i ( t ) ) ) 〉 (21) (4) For k = 3, ..., K , the k th residue is calculated by r k ( t ) = 〈 M ( r k − 1 ( t ) + β k − 1 E k ( ω i ( t ) ) ) 〉 (22) (5) The k th mode is calculated by d ˜ k ( t ) = r k − 1 ( t ) − r k ( t ) (23) (6) Repeat Steps (4) and (5) to calculate the next r k ( t ) and d ˜ k ( t ) . Constants β k − 1 = ε k std ( r k ) (std( r ) means the standard deviation of r ) are chosen to obtain a desired SNR between the added noise and the residue to which the noise is added. It should be noticed that, in EEMD, the SNR between the added noise and the residue increases with the order k . This is because the energy of the noise in the k th residue, k > 1, is only a fraction of the energy of the noise added at the beginning of the algorithm. To emulate this behavior, in the algorithm, β 0 is selected in a way that ε 0 is exactly the reciprocal of the desired SNR between the first added noise and the analyzed signal: if the SNR is defined as a quotient of standard deviations, we have β 0 = ε 0 std ( f ) / std ( E 1 ( ω ( i ) ) ) . To obtain noise realizations with a smaller amplitude for the following stages of the decomposition, the added noise is calculated as ( β k − 1 = ε k std ( r k ) , k ≥ 1). 2.4.3. Comparisons among These Algorithms We construct a sample signal f sig 4 , defined as, f s i g 4 = s 1 ( t ) + s 2 ( t ) s 1 ( t ) = { 0 sin ( 65 × 2 π t ) 0 0 ≤ t ≤ 0.5 0.5 < t ≤ 0.75 0.75 < t ≤ 1 s 2 ( t ) = sin ( 255 × 2 π t ) , 0 ≤ t ≤ 1 (24) The sampling frequency of f sig 4 is 1 kHz. The waveform is shown in Figure 19 . We employ EMD, EEMD, CEEMD and improved CEEMDAN on f sig 4 to obtain comparisons among EMD, EEMD, CEEMD and improved CEEMDAN. The decomposition results are presented in Figure 20 . Because the principles of CEEMDAN and improved CEEMDAN are similar in a certain degree, therefore a test for improved CEEMDAN is just done on paper. To quantify the performance of the methods, we set the total number of decompositions as 100, and the amplitude of noise ε 0 as a recommended value of 0.2 for the three noise-assisted EMD variations (EEMD, CEEMD and improved CEEMDAN). Figure 19. Open in a new tab The waveform of the sample signal f sig 4 . Figure 20. Open in a new tab Comparisons among EMD, EEMD, CEEMD and improved CEEMDAN. As illustrated in Figure 20 , for EMD, as mentioned above, in the process of an IMF generation, EMD extracts the component with the highest frequency in every time section, so the component of 255 Hz in time section [0.5 0.75] s is extracted in IMF1 first by EMD (as shown in Figure 20 ), and mode-mixing occurs. The other noise-assisted EMD variations resolve this problem. However, fewer IMFs are generated in the improved CEEMDAN. To test the accuracy of decomposition result, we define the error with two-norm, e r r = ‖ IMF i − s i ‖ 2 ‖ s i ‖ 2 (25) where IMF i represents the i th IMF of the decomposition result, and the s i is the corresponding component of the original signal. The errors of decomposition results of EEMD, CEEMD and improved CEEMDAN are shown in Figure 21 . As can be seen in Figure 21 , the result errors of improved CEEMDAN also are less than that of EEMD and CEEMD. Figure 21. Open in a new tab The errors of decomposition results of EEMD, CEEMD and improved CEEMDAN. 2.5. Applications and Other Improvement Works of Empirical Mode Decomposition EMD can decompose a signal into several narrow-band components, which introduces the attractive feature of robustness in the presence of non-linear and non-stationary data. Therefore, a great number of investigations of theory and application have been done for EMD [ 23 , 24 , 25 , 26 , 27 , 28 , 29 , 30 , 31 , 32 , 33 , 34 , 35 , 36 , 37 , 38 , 39 , 40 , 41 , 42 , 43 , 44 , 45 , 46 , 47 , 48 , 49 , 50 , 51 , 52 , 53 , 54 , 55 , 56 , 57 , 58 ]. Ref. [ 6 ] reviewed the essential problems in improvement work and application. Ref. [ 23 ] reviewed recent mathematical progress on constructing a large bank of basic functions, establishing a fast adaptive decomposition algorithm, piecewise linear spectral sequences and a Bedrosian identity. Ref. [ 24 ] reviewed works on new stopping criteria and an online version of the algorithm. Ref. [ 25 ] discuss the way EMD behaves in stochastic situations involving broadband noise. In addition, the references above are all valuable for understanding and employing EMD. In this paper, we summarize some works that were published recently in mechanical engineering, consisting of three parts that are currently popular research issues, i.e., current applications of parameter identification of the mechanical system assisted by algorithms deriving from EMD, techniques applied to process decomposition results obtained by using EMD, or improved methods deriving from EMD about fault diagnosis and other improvement works of EMD about fault diagnosis. The applications of EMD above are based on precisely extracting targeted mono-components, which can be taken as a kind of precise filter. Another application of EMD can be taken as rough filter. Extracting fault signals can be taken as a typical application of rough filter. In addition, the aim focuses on highlighting some quantitative evaluation parameters of fault information. For precise filter operation, as time–frequency transformations generally offer useful insight into the dynamics of non-linear systems, EMD was widely employed to make parameter identification of mechanical systems more achievable. Yang [ 26 , 27 ] used EMD to isolate different modal responses from free vibrations, and then HT was applied to the instantaneous amplitude and phase angle time histories, which provide a basis for identifying the natural frequency and damping ratio of multi-degree-of-freedom linear systems. Khan [ 28 ] employed EEMD and Pareto technique to extract valuable components. After that, Recursive Stochastic Subspace Identification was employed to carry out the continuous modal parameter identification of the cable-stayed bridge. Pai [ 29 ] took time-varying amplitude and frequency of the first component extracted by EMD and HT as indicators for pinpointing times and locations of impulsive external loads to obtain extracting characteristics of non-linear systems and intermittent transient responses. Lee [ 30 ] developed a time-domain non-linear system identification technique based on EMD. Eriten [ 31 ] applied EMD to decompose a given measured velocity signal in terms of IMFs that provided information about the modal content of the signal, which provided a foundation for a non-linear system identification of frictional effects in a beam with a bolted joint connection., presenting a novel method based on Hilbert Huang Transform (HHT), combined by EMD and HT, for analyzing the non-linear and non-stationary Aerial Planting Projectile flight data signal. Chen [ 33 ] performed non-linear system identification on the acceleration signals that were experimentally measured at ten almost evenly spaced positions along a cantilever beam undergoing vibro-impacts between two rigid stops with clearances. In addition, EMD was used to obtain sets of intrinsic modal oscillators governing the vibro-impact dynamics at different time scales. Poon [ 34 ] attempted to use EMD to identify properties of non-linear elastic multi-degree-of-freedom structures. The IMFs obtained by EMD were used in the context of the non-linear normal mode method to estimate the properties of the non-linear elastic structure. Pai [ 35 ] presented a signal-processing methodology based on EMD and a new conjugate-pair decomposition method for characterization of non-linear normal modes and parametric identification of non-linear multiple-degree-of-freedom dynamical systems. To make fault diagnosis more convenient, some scholars employed some techniques on decomposition results. Bustos [ 36 ] proposed an efficient methodology based on EMD, which provided a set of parameters for the fast identification of the operating state of a high-speed train. Van [ 37 ] presented a novel two-stage feature selection, hybrid distance evaluation technique-particle swarm optimization to select the superior combining feature subset that discriminates well among classes. On this basis, a comparison among three types of popular classifiers— K -nearest neighbors, probabilistic neural network and support-vector machine—was made to establish the sensitivity of each classifier corresponding to the irrelevant and redundant features, and the curse of dimensionality. Wang [ 38 ] applied sample entropy to characterize the complexity of IMFs obtained by using CEEMD in different time scales. Then, a random forest classifier was untiled for identification and classification of fault modes of centrifugal pumps. Ali [ 39 ] used an artificial neural network to classify bearings defects, and a mathematical analysis to select the most significant IMFs. Zhang [ 40 ] utilized support vector machines optimized by inter-cluster distance in the feature space to classify the fault type. The permutation entropy values of the first few IMFs obtained by using EEMD were taken to reveal the multi-scale intrinsic characteristics of signals. Georgoulas [ 41 ] extracted fault features by using HHT, and then trained a hybrid ensemble detector to obtain detection of any deviation from the normal condition. Further, Georgoulas [ 42 ] employed hidden Markov models to automatically identify fault, and the inputs were feather parameters obtained by using complex EMD and HT. Meng [ 50 ] also employed a hidden Markov model classifier for malfunction recognition, in which the instantaneous energy distribution of signals were taken as the inputs. Zhao [ 45 ] quantitatively evaluated the complexity of the IMFs to obtain quantitative diagnosis of a spall-like fault of a rolling element bearing. Djebala [ 46 ] used an optimized wavelet multi-resolution analysis to analyze envelope spectrums of optimal IMFs and highlight the fault characteristic frequency. Bi-spectrums, a third-order statistic, which helps to identify phase coupling effects of IMFs were used to detect outer race bearing defects by Saidi [ 47 ]. Le [ 48 ] employed the radial basis function neural network based on chemical reaction optimization algorithms to identify the work condition of the gear, in which, the energy features extracted from valuable IMFs were taken as the inputs. Wang [ 49 ] applied independent component analysis (ICA) technique on IMFs that contained information of compound faults to effectively separate component fault features. Apart from the mentioned techniques, i.e., EEMD, CEEMD, CEEMDAN and improved CEEMDAN, some other improved versions of EMD were also proposed, which also have superior qualities to EMD. Zheng [ 51 ] proposed an adaptive data-driven analysis approach called generalized empirical mode decomposition (GEMD), in which different baselines were firstly defined and separately subtracted from the original data, and then different pre-generated intrinsic mode functions were obtained. Next, the pre-generated intrinsic mode function was subtracted from the original signal. A demodulating method called empirical envelope demodulation (EED) was introduced. Results revealed that the method consisting of GEMD and EED performed better in restraining the end effect, gaining a better frequency resolution and more accurate time frequency distribution. Zheng [ 53 ] also presented another improved version of EMD called partly ensemble EMD (PEEMD) to resolve the mode-mixing problem. In PEEMD, after the intermittency or noise signal was obtained in an ensemble way and was detected by permutation entropy, the residual signal was decomposed directly by using EMD. Similarly to Ref. [ 49 ], Jiang [ 54 ] put forward an algorithm called improved EEMD with multiwavelet packet, in which multiwavelet packet was used as the pre-filter to improve EEMD decomposition results. The result showed that the method can keep weak multi-fault characteristic components. Table 1 is designed to make this section more readable. Table 1. Summary of works for EMD. Objects References Methodologies Precise filter operation Non-linear system Lee et al. [ 30 ] EMD Chen et al. [ 33 ] Poon et al. [ 34 ] Beam with a bolted joint connection Eriten et al. [ 31 ] Non-linear system Yang et al. [ 26 , 27 ] EMD + HT Pai et al. [ 29 ] Aerial Planting Projectile flight data Goodarzi et al. [ 32 ] Non-linear system Pai et al. [ 35 ] EMD + conjugate-pair decomposition method Cable-stayed bridge Khan et al. [ 28 ] EEMD + Pareto technique Rough filter operation Bear Van et al. [ 37 ] EMD + non-local-means de-noising + particle swarm optimization + K -nearest neighbors, probabilistic neural network and support-vector machine Ali et al. [ 39 ] EMD + artificial neural network Georgoulas et al. [ 41 ] EMD + HT Georgoulas et al. [ 42 ] EMD + HT+ hidden Markov model Meng et al. [ 50 ] EMD + hidden Markov model classifier Zhao et al. [ 45 ] EMD + the approximate entropy method Djebala et al. [ 46 ] EMD + optimized wavelet multi-resolution Saidi et al. [ 47 ] EMD + Bi-spectrums, a third-order statistic Wang et al. [ 49 ] EMD + ICA Zhang et al. [ 40 ] EEMD + support-vector machine Zheng et al. [ 51 ] GEMD + EED Zheng et al. [ 53 ] PEEMD High-speed train Bustos et al. [ 36 ] EMD Centrifugal pumps Wang et al. [ 38 ] CEEMD + random forest classifier Gear Le et al. [ 48 ] EMD + radial basis function neural network Rotating machinery Jiang et al. [ 54 ] EEMD + multiwavelet packet Open in a new tab 3. Empirical Wavelet Transform In 2013, Gilles [ 59 ] proposed a novel adaptive decomposition entitled EWT, which combines merits of EMD and WT. EWT utilizes the Meyer wavelet siding along the time axis to conduct reconstruction instead of the orthogonal basis of sine wave, so the local characteristics of signals tend to be more accurately described than Fourier transform (FT). Moreover, in contrast with adaptive decomposition algorithms such as EMD, the basic function of Meyer wavelet generates in the calculation process of the inner product between the Fourier spectrum of signals and the Fourier spectrum of the Meyer wavelet; therefore, the frequency resolution of EWT depends on the frequency resolution of FT posing a promising frequency resolution, which can be easily deduced from Heisenberg’s uncertainty principle. 3.1. Principle of Empirical Wavelet Transform The principle can be found in Ref. [ 59 ], and we briefly explain the theory of EWT in the paper. There is an assumption in EWT that the Fourier support is segmented into contiguous segments. Segmenting the Fourier spectrum generates the limits between each segment (where ω 0 = 0 and ω N = π , the total number of segmenting section is N ). Λ n = [ ω n − 1 , ω n ] represents each segment. It is defined that a transition phase T n centers around each ω N . The empirical wavelets act as bandpass filters on each Λ n , as shown in Figure 22 . When ∀ n > 0 , Equations (26) and (27) define the empirical scaling function and the empirical wavelets, respectively. φ ^ n ( ω ) = { 1 , | ω | ≤ ω n − τ n cos [ π 2 β ( 1 2 τ n ( | ω | − ω n + τ n ) ] , ω n − τ n ≤ | ω | ≤ ω n + τ n 0 , otherwise (26) and ψ ^ n ( ω ) = { 1 , ω n + τ n ≤ | ω | ≤ ω n + 1 − τ n + 1 cos [ π 2 β ( 1 2 τ n ( | ω | − ω n + 1 + τ n + 1 ) ) ] , ω n + 1 − τ n + 1 ≤ | ω | ≤ ω n + 1 + τ n + 1 sin [ π 2 β ( 1 2 τ n ( | ω | − ω n + τ n ) ) ] , ω n − τ n ≤ | ω | ≤ ω n + τ n   0 , otherwise (27) Figure 22. Open in a new tab Segmenting Fourier spectrum into N contiguous segments. The function β ( x ) is an arbitrary function C k ( [ 0 , 1 ] ) that subjects to { β ( x ) = 0 , x = 0 β ( x ) + β ( 1 − x ) = 1 , ∀ x ∈ ( 0 , 1 ) β ( x ) = 1 , x = 1 (28) To obtain τ n , proportional is chosen to ω n : τ n = γ ω n where 0 < γ < 1 . Consequently, ∀ n > 0 , Equations (26) and (27) can simplify to Equations (29) and (30), φ ^ n ( ω ) = { 1 , | ω | ≤ ( 1 − γ ) ω n cos [ π 2 β ( 1 2 γ ω n ( | ω | − ( 1 − ω ) ω n ) ) ] , ( 1 − γ ) ω n ≤ | ω | ≤ ( 1 + γ ) ω n 0 , otherwise (29) and ψ ^ n ( ω ) = { 1 , ( 1 + γ ) ω n ≤ | ω | ≤ ( 1 − γ ) ω n + 1 cos [ π 2 β ( 1 2 γ ω n ( | ω | − ( 1 − γ ) ω n + 1 ) ) ] , ( 1 − γ ) ω n + 1 ≤ | ω | ≤ ( 1 + γ ) ω n + 1 sin [ π 2 β ( 1 2 γ ω n ( | ω | − ( 1 − γ ) ω n ) ) ] , ( 1 − γ ) ω n ≤ | ω | ≤ ( 1 + γ ) ω n 0 , otherwise (30) To get the boundaries ω n , we can segment the Fourier spectrum of signal on the basis of local maxima. The parameter γ can be set as value in internal [0, γ0) (Then the set { φ 1 ( t ) ,  { ψ n ( t ) } n = 1 N } is an orthonormal basis of L 2 ( ℝ ) and γ 0 is calculated by, γ 0 = arg min ( ω n + 1 − ω n ω n + 1 + ω n ) (31) W f ε ( n , t ) is defined as the Empirical Wavelet Transform. The detail coefficients are given by the inner product with the empirical wavelets: W f ε ( n , t ) = 〈 f , ψ n 〉 = ∫ f ( τ ) ψ n ( τ − t ) ¯ d τ = ( f ^ ( ω ) ψ ^ n ( ω ) ¯ ) ∨ (32) and the approximation coefficients W f ε ( 0 , t ) is adopted to denote them) by the inner product with the scaling function: W f ε ( 0 , t ) = 〈 f , φ 1 〉 = ∫ f ( τ ) φ 1 ( τ − t ) ¯ d τ = ( f ^ ( ω ) φ ^ 1 ( ω ) ¯ ) ∨ (33) where φ ^ 1 ( ω ) and ψ ^ n ( ω ) are defined by Equations (27) and (28), respectively. The reconstruction is obtained by f ( t ) = f 0 ( t ) + ∑ n = 1 N f n ( t ) = W f ε ( 0 , t ) * φ 1 ( t ) + ∑ n = 1 N W f ε ( n , t ) * ψ n ( t )   = ( W f ε ^ ( 0 , ω ) ⋅ φ 1 ( ω ) + ∑ n = 1 N W f ε ^ ( n , ω ) ⋅ ψ n ( ω ) ) ∨ (34) where, * denotes the convolution operators. 3.2. Advantage of Empirical Wavelet Transform As mentioned above, the frequency resolution of algorithms deriving from EMD is a basic limitation. When the ratio between a relatively low frequency and a relatively high frequency is larger than 0.75, the two components of a signal cannot be separated. This limitation can be broken by EWT, as its frequency resolution depends on Fourier spectrum posing a promising frequency resolution. To illustrate it, a sample signal fsig5 is employed. f s i g 5 = s 1 ( t ) + s 2 ( t ) + s 3 ( t ) s 1 ( t ) = sin ( 50 × 2 π t ) , 0 ≤ t ≤ 1 s 2 ( t ) = sin ( 600 × 2 π t ) , 0 ≤ t ≤ 1 s 3 ( t ) = sin ( 800 × 2 π t ) , 0 ≤ t ≤ 1 (35) The sampling frequency is 2 kHz, as shown in Figure 23 . EWT successfully separate components of 50, 600 and 800 Hz, as shown in Figure 24 . In Section 2.1.3 and Section 2.2.4 , it has been shown that the sampling frequency can influence the decomposition result by using algorithms deriving from EMD. When the sampling frequency is not sufficient enough, the errors of extreme location can result in error of decomposition result. However, EWT is immune to this within limitation of Shannon’s sampling theorem. To illustrate this, we show the EWT result and the original signal of 800 Hz within [0.4 0.45] s in Figure 25 . As shown in Figure 25 , the EWT result almost overlaps with the original signal, which indicates a high accuracy of EWT. Figure 23. Open in a new tab The waveform of the sample signal f sig 5 . Figure 24. Open in a new tab The waveforms of EWT result of f sig 5 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. Figure 25. Open in a new tab The EWT result and the original signal of 800 Hz within [0.4 0.45] s. 3.3. Disadvantage of Empirical Wavelet Transform 3.3.1. Limitation of Segmenting Fourier Spectrum Since empirical wavelets are generated by segmenting Fourier spectrum, when different components of a signal cannot be separated in Fourier spectrum, decomposition results of EWT will not be correct. To illustrate this, we construct a sample signal f sig 6 f sig 6 ( t ) = s 1 ( t ) + s 2 ( t ) + s 3 ( t ) , 0 ≤ t ≤ 1 s 1 = { 4 t sin [ ( 50 + 100 t ) ⋅ 2 π ⋅ t ] − 4 3 ( t − 1 ) sin [ ( 50 + 100 t ) ⋅ 2 π ⋅ t ] ,   0 ≤ t < 0.25 ,   0.25 ≤ t < 1 s 2 = { 2 t sin [ ( 100 + 200 t ) 2 π ⋅ t ] − 2 ( t − 1 ) sin [ ( 100 + 200 t ) ⋅ 2 π ⋅ t ] ,   0 ≤ t < 0.5 ,   0.5 ≤ t < 1 s 3 = { 4 3 t sin [ ( 200 + 400 t ) ⋅ 2 π ⋅ t ] − 4 ( t − 1 ) sin [ ( 200 + 400 t ) ⋅ 2 π ⋅ t ] ,   0 ≤ t < 0.75 ,   0.75 ≤ t < 1 (36) The sampling frequency is 2 kHz. The waveform of f sig 6 is shown in Figure 26 , and the corresponding STFT representation is shown in Figure 27 . The IF and IA of the sample signal f sig 6 are shown in Figure 28 . We employ EWT to process the sample signal f sig 6 , and the result is shown in Figure 29 . It is easy to establish that the EWT of the sample signal f sig 6 is unsuccessful. Moreover, there are some negative factors for finding boundaries of different mono-components, for example white noise that can introduce redundant extremes in the Fourier spectrum, which are essential for establishing boundaries of mono-components. Therefore, further work can be done to eliminate such negative influences. Figure 26. Open in a new tab The waveform of the sample signal f sig 6 . Figure 27. Open in a new tab The STFT of the sample signal f sig 6 : ( a ) 2D figure and ( b ) 3D figure. Figure 28. Open in a new tab The IF and IA of the sample signal f sig 6 : ( a ) IF and ( b ) IA. Figure 29. Open in a new tab The waveforms of EWT result of f sig 6 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. 3.3.2. Limitation of Selection of Detection Method of Boundary Successful decomposition of multi-component signals depends on segmenting the corresponding Fourier spectrum by using EWT. To improve the adaptivity of EWT, several detection methods of boundary are available in the code of EWT [ 60 ]. The same computation results may be obtained when the Fourier spectrum of the signals are simple. However, when the spectrum is complicated, a suitable method should be selected to improve decomposition result. To illustrate this, we construct a sample signal f sig 7 , as shown in Figure 30 , and the corresponding STFT representation is shown in Figure 31 . Each component of the sample signal f sig 7 is shown in Figure 32 . It consists of components of 50, 100 and 200 Hz, and the component of 200 Hz is amplitude-modulated. They are defined as components 1–3. The sampling frequency is 2 kHz. As presented in Figure 32 b, the Fourier spectrum of component 3 is complicated. We process f sig 7 by EWT, setting parameters used in processed code as follows, params.detect is set as “adaptivereg”, params.typeDetect is set as “otsu”. As shown in Figure 33 , it is clear that the decomposition result of f sig 7 is not promising. Therefore, when the signal is non-stationary, the Fourier spectrum tends to become complicated, and EWT will easily fail when doing mode separation. Though we can obtain a successful result by changing the detection method of the boundary, robustness of EWT is not promising. Researchers should pay attention to these characteristics to guarantee that the employment of EWT is correct. Figure 30. Open in a new tab The waveform of the sample signal f sig 7 . Figure 31. Open in a new tab The STFT of the sample signal f sig 7 : ( a ) 2D figure and ( b ) 3D figure. Figure 32. Open in a new tab Each component of the sample signal f sig 7 : ( a ) the waveform and ( b ) the Fourier spectrum. Figure 33. Open in a new tab The waveforms of EWT result of f sig 6 and the corresponding Fourier spectrums: ( a ) waveforms and ( b ) Fourier spectrums. The parameters used in processed code are as follows: params.detect is set as ‘adaptivereg’, params.typeDetect is set as ‘otsu’. 3.4. Application and Improvement Works of Empirical Wavelet Transform As illustrated above, EWT can separate different mono-components that do not overlap in the Fourier spectrum. Sometimes, we can employ this method to extract specific mono-components to a high accuracy. Yuan [ 61 ] presented a technique that combined the second-order blind identification method with the EWT to delineate closely spaced frequencies. In addition, EWT operated on the modal responses estimated by the SOBI and yielded the closely spaced natural frequencies. Hu [ 62 ] proposed a hybrid model that was composed of EWT, partial auto-correlation function and Gaussian process regression method for short-term wind speed prediction. In this approach, EWT was employed to extract meaningful information from a wind speed series by designing an appropriate wavelet filter bank. Reddy [ 63 ] applied EWT to extract the actual fundamental frequency component and disturbance components from distorted signals. In addition, then, time-varying power quality indices for accurate assessment of Power Quality Disturbances were estimated. Thirumala [ 64 , 65 ] proposed two different algorithms for the estimation of power-quality indices based on EWT. The results confirmed that EWT efficiently extracts the mono-component signals from the actual distorted signal and thereby accurately estimates the power quality indices. Li [ 66 ] proposed a novel approach for capturing the instantaneous pitch that may reveal some innate character of the speech, and EWT was employed to pick out the mode containing the pitch. Liu [ 67 ] presented an algorithm combining EWT, HT and short time Fourier transform obtain the TFD of ultrasonic testing waves. The different wave packages were isolated using EWT. In some situations, some residual noise is tolerable under the premise that the specific signal feature is strong enough, for example in fault diagnosis of machines, which is a popular research issue at present. In addition, EWT also can remove other unvalued components. Therefore, EWT and its improved version have been successfully employed in fault diagnosis of machines [ 68 , 69 , 70 , 71 , 72 , 73 , 74 , 75 , 76 , 77 , 78 , 79 , 80 , 81 , 82 ]. Since the main idea of EWT is defining a bank of wavelet filters based on the “well-chosen” Fourier supports, establishing targeted boundaries of the filters is key to extracting fault components from raw signals. To guarantee the obtaining of correct boundaries, some scholars also conducted investigations on the issue. Gilles [ 68 ] proposed a parameterless scale-space approach, which is easy to implement, is fast, and does not require any parameter, to find meaningful modes in histograms-application spectrum segmentation. The algorithm is based on the behavior of local minima in a scale-space representation, and the detection of such meaningful modes is the equivalent to a two-class clustering problem on the length of minima scale-space curves. Based on this method, Zheng [ 69 ] presented an improved version of EWT called adaptive parameterless EWT, in which the adaptive segmentation of Fourier spectrum led to the adaptive separation of empirical wavelets. In Ref. [ 70 ], the peak characteristic of autocorrelation function was used to judge the periodicity of each signal, and the most obvious signal was taken as the characteristic signal. An iteration decomposition of trend was presented. Kedadouche [ 71 ] presented another method of segmentation of the Fourier spectrum. The aim of the method was to separate different portions of the spectrum which were centered on a specific frequency, which presented the highest amplitude. To remove more useless components, the decomposition target was not the raw testing signal, but Combined Mode Function obtained by combining neighboring IMFs obtained from the EMD of the raw signal. Moreover, Kedadouche [ 72 ] also pointed out that EWT acts like a filter bank and employed operational modal analysis to define the support boundaries of the filter, and the algorithm was called operational modal analysis-empirical wavelet transform, which was better than the original version of EWT presented in Ref. [ 49 ] at decomposing multiple-component signals. To avoid the inaccurate segmentation of Fourier spectrum resulted from noises, Chen [ 73 ] conducted a de-noising operation by using wavelet spatial neighboring coefficient de-noising with a data-driven threshold. The result indicated that the technique was effective on weak fault and compound fault diagnosis. Pan [ 74 ] proposed a data-driven adaptive Fourier spectrum segment method for mechanical fault identification. In this technique, the inner product was first calculated between the Fourier spectrum of the analyzed signal and the Gaussian function for scale representation, and then, local minima of the scale representation were detected to obtain the adaptive spectrum segment. Hu [ 75 ] modified the segmentation algorithm by using the envelope approach based on the order statistics filter and applying criteria to pick out useful peaks. The proposed method obtained a perfect segmentation in decomposing noisy and non-stationary signals. In some references, EWT or its improved version is firstly employed to decompose signals to obtain valuable components that carry defect information. Then, another technique is used to process these valuable components to obtain detection of fault. Specifically, Huang [ 76 ] used one-class support vector machine to value components to achieve fault detection of high-voltage circuit breakers. Following HHT, HT is used to obtain IFs and IAs of valuable components to obtain early detection of tooth-crack damage in a gearbox [ 77 , 78 ]. In Ref. [ 79 ], each single fault frequency was incorporated into a duffing oscillator to establish its corresponding fault isolator, and the single faults were identified one by one from the empirical modes by directly observing the chaotic motion from the Poincar mapping of the isolator outputs. Zheng [ 69 ] presented an improved version of HT called quadrature derivative-based normalized Hilbert transform to process valuable components, and the proposed method could effectively fulfill the fault diagnosis of rotor rubbing. 4. Variational Mode Decomposition As mentioned above, the selection of detection method of boundary is an inconvenience of EWT. Therefore, an adaptive decomposition algorithm without this operation may be more welcome. In 2014, Dragomiretskiy [ 83 ] proposed VMD, determining the relevant bands adaptively. The method can estimate the corresponding modes concurrently, thus perfectly balancing errors between them to obtain separation of different components from signals. 4.1. Principle of Variational Mode Decomposition In VMD, Wiener filtering, the HT and heterodyne demodulation are combined, and an alternate direction method of multipliers (ADMM) is employed to obtain decomposing modes. The decomposed modes are localized on central frequencies. The bandwidth of a decomposed mode is generated in the following ways [ 83 ]: (1) HT is employed to estimate the one-sided frequency spectrum of a real signal using an analytic representation. (2) The modulation properties are utilized to obtain the shift of the frequency spectrum of the mode is shifted to the estimated base-band frequencies. (3) The H 1 Gaussian smoothness of the demodulated signal is applied to estimate the bandwidth. VMD represents a signal f ( t ) with a set of components called modes φ k ( t ) localized on the center frequency ω k . ADDM is employed to resolve the constrained variational optimization problem, which can be expressed by min { φ k } , { ω t } { ∑ k = 1 K ‖ ∂ t [ ( δ ( t ) + j π t ) * φ k ( t ) ] e − j ω k t ‖ 2 2 } , Subject   to   f ( t ) = ∑ k = 1 K φ k ( t ) (37) where δ denotes the Dirac distribution, and * and ∂ denote the convolution and partial differential operators, respectively. Equation (37) can be addressed by introducing a quadratic penalty and Lagrangian multipliers. The augmented Lagrangian is given as follows: L ( { φ k } , { ω k } , λ ) = α ∑ k = 1 K ‖ ∂ t [ ( δ ( t ) + j π t ) * φ k ( t ) ] e − j ω k t ‖ 2 2 + ‖ f ( t ) − ∑ k = 1 K φ k ( t ) ‖ 2 2 + 〈 λ ( t ) , f ( t ) − ∑ k = 1 K φ k ( t ) 〉 . (38) The modes φ k ( t ) in the frequency domain are estimated using ADDM in the form of the Wiener filter structure as follows: φ ^ k ( ω ) = f ^ ( ω ) − ∑ i ≠ k φ ^ i ( ω ) + ( λ ^ ( ω ) / 2 ) 1 + 2 α ( ω − ω k ) 2 (39) where φ ^ k ( ω ) , f ^ ( ω ) , φ ^ i ( ω ) and λ ^ ( ω ) are the FT of the components. Finally, the modes in the time domain are obtained computing the inverse FT of the filtered signal, and the center frequencies are estimated by ω k = ∫ 0 ∞ ω | φ ^ i ( ω ) | 2 d ω ∫ 0 ∞ | φ ^ i ( ω ) | 2 d ω (40) Thus, it is feasible to analyze the sub-components of a signal with the modes having localized center frequency properties. In this paper, we briefly illustrate the principle of VMD. The detail of calculation process can be found in Ref. [ 83 ]. 4.2. Advantage of Variational Mode Decomposition As mentioned above, VMD can determine the relevant bands adaptively. To invalidate this characteristic, we employ VMD to process the sample signal f sig 7 . Figure 34 shows the decomposition result of f sig 7 . As presented in Figure 34 , the three sub-components of f sig 7 are separated by VMD. Comparing Figure 33 with Figure 34 , it is easy to learn that the decomposition result obtaining be VMD is of a high accuracy, except for some end errors. So the end effect is also a research issue that needs to be resolved. Moreover, another advantage should be noted for VMD. The resulting optimization scheme is very simple and fast. In VMD, the narrow-band Wiener filter that corresponds to the current estimate of the mode’s center-frequency is applied to the signal estimation residual of all other modes, which generates each mode in iteratively updating directly Fourier spectrum. The center frequency is re-estimated as the center of gravity of the mode’s power spectrum. The computation intensity of these processes is low. Figure 34. Open in a new tab The decomposition result of f sig 7 : ( a ) the waveform and ( b ) the Fourier spectrum. 4.3. Disadvantage of Variational Mode Decomposition VMD segments the Fourier spectrum to obtain separating different components of a signal. Similar to EWT, therefore, this method also suffers from the limitation of the Fourier spectrum, i.e., when different components cannot be separated in the Fourier spectrum, they cannot be separated by VMD. There, VMD is also employed to process f sig 6 , and the result is shown in Figure 35 , and the STFT representations of the Comps 1–3 are shown in Figure 36 . As shown in Figure 28 a, the frequencies of components 1–3 are, respectively, in intervals [50 150], [100 300] and [200 600]. However, the frequencies of components 1–3 are in intervals [50 250], [150 600] and [400 600], as shown in Figure 35 b. So the VMD of f sig 6 is unsuccessful, which can be further verified by comparing Figure 27 with Figure 36 . Figure 35. Open in a new tab The decomposition result of f sig 6 by using VMD: ( a ) the waveform and ( b ) the Fourier spectrum. Figure 36. Open in a new tab The STFT representations of the Comps 1–3: ( a ) Comp 1; ( b ) Comp 2 and ( c ) Comp 3. However, a promising decomposition result can be obtained by using EMD. Figure 37 shows the decomposition result of f sig 6 by using EMD, and the corresponding STFT representations are shown in Figure 38 (it should be noted that IMFs 1–3 correspond to components 3–1 of f sig 6 ). As presented in Figure 37 and Figure 38 , EMD successfully separates the three components of f sig 6 . Therefore, EMD does not suffer this limitation of the Fourier spectrum. Figure 37. Open in a new tab The decomposition result of f sig 6 by using EMD: ( a ) the waveform and ( b ) the Fourier spectrum. Figure 38. Open in a new tab The STFT representations of the IMFs 1–3: ( a ) IMF 1; ( b ) IMF 2; and ( c ) IMF 3. 4.4. Application and Improvement Works of Variational Mode Decomposition As illustrated above, in VMD, separating different modes of a signal is translated into a constrained variational optimization problem. Therefore VMD allows adaptive decomposition of the signal into various modes by identifying a compact frequency support around its central frequency. Similar to EWT, the method can be employed to accurately extract specific mono-components from raw signals, and then some parameter estimations can be obtained. Upadhyay proposed approaches to obtain the instantaneous detection of voiced/non-voiced regions in the speech signals [ 84 ] and determine instantaneous fundamental frequency [ 85 ] of speech signals based on VMD. Yin [ 86 ] presented a microwave propagating mode extraction algorithm for microwave waveguide using VMD. A coated steel defect detection experiment was conducted using an X-band open-ended rectangular waveguide to evaluate the efficacy of VMD. In addition, for two samples, the VMD results could accurately identify the defects. Gao [ 87 ] proposed an online evaluation of metal burn degrees based on acoustic emission and VMD, and VMD was applied to extract the main frequency of AE burn signals. To resolve the problem that the features of ship-radiated noise were difficult to extract and were inaccurate, Li [ 88 ] presented a method based on VMD, multi-scale permutation entropy) and a support vector machine to extract the features of ship-radiated noise. Similar to EWT, as the fault diagnosis of machinery is a popular issue of dynamic analysis, a great amount of research regarding application and improvement has been undertaken for VMD in this domain [ 89 , 90 , 91 , 92 , 93 , 94 , 95 , 96 , 97 , 98 , 99 , 100 , 101 , 102 , 103 , 104 , 105 , 106 , 107 , 108 , 109 , 110 , 111 ]. After decomposition of signals by using VMD, signal characteristics of fault are obtained by some other methods. Aneesh [ 90 ] employed support vector machine to obtain detection of faults with statistical parameter vector of IMF candidates. In addition, classification results using support vector machine shows that VMD outperforms EWT for feature extraction processes and the classification accuracy is recorded. Lv [ 92 ] adopted multikernel support vector machine optimized by Immune Genetic Algorithm to diagnose outer ring damage, rolling damage, and inner ring damage of a bearing. The experiments of mechanical faults showed that, compared to traditional fault diagnosis models, the proposed method significantly increased the diagnosis accuracy of mechanical faults and enhances the generalization of its application. Muralidharan [ 93 ] used the J48 decision tree algorithm to identify the useful features, and the selected features were used for classification using the decision trees, namely Random Forest, REP Tree and Logistic Model Tree algorithms, and the performance analyses of these algorithms were done in detail. Liu [ 94 ] presented an algorithm to extract fault features of a rolling bearing, combining singular value decomposition and standard fuzzy C means clustering. The result showed that, in comparison to a similar process based on EMD, VMD was not sensitive to the initialization of standard fuzzy C means clustering and exhibited a better classification performance in the same load fault diagnosis. Tang [ 95 ] proposed a method to solve the underdetermined problem and to extract fault features based on VMD. After decomposition of signals by using VMD, the demodulated signals with HT of these multi-channel functions were used as the input matrix for ICA to separate compound faults of roller bearings. An [ 98 ] took permutation entropy of components carrying key fault information obtained by VMD of signals as a bearing fault characteristic value, and the nearest neighbor algorithm was employed as a classifier to identify faults in a roller bearing. An [ 100 ] also used the K nearest neighbor algorithm to extract energy characteristic parameters from components carrying defect information decomposed by VMD to obtain fault diagnosis of rolling bearings of a wind turbine. Yang [ 101 ] employed local linear embedding to reduce the dimensionality of these extracted features extracted from both VMD sub-signals and the original one and made the samples more separable. Then, multiclass support vector machine was used to diagnose mechanical faults of a rotor-bearing-casing system. Huang [ 102 ] divided the IMF matrix obtained by using VMD into submatrices to compute the local singular values. In addition, a multi-layer classifier composed of two one-class support vector machines and a support vector machine was constructed to identify the fault type of high-voltage circuit breakers with the maximum singular values of each submatrix. To obtain more promising decomposition results and enhance the adaptivity of the method, scholars have made improvements to VMD. Yi [ 103 ] optimized local extremum of individual particles and global extremum of group particles by using a particle swarm optimization algorithm to improve VMD. The results indicated that the method was much more robust to sampling and noise. Liu [ 104 ] improved VMD by using the correlation coefficient criterion to determine the number of mono-components adaptively. Zhu [ 105 ] employed kurtosis as an optimization index to determine the number of decomposition modes and data-fidelity constraint of VMD by using an artificial fish swarm algorithm. As VMD can decompose a multi-component signal into different mono-components, the algorithm can separate noise from signals. Some applications and researches were done to the de-noising issue by using VMD. Zhang [ 96 ] employed majoriation–minization-based total variation denoising to eliminate stochastic noise in the raw signal. An [ 106 ] took the approximate entropy of modes obtained by using VMD as evaluation parameter of the significance of the mode for the original signal, and the de-noising signal was constructed with modes with approximate entropies greater than threshold. The results showed that the method had better de-noising performance than WTs in terms of SNR, root mean square error and partial correlation index. Liu [ 107 ] presented a criterion based on detrended fluctuation analysis to select the mode number of VMD, aiming to avoid the impact of overbinning or underbinning on the VMD denoising. Yao [ 108 ] presented a noise source identification algorithm for diesel engines based on variational mode decomposition and robust independent component analysis. After the VMD of signals, the RobustICA algorithm was employed on the modes to extract the independent components. Furthermore, the continuous wavelet transform and the prior knowledge of diesel engines were applied to further identify the separated results. Table 2 is designed to make this section more readable. Table 2. Summary of works for VMD. Objects References Methodologies Precise filter operation speech signal Upadhyay et al. [ 84 ] VMD Upadhyay et al. [ 85 ] X-band open-ended rectangular waveguide Yin et al. [ 86 ] metal burn degrees Gao et al. [ 87 ] ship-radiated noise Li et al. [ 88 ] VMD + support vector machine Rough filter operation Bearings Lv et al. [ 92 ] VMD + multikernel support vector machine Liu et al. [ 94 ] VMD + singular value decomposition and standard fuzzy C means clustering Tang et al. [ 95 ] VMD + HT + ICA An et al. [ 98 ] VMD + nearest neighbor algorithm An et al. [ 100 ] VMD + K nearest neighbor algorithm Yang et al. [ 101 ] VMD + multiclass support vector machine Power signal Aneesh et al. [ 90 ] VMD + support vector machine Gear Muralidharan et al. [ 93 ] VMD + J48 decision tree algorithm Voltage circuit breaker Huang et al. [ 102 ] VMD + one-class support vector machine Improving VMD Bearings Yi et al. [ 103 ] VMD + particle swarm optimization Zhu et al. [ 105 ] VMD + artificial fish swarm Rotor system Liu et al. [ 94 ] VMD + correlation coefficient criterion De-noising Bearings Zhang et al. [ 96 ] VMD + majoriation–minization-based total variation Hydropower unit An et al. [ 106 ] VMD + approximate entropy Bumps, Blocks, Heavysine, Doppler and ECG Liu et al. [ 107 ] VMD + detrended fluctuation analysis Diesel engine Yao et al. [ 108 ] VMD + robust independent component analysis Open in a new tab 5. Vold–Kalman Filter Order Tracking VKF_OT can determine the slowly-varying envelope of tracked order components with known instantaneous frequencies [ 112 , 113 , 114 ]. The algorithm was first employed in vibration analysis of rotating machinery. In this paper, to explore the theoretical details of VKF_OT technique realization and parameter characteristics, we explicitly state the mathematical background of both the angular-velocity and angular-displacement VKF_OT techniques, according to Ref. [ 115 ]. The purpose of the method is to obtain the tracked order components by minimizing the energy of errors for both the structural and data equations by mean of one of the least squares approaches [ 116 ]. 5.1. Principle of Vold–Kalman Filter Order Tracking 5.1.1. The Angular-Velocity Vold–Kalman Filter Order Tracking The   Structural Equation For a second-order ordinary differential equation (ODE) d 2 f ( t ) d t 2 + ω 2 f ( t ) = 0 (41) The complementary solution is f ( t ) = K 1 e j ω t + K 2 e − j ω t (42) where K 1 and K 2 are arbitrary constants. The discrete form can be expressed as f ( t ) = K 1 e n j ω Δ t + K 2 e − n j ω Δ t (43) where t = n Δ T ; n = 1, 2, 3, ...; and Δ T denotes the sampling time spacing. Let d 1 = e j ω Δ T and d 2 = e − j ω Δ T ; respectively, then Equation (43) becomes f ( n ) = K 1 ( d 1 ) n + K 2 ( d 2 ) n (44) and the characteristic equation can be expressed as H ( n ) = ( D − d 1 ) ( D − d 2 ) (45) where the operator notation D denotes a discrete-time delay such as D f ( n ) = f ( n − 1 ) . The analyzed signal f ( n ) satisfies the following second-order difference equation, f ( n ) − 2 c o s ( ω Δ T ) f ( n − 1 ) + f ( n − 2 ) = 0 (46) where f ( n ) denotes the tracked order component, and ω is the radian frequency. Generally, a non-homogeneous term, ε ( n ) is introduced to represent the other not-concerned components. So the amplitude, frequency and phase change slightly. In addition, Equation (46) can be written as [ 115 ] f ( n ) − 2 c o s ( ω Δ T ) f ( n − 1 ) + f ( n − 2 ) = ε ( n ) (47) Equation (47) is called the structural equation of the angular-velocity VKF_OT. The   Data Equation An analyzed signal y ( n ) possesses a formality like [ 115 ] y ( n ) = f ( n ) + η ( n ) (48) where the component η ( n ) denotes other not-concerned components. Equation (48) represents the data equation of the angular-velocity VKF_OT. Computation   of the Tracked Order Component f Equation (47) expresses the tracked order component, and Equation (48) expresses the measured signal. It is assumed that the length of the measured signal y ( n ) is N , and the tracked order component f ( n ) is calculated with Equations (47) and (48), i.e., [ 1 − c 1 0 0 ⋯ 0 0 0 0 1 − c 1 0 ⋯ 0 0 0 0 0 1 − c 1 ⋯ 0 0 0 ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 0 0 ⋯ 1 − c 1 ] [ f ( 1 )   f ( 2 )   f ( 3 )   ⋮ f ( N )   ] = [ ε ( 1 )   ε ( 2 )   ε ( 3 )   ⋮ ε ( N )   ] (49) and [ y ( 1 )   y ( 2 )   y ( 3 )   ⋮ y ( N )   ] = [ f ( 1 )   f ( 2 )   f ( 3 )   ⋮ f ( N )   ] + [ η ( 1 )   η ( 2 )   η ( 3 )   ⋮ η ( N )   ] (50) where c = 2 con ( ω Δ T ) . Equations (49) and (50) can be symbolized, respectively, as A ↔ f ˜ = ε ˜ (51) and y ˜ = f ˜ + η ˜ (52) where the matrix A ↔ is a sparse matrix, and the dimension is ( N − 2 ) × N ; f ˜ denotes the tracked order component, and y ˜ represents the measured data; ε ˜ is the vector of the non-homogeneous term, and η ˜ is the vector of the not-concerned component. The norm square of the non-homogeneous vector is as follows ε ˜ T ε ˜ = f ˜ T A ↔ T A ↔ f ˜ (53) where the symbols T denote the transpose operations. Likewise, the norm square of the not-concerned vector can be expressed as η ˜ T η ˜ = ( y ˜ T − f ˜ T ) ( y ˜ − f ˜ ) (54) The least squares approach is employed to calculate the tracked order component. The calculation goal is to minimize the energy of errors for both the structural and data equations. A weighting factor r is used to tune the tracked order component f ˜ with desirable resolutions. A weighted combination forms by combining both the structural and data equations, J = r 2 ε ˜ T ε ˜ + η ˜ T η ˜ (55) is employed to evaluate f ˜ ; where r 2 ε ˜ T ε ˜ = f ˜ T A ↔ T A ↔ f ˜ : To make ∂ J / ∂ f ˜ = 0 , the calculation result is as follows, ( r 2 A ↔ T A ↔ + I ↔ ) f ˜ = y ˜ (56) The tracked order component y ˜ is calculated by using the LU decomposition method [ 117 ]. Every mono-component f ˜ depends on the corresponding instantaneous amplitude and instantaneous phase, and they are considered as local constants. In addition, enough time points are needed to compute the amplitude and phase. Supplement   to Amplitude and Phase of Tracked Order In Equation (47), it is assumed that the radian-frequency ( ω ) is a constant. The tracked order component f ˜ can be calculated with known IF. In a second-order, ODE is called the angular-velocity VKF_OT, and the tracking procedure is obtained in another way. The computed order component f ˜ is calculated by f ( n ) = [ cos ( ∑ m = 0 n ω ( m ) Δ T )   sin ( ∑ m = 0 n ω ( m ) Δ T ) ] [ a ( n ) b ( n ) ] (57) where the amplitude is a ( n ) 2 + b ( n ) 2 , and the phase tan − 1 ( a ( n ) b ( n ) ) . In the next subsection, another OT technique will be explained by using a different structural equation arising directly from the order waveform similar to Equation (57). 5.1.2. The Angular-Displacement Vold–Kalman Filter Order Tracking The k th-order component arising from the operation of a rotary machine can be expressed as f k ( t ) = a k ( t ) θ k ( t ) + a − k ( t ) θ − k ( t ) (58) where a k ( t ) represents the complex envelope, and a − k ( t ) is the complex conjugate of a k ( t ) to make f k ( t ) a real waveform. It is noted that θ k ( t ) is a carrier wave, and defined as θ k ( t ) = exp ( k i ∫ 0 t ω ( u ) d u ) (59) where d u is the speed of the reference axle, and ∫ 0 t ω ( u ) d u is the elapsed angular displacement. The discrete form of Equation (59) can then be expressed as θ k ( n ) = exp ( k i ∑ m = 0 n ω ( m ) Δ T ) (60) The Structural Equation To obtain the tracked order component f k ( t ) , the corresponding envelope a k ( t ) needs to be computed. It is assumed that a k ( t ) can be a relatively smooth polynomial with a low degree, and fulfills [ 114 ] d S a k ( t ) d t s = ψ k ( t ) (61) where ψ k ( t ) represents a higher-degree term in a k ( t ) . Likewise, the corresponding discrete forms is as follows ∇ S a k ( n ) = ψ k ( n ) (62) where ∇ denotes the difference operator; the index s denotes the differentiation order; ψ k ( n ) denotes a combination of other spectral components and additional measurement noise. The   Data Equation A measured signal y ( n ) is taken as a combination of several order/spectral components, f k ( t ) , and measurement noise [ 114 ] y ( n ) = ∑ k ∈ j a k ( n ) θ k ( n ) + ξ ( n ) (63) where the integral number j ( = ± 1 , ± 2 , ± 3 , … , and / or ± K ) denotes the order of spectral components to be tracked, ξ ( n ) represents unwanted spectral components and measurement errors. It is noted that each order/spectral component a k ( n ) of interest modulates with its corresponding carrier wave θ k ( n ) . Computation   of the Tracked Order Component f In Equation (62), let s = 2, and data length be N , then the matrix form can be expressed as [ − 2 1 0 0 0 ⋯ 0 0 1 − 2 1 0 0 ⋯ 0 0 0 1 − 2 1 0 ⋯ 0 0 ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 0 0 ⋯ − 2 1 ] [ a k ( 1 )   a k ( 2 )   a k ( 3 )   ⋮ a k ( N )   ] = [ ψ k ( 1 )   ψ k ( 2 )   ψ k ( 3 )   ⋮ ψ k ( N )   ] (64) To simultaneously track multiple orders and spectral components, e.g., resonance, Equation (63) can also be extended to all tracked order components. Let M ↔ = [ − 2 1 0 0 0 ⋯ 0 0 1 − 2 1 0 0 ⋯ 0 0 0 1 − 2 1 0 ⋯ 0 0 ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 0 0 ⋯ − 2 1 ] , A ↔ = [ a k ( 1 )   a k ( 2 )   a k ( 3 )   ⋮ a k ( N )   ]   and   Z ↔ = [ ψ ˜ k ( 1 )   ψ ˜ k ( 2 )   ψ ˜ k ( 3 )   ⋮ ψ ˜ k ( N )   ] and then Equation (64) becomes [ M ↔ 0 0 0 ⋯ 0 0 0 M ↔ 0 0 ⋯ 0 0 0 0 M ↔ 0 ⋯ 0 0 ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 0 ⋯ 0 M ↔ ] A ↔ = Z ↔ (65) where elements a ˜ k in the matrix A ↔ are column vectors with a length N , which denote the k th order component; ψ ˜ k represent error vectors with a dimension N × 1; and M is a matrix with a dimension N × N . The terms with negative indexes in Equation (63) assure f k ( t ) to be a real waveform. y ˜ denote the measured signal with a length of N , ξ an error vector with dimension N × 1; and B ↔ k consist of carrier signals, which is a diagonal matrix, as B ↔ k = [ θ k ( 1 ) 0 0 ⋯ 0 0 θ k ( 2 ) 0 ⋯ 0 0 0 θ k ( 3 ) ⋯ 0 ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 ⋯ θ k ( N ) ] A ↔ = Z ↔ (66) Thus, Equation (63) can be rewritten as y ˜ − [ B ˜ 1   B ˜ 2   B ˜ 3 … B ˜ k ] [ a ˜ 1   a ˜ 2 a ˜ 3   ⋮ a ˜ K   ] = ξ ˜ (67) As the angular-velocity VKF_OT scheme, a weighting factor is introduced, and combine Equations (64) and (67), and then [ 0   0 0     ⋮ 0 y ˜   ] − [ r M ↔ 0 0 ⋯ 0 0 0 r M ↔ 0 ⋯ 0 0 0 0 r M ↔ ⋯ 0 0 ⋮ ⋮ ⋮ ⋮ ⋮ ⋮ 0 0 0 ⋯ 0 r M ↔ B ˜ 1 B ˜ 2 B ˜ 3 … B ˜ k − 1 B ˜ k ] [ a ˜ 1   a ˜ 2 a ˜ 3   ⋮ a ˜ K − 1 a ˜ K   ] = [ r Z ↔ ξ ] (68) For the convenience of subsequent deviation, Equation (68) can be symbolized as Y ↔ − P ↔ A ↔ = E ↔ (69) The evaluation of tracked order components is exactly to find a vector A ↔ fulfilling min A ↔ ( ‖ E ↔ ‖ 2 ) = min A ↔ ( E ↔ H E ↔ ) = min A ↔ ( J ) (70) i.e., ∂ J / ∂ A ↔ = 0 . The vector A ↔ can be written as P ↔ H P ↔ A ↔ = P ↔ H Y ↔ (71) The matrix P ↔ H P ↔ is of the form P ↔ H P ↔ = [ S ↔ B ↔ 1 , 2 B ↔ 1 , 3 ⋯ B ↔ 1 , K B ↔ 2 , 1 S ↔ B ↔ 2 , 3 ⋯ B ↔ 2 , K B ↔ 3 , 1 B ↔ 1 , 2 S ↔ ⋯ B ↔ 3 , K ⋮ ⋮ ⋮ ⋱ ⋮ B ↔ K , 1 B ↔ K , 2 B ↔ K , 3 ⋯ S ↔ ] (72) where S ↔ = r 2 M ↔ T M ↔ + I ↔ , and B ↔ u , v = B u H B v , Moreover, P ↔ H is of the form P ↔ H = [ B ↔ ¯ 1   B ↔ ¯ 2   B ↔ ¯ 3 ⋯   B ↔ ¯ K ] T (73) where B ↔ ¯ K denotes the complex conjugate of B ↔ K . It should be noted that Equation (72) is positive definite, its inverse matrix exists and can be evaluated numerically. 5.2. Advantage of Vold–Kalman Filter Order Tracking As mentioned above, the different mono-components can be separated by using VKF_OT with known IF, even when the IFs cross in time-frequency panel, which cannot be done by methods deriving from EMD, EWT and VMD. We construct a sample f sig 8 (as shown in Figure 39 ) f s i g 8 = s 1 ( t ) + s 2 ( t ) s 1 ( t ) = t ⋅ sin [ ( 100 + 100 t ) × 2 π t ] , 0 ≤ t ≤ 1 s 2 ( t ) = t ⋅ sin [ ( 300 t ) × 2 π t ] , 0 ≤ t ≤ 1 (74) to illustrate it, and the sampling frequency is 2 kHz. As can be seen in Figure 40 , two components cross in time-frequency panel. Figure 41 a presents the decomposition result. To check the calculation accuracy of the VKF_OT, we calculate the error e k ( t ) by e k ( t ) = f k ( t ) − f k 0 ( t ) (75) where f k ( t ) denote the k th component of decomposition result ( k = 1 , 2 , … , k is the number of components obtained by using VKF_OT), and f 0 k ( t ) denote the k th component of original signal. As shown in Figure 41 b, the errors are small. Therefore, for the slowly-varying envelope of tracked order components with known instantaneous frequencies, a promising calculation result can be obtained by using VKF_OT. Figure 39. Open in a new tab The waveform of the sample signal f sig 8 . Figure 40. Open in a new tab The STFT of the sample signal f sig 8 : ( a ) 2D figure and ( b ) 3D figure. Figure 41. Open in a new tab The waveforms of decomposition result of f sig 8 by using VKF_OT and the corresponding calculation errors: ( a ) waveforms and ( b ) calculation errors. 5.3. Disadvantage of Vold–Kalman Filter Order Tracking We construct a sample signal f sig 9 (as shown in Figure 42 ) f s i g 8 = s 1 ( t ) + s 2 ( t ) + s 3 ( t ) s 1 = { 0 ,   0 ≤ t < 0.1 2 ( t − 0.1 ) sin { 800 ⋅ 2 π ⋅ t } ,   0.1 ≤ t < 0.6 − 2 ( t − 1.1 ) sin { 800 ⋅ 2 π ⋅ t } ,   0.6 ≤ t < 1.1 0 ,   1.1 ≤ t < 1.2 s 2 ( t ) = t ⋅ sin [ ( 600 t ) × 2 π t ] , 0 ≤ t ≤ 1.2 s 3 = sin [ 300 ⋅ 2 π ⋅ t + 100 sin ( 2 π t / 1.2 ) ] , 0 ≤ t ≤ 1.2 (76) to demonstrate the disadvantage of VKF_OT, and the sampling frequency is 2 kHz. In this paper, we employ STFT to obtain the IF of each component. On this basis, VKF_OT is adapted to compute components of the signal. The STFT of the sample signal f sig 9 is shown in Figure 43 . The IF errors of the sample signal f sig 9 is obtained from the corresponding STFTs, and is presented in Figure 44 . In addition, the calculation result of VKF_OT is presented in Figure 45 . As shown in Figure 45 , the computation error of components 1 and 2 are small, except for the signal ends marked by red rectangles in Figure 45 . However, the errors of component 3 are relatively greater, about 0.1, which may result from the error of IF. As shown in Figure 44 , the IF errors of component 3 are relatively greater, too. The IF of component 3 varies relatively quickly, so it is difficult to obtain IF with a high accuracy by using STFT. It can be deduced that a high computation accuracy of the component 3 can be obtained, if the IF difference between the calculation result and the true value is small. Moreover, as can be seen in Figure 44 , the IF errors of component 1 in [0 0.1] s and [1.1 1.2] s are great, as marked by red rectangles in Figure 44 , but the calculation errors in these time sections are also small. Therefore, this phenomenon reveals that the error of IF cannot result in calculation error when the values of the component are zero in the corresponding time sections. Figure 42. Open in a new tab The waveform of the sample signal f sig 9 . Figure 43. Open in a new tab The STFT of the sample signal f sig 9 : ( a ) 2D figure and ( b ) 3D figure. Figure 44. Open in a new tab The IF errors of the sample signal f sig 9 obtained from the corresponding STFTs. Figure 45. Open in a new tab The waveforms of decomposition result of f sig 9 by using VKF_OT and the corresponding calculation errors: ( a ) waveforms and ( b ) calculation errors. The analysis above indicates that precisely calculating IF is crucial for the calculation accuracy of VKF_OT. In this paper, STFT is adapted. Maybe, other time-frequency representation techniques can be tried to obtain the IF of component with a high accuracy. The selection of parameters such as the weighting factor and the correlation matrix of process noise, which influences tracking performance, are issues open to further research. 5.4. Application and Improvement Works of Vold–Kalman Filter Order Tracking As illustrated above, VKF_OT can single out mono-component-related signatures, so it is an effective tool for the analysis of measured dynamic signals. Scholars have done numerous investigations of application and improvement research for VKF_OT [ 113 , 114 , 115 , 116 , 117 , 118 , 119 , 120 , 121 , 122 , 123 , 124 , 125 , 126 , 127 , 128 , 129 , 130 , 131 , 132 , 133 , 134 , 135 ]. First, we summarize some theoretical researches of VKF_OT. Vold [ 114 ] proposed VKF_OT for the estimation of a single-order component. Afterwards, an improved version simultaneously estimating multiple orders was proposed [ 113 , 114 ]. Pan [ 115 , 116 ] further explored the theoretical details of the angular-velocity and angular-displacement VKF_OTs. However, these VKF_OT schemes must be computed off-line and implemented as post-processing techniques, resulting from determination of structural equations and data equations [ 113 , 114 ], which makes the unknown complex envelopes smooth and relates the tracked orders to the measured signal. These two equations should be evaluated within a huge inverse matrix with all observed time sequence data. The solution of Kalman filtering converges to the optimum Wiener solution in some statistical sense. It can be seen that embodying the structural and data equations of a linear, discrete-time dynamical system in the process and measurement equations translates the order-tracking problem into a state estimation task. Haykin [ 118 ] introduced a one-step prediction into Kalman filtering, overcoming this drawback of the original VKF_OT scheme, and made real-time processing feasible. In addition, Wu [ 119 ] employed the algorithm to undertake fault diagnosis of a gear set and damaged engine turbocharger wheel blades. In Ref. [ 119 ], sound emission signals served as an alternative reference signal to the fault diagnosis system. Pan [ 120 , 121 ] took this and improved the original angular-velocity and angular-displacement VKF_OTs [ 115 , 116 ], which enabled addressing of computation complexity, and allowed it to be considered in on-line and real-time applications. Pan [ 122 ] adopted the procedure of accumulative vectors and the concept that a measured signal could be represented as the superposition of order components to the original angular-velocity Vold–Kalman order tracking [ 115 , 116 ], and presented an extended angular-velocity VKF_OT. It is worth mentioning that Pan [ 128 ] built a remote online machine condition monitoring system in the architecture of both the Borland C++ Builder (BCB) software-developing environment and Internet transmission communication. Various signal-processing computation schemes such as time–frequency analysis and VKF_OT were implemented-based upon the Borland C++ Builder graphical user interface. To improve performance of the method for the dynamics analysis, other signal processing algorithms were employed to analyze testing signals together with VKF_OT. Wang [ 123 ] used EMD to preprocess raw signals, and then further decompose IMFs to separate speed synchronous and non-synchronous vibrations by using VKF_OT. Besides, to select a suitable bandwidth of VKF_OT in implementation of Vibration Monitoring of Electrical Machines, Wang [ 124 ] established a simplified simulation model of electrical rotating machinery, and a parameter was chosen based on two different damping ratios of the simulation model. Similar to Ref. [ 123 ], Guo [ 125 ] applied ICA to decouple the disturbance orders. Furthermore, the independent components were decomposed using VKF_OT. Feng [ 126 ] employed higher-order energy separation on mono-components obtained by VKF_OT to accurately estimate the IF because of its high adaptability to local signal changes. 6. Summary and Prospects Adaptive methods to analyze a signal are of great interest regarding finding sparse representation in the contest of compressive sensing. Employing a proper adaptive decomposition algorithm tends to successfully separate a multicomponent signal into different mono-components. Practical engineering problems can be roughly divided into two categories, precise filtering operations and rough filtering operations. The former requires that a single targeted mono-component should be accurately extracted form a raw signal, and it is ideal that there should be no loss of the targeted mono-component and no residual noise. The parameter identification of mechanical systems [ 136 , 137 ] and isolation of deferent wave packages in ultrasonic non-destructive testing [ 67 ] belong to this category. For the latter, highlighting specific characteristics of valuable components is the filter target, and the loss of the valuable component and the residual noise (invaluable components can be taken as noise in this paper) are tolerable, for example in fault diagnosis of rolling bearings [ 138 , 139 , 140 ]. The algorithms mentioned above can be taken as different filters, and have their respective applicable scopes, inapplicable scopes and further research issues, as summarized in Table 3 and Table 4 . In real applications, one should select an appropriate method according to the specific characteristics of the signal. Table 3. Comparison of various time–frequency analysis methods for the precise operation. It should be noticed that algorithms deriving from EMD that are referred in this paper include EEMD, CEEMD, CEEMDAN, and improved CEEMDAN, and the number of sifting iterations is set as 2000, and the stopping criterion threshold is set as 0.05. The conclusion about algorithms deriving from EMD is obtained under these conditions of parameter setting. Method Applicable Scope Inapplicable Scope Further Work Algorithms deriving from EMD The IFs of different component are in an enough distinction degree. The ratio is greater than 0.75. In addition, the ratio (low IF/high IF) is less than 0.5, which can obtain a promising decomposition. 1. Decrease computation intensity; 2. Improve decomposition stability. EWT Different components can be separated in Fourier spectrum. Different components overlap in Fourier spectrum. 1. Develop detection strategy of boundary; 2. Suppress influence of the white noise. VMD Different components can be separated in Fourier spectrum. Different components overlap in Fourier spectrum. 1. Develop the specific sparsity property; 2. Suppress influence of the white noise; 3. Select parameters such as number of decomposition modes and data-fidelity constraint. VKF_OT The component IF is available. The component IF is unknown. In addition, the calculation accuracy of mono-component depends on the precision of corresponding IF. 1. Obtain the IF in a high precision; 2. Select parameters such as the weighting factor and the correlation matrix of process noise. Open in a new tab Table 4. Comparison of various time–frequency analysis methods for the rough operation. It should be noticed that algorithms deriving from EMD that are referred in this paper include EEMD, CEEMD, CEEMDAN, and improved CEEMDAN, and the number of sifting iterations is set as 2000, and the stopping criterion threshold is set as 0.05. The conclusion about algorithms deriving from EMD is obtained under these conditions of parameter setting. Because algorithms deriving from EMD, EWT and VMD can work for most cases for the rough operation, we just list the further works for them. Method Further Work Algorithms deriving from EMD 1. Decrease computation intensity; 2. Improve decomposition stability; 3. Further remove color noise in conjunction with other decomposition methods; 4. Highlight the specific characteristics of the valuable component; 5. Identify interesting components. EWT 1. Develop detection strategy of boundary; 2. Further remove color noise in conjunction with other decomposition methods; 3. Highlight the specific characteristics of the valuable component; 4. Identify interesting components. VMD 1. Develop the specific sparsity property; 2. Select parameters such as number of decomposition modes and data-fidelity constraint; 3. Further remove color noise in conjunction with other decomposition methods; 4. Highlight the specific characteristics of the valuable component; 5. Identify interesting components. Open in a new tab 6.1. Algorithms Deriving from Empirical Mode Decomposition EMD decomposes multicomponent signals in sifting and iteration process. EEMD solves mode-mixing caused by intermittence signals. CEEMD can suppress the residue coming from adding white noise in the decomposition process. CEEMDAN and improved CEEMDAN can reduce computation amount. EEMD, CEEMD, CEEMDAN and improved CEEMDAN can work when the IFs of different mono-components are distinct enough at each time point. It is necessary for the ratio between a relatively low IF and a relatively high IF to be smaller than 0.75, and an ideal decomposition result can be obtained when the ratio is smaller than 0.5, for reasonable numbers of sifting iterations. For the precise filtering operation mentioned above, this necessary condition of the frequency resolution should be met; otherwise, a relatively large calculation error may be introduced. Further work can be done on decreasing computation intensity and improving decomposition stability. For the rough filtering operation mentioned above, algorithms deriving from EMD can work in most cases. If the conduction of frequency resolution is not met, although the loss of valuable components and residual noise may occur, noise can be removed to a certain degree by using algorithms deriving from EMD. Therefore they may work in this case. Moreover, algorithms deriving from EMD can be employed in conjunction with other decomposition methods such as wavelet transforms, principal component analysis and adaptive multiscale morphological analysis to further remove color noise and highlight the specific characteristics of the valuable component. In addition, identifying interesting components is also a research issue. Finally, further work can be done on decreasing computation intensity and improving decomposition stability. 6.2. Empirical Wavelet Transform EWT is a combination of WT and EMD. For precise filtering operations, the necessary premise is that valuable mono-components can be separated in the Fourier spectrum. However, considering the characteristics of the Fourier spectrum in different practical problems as shown in Section 3.3.2 , the filtering goal can be met under the condition that the boundaries of valuable mono-components can be obtained; therefore suitable strategies for boundary detection is crucial. Further research can be conducted with respect to this problem. White noise distributes in the entire Fourier spectrum. For a broadband mono-component, the negative effect from white noise cannot be neglected in some situations. In that case, a de-noising operation before or after EWT may be necessary. Further work may be done by finding effective de-noising methods for this problem. For the rough filter operation, EWT can work in most cases. Similar to the precise filter operation, correctly establishing the boundary of valuable components is decisive. Therefore, finding out the spectrum band corresponding to the valuable component in different practical problems is an open research issue. A specific effective boundary detection strategy can be taken as a significant contribution for a scientific problem. Finally, further removing color noise in conjunction with other decomposition methods, highlighting the specific characteristics of the valuable component, and identifying interesting components also are research issues for EWT. 6.3. Variational Mode Decomposition VMD decomposes a multicomponent signal into a series of sub-signals (mono-component) that have specific sparsity properties by assessing the bandwidth of a mono-component in an iteration process using an ADMM. For the precise filter operation, as shown in Section 4.2 , it is necessary that the different mono-components are well isolated in the frequency spectrum of the raw signal. Otherwise, decomposition cannot be successful. Further, the sparsity property employed in VMD is that the mono-component should be mostly compact around a center pulsation in the frequency spectrum. The widespread application of original VMD seems to suggest that this goal has fine applicability. However, in practical problems, this goal may not be universal in all cases. A suitable sparsity property should be defined for a specific problem. In addition, the frequency spectrum can be extended into time-frequency spectrums. Similar to EWT, the effluence of white noise should also be taken into consideration, as it is inevitable that each bandwidth of the frequency spectrum keeps part of the energy of white noise. Therefore, a de-noising operation before or after VMD may be necessary, if the white noise has a strength of energy that cannot be ignored. In addition, the selection of parameters such as number of decomposition modes and data-fidelity constraint, which influences tracking performance, is an issue open to future research. For rough filter operation, VMD can work in most cases. The goal of extracting the interesting component from the raw signal can also be obtained, when different mono-components overlap in the corresponding frequency spectrum useless components are inevitable. Similar to the precise filter operation, finding a suitable sparsity property in a specific practical problem and the selection of parameters are open issues. In addition, the selection of parameters is also an issue open to future research. Finally, further removing color noise in conjunction with other decomposition methods, highlighting the specific characteristics of the valuable components and identifying interesting components also are research issues for VMD. 6.4. Vold–Kalman Filter Order Tracking VKF_OT can decompose multicomponent signals into different mono-components with known corresponding IFs. Therefore, VKF_OT is more suitable for precise filter operation, compared with rough filter operation. Because the calculation accuracy depends on the accuracy of the IF, calculating the IF at a high accuracy is key for VKF_OT. We can employ time-frequency analysis techniques. However, when the IF changes quickly, it is difficult to obtain the IF precisely. Recently, some novel time-frequency representation techniques such as polynomial chirplet transform [ 141 , 142 ] and synchrosqueezing transform [ 143 ] have become available. These methods may be ideal choices to obtain the IF. Obtaining the IF to a high accuracy and the selection of parameters such as the weighting factor and the correlation matrix of process noise are issues open to future research. Acknowledgments The authors are grateful to N.E. Huang, Z. Wu, J.R. Yeh, M.E. Torres, M.A. Colominas, J. Gilles, K. Dragomiretskiy and M.V.D. Seijs for sharing their adaptive mode decomposition codes. Comments and suggestions from all reviewers are appreciated. Abbreviations EMD empirical mode decomposition EWT empirical wavelet transform VMD variational mode decomposition VKF_OT Vold-Kalman filter order tracking EEMD ensemble empirical mode decomposition CEEMD complementary ensemble empirical mode decomposition CEEMDAN complementary ensemble empirical mode decomposition with adaptive noises Improved CEEMDAN improved complementary ensemble empirical mode decomposition with adaptive noises AM amplitude modulated FM frequency modulated IF instantaneous frequency IA instantaneous amplitude HT Hilbert transform STFT short time Fourier transform IMF intrinsic mode functions SNR signal-noise ratio HHT Hilbert-Huang Transform ICA independent component analysis GEMD generalized empirical mode decomposition EED empirical envelope demodulation PEEMD partly ensemble EMD FT Fourier transform ADMM alternate direction method of multipliers Open in a new tab Author Contributions All authors discussed the paper; S.Y. conceived the project. T.L. wrote the manuscript, and analyzed and summarized the characteristics of the algorithms mentioned in the paper; Z.L. participated in the analysis and discussion of empirical wavelet transform and variational mode decomposition; J.H. participated in the analysis and discussion of Vold-Kalman filter order tracking. Funding This research was funded by Natural Science Foundation of Beijing Municipality grant number 3172017, National Natural Science Foundation of China grant number 11272171 and Education Ministry Doctoral Fund of China grant number 20120002110070. Conflicts of Interest The authors declare no conflicts of interest. References 1. Huang N.E., Shen Z., Long S.R., Wu M.C., Shih H.H., Zheng Q., Yen N.C., Tung C.C., Liu H.H. The empirical mode decomposition and the Hilbert spectrum for non-linear and non-stationary time series analysis. Proc. R. Soc. Lond. Ser. A Math. Phys. Eng. Sci. 1998;454:903–995. doi: 10.1098/rspa.1998.0193. [ DOI ] [ Google Scholar ] 2. Feng Z., Zhang D., Zuo M.J. Adaptive Mode Decomposition Methods and Their Applications in Signal Analysis for Machinery Fault Diagnosis: A Review with Examples. IEEE Access. 2017;5:24301–24331. doi: 10.1109/ACCESS.2017.2766232. [ DOI ] [ Google Scholar ] 3. Peng Z.K., Chu F.L. Application of the wavelet transform in machine condition monitoring and fault diagnostics: A review with bibliography. Mech. Syst. Signal Process. 2004;18:199–221. doi: 10.1016/S0888-3270(03)00075-X. [ DOI ] [ Google Scholar ] 4. Nguyen H.N., Kim J., Kim J.M. Optimal Sub-Band Analysis Based on the Envelope Power Spectrum for Effective Fault Detection in Bearing under Variable, Low Speeds. Sensors. 2018;18:1389. doi: 10.3390/s18051389. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 5. Baek W., Baek S., Kim D.Y. Characterization of System Status Signals for Multivariate Time Series Discretization Based on Frequency and Amplitude Variation. Sensors. 2018;18:154. doi: 10.3390/s18010154. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 6. Yan R., Gao R.X., Chen X. Wavelets for fault diagnosis of rotary machines: A review with applications. Signal Process. 2014;96:1–15. doi: 10.1016/j.sigpro.2013.04.015. [ DOI ] [ Google Scholar ] 7. Feng Z., Liang M., Chu F. Recent advances in time-frequency analysis methods for machinery fault diagnosis: A review with application examples. Mech. Syst. Signal Process. 2013;38:165–205. doi: 10.1016/j.ymssp.2013.01.017. [ DOI ] [ Google Scholar ] 8. Lei Y., Lin J., He Z., Zuo M.J. A review on empirical mode decomposition in fault diagnosis of rotating machinery. Mech. Syst. Signal Process. 2013;35:108–126. doi: 10.1016/j.ymssp.2012.09.015. [ DOI ] [ Google Scholar ] 9. Wei Y., Dong Y., Huang X., Zhang Z. Nonlinearity measurement for low-pressure encapsulated MEMS gyroscopes by transient response. Mech. Syst. Signal Process. 2018;100:534–549. doi: 10.1016/j.ymssp.2017.07.034. [ DOI ] [ Google Scholar ] 10. Hayashi T., Song W.J., Rose J.L. Guided wave dispersion curves for a bar with an arbitrary cross-section, a rod and rail example. Ultrasonics. 2004;41:175–183. doi: 10.1016/S0041-624X(03)00097-0. [ DOI ] [ PubMed ] [ Google Scholar ] 11. Rostami J., Chen J., Tse P.W. A Signal Processing Approach with a Smooth Empirical Mode Decomposition to Reveal Hidden Trace of Corrosion in Highly Contaminated Guided Wave Signals for Concrete-Covered Pipes. Sensors. 2017;17:302. doi: 10.3390/s17020302. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 12. Chudzikiewicz A., Bogacz R., Kostrzewski M. Using Acceleration Signals Recorded on a Railway Vehicle Wheelsets for Rail Track Condition Monitoring; Proceedings of the 7th European Workshop on Structural Health Monitoring (EWSHM); Nantes, France. 8–11 July 2014. [ Google Scholar ] 13. Chudzikiewicz A., Bogacz R., Kostrzewski M., Konowrocki R. Condition monitoring of railway track systems by using acceleration signals on wheelset axle-boxes. Transport. 2018;33:30–42. doi: 10.3846/16484142.2017.1342101. [ DOI ] [ Google Scholar ] 14. Garramiola F., del Olmo J., Poza J., Madina P., Almandoz G. Integral Sensor Fault Detection and Isolation for Railway Traction Drive. Sensors. 2018;18:1543. doi: 10.3390/s18051543. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 15. Rilling G., Flandrin P. One or two frequencies? The empirical mode decomposition answers. IEEE Trans. Signal Process. 2008;56:85–95. doi: 10.1109/TSP.2007.906771. [ DOI ] [ Google Scholar ] 16. Roy A., Doherty J.F. Empirical mode decomposition frequency resolution improvement using the pre-emphasis and de-emphasis method; Proceedings of the 42nd Annual Conference on IEEE Information Sciences and Systems; Princeton, NJ, USA. 19–21 March 2008; pp. 453–457. [ Google Scholar ] 17. Sun W., Peng Y., Yang Y. Method for improving frequency resolution of empirical mode decomposition. Comput. Eng. Appl. 2010;46:129–133. [ Google Scholar ] 18. Hu W., Mo J., Du M. Frequency-domain resolution and its improvement during empirical mode decomposition. J. South China Univ. Technol. 2007;35:15–19. [ Google Scholar ] 19. Wu Z., Huang N.E. Ensemble empirical mode decomposition: A noise-assisted data analysis method. Adv. Adapt. Data Anal. 2009;1:1–41. doi: 10.1142/S1793536909000047. [ DOI ] [ Google Scholar ] 20. Yeh J.R., Shieh J.S., Huang N.E. Complementary ensemble empirical mode decomposition: A novel noise enhanced data analysis method. Adv. Adapt. Data Anal. 2010;2:135–156. doi: 10.1142/S1793536910000422. [ DOI ] [ Google Scholar ] 21. Torres M.E., Colominas M.A., Schlotthauer G., Flandrin P. A complete ensemble empirical mode decomposition with adaptive noise; Proceedings of the IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP); Prague, Czech Republic. 22–27 May 2011; pp. 4144–4147. [ Google Scholar ] 22. Colominas M.A., Schlotthauer G., Torres M.E. Improved complete ensemble EMD: A suitable tool for biomedical signal processing. Biomed. Signal Process. Control. 2014;14:19–29. doi: 10.1016/j.bspc.2014.06.009. [ DOI ] [ Google Scholar ] 23. Xu Y., Zhang H. Recent mathematical developments on empirical mode decomposition. Adv. Adapt. Data Anal. 2009;1:681–702. doi: 10.1142/S1793536909000242. [ DOI ] [ Google Scholar ] 24. Flandrin P., Rilling G., Goncalves P. Empirical mode decomposition as a filter bank. IEEE Signal Process. Lett. 2004;11:112–114. doi: 10.1109/LSP.2003.821662. [ DOI ] [ Google Scholar ] 25. Flandrin P., Goncalves P. On empirical mode decomposition and its algorithms; Proceedings of the IEEE-EURASIP Workshop on Nonlinear Signal and Image Processing (NSIP-03); Grado, Italy. 8–11 June 2003; [(accessed on 26 June 2018)]. pp. 8–11. Available online: https://hal.inria.fr/inria-00570628/en/ [ Google Scholar ] 26. Yang J.N., Lei Y., Pan S., Huang N. System identification of linear structures based on Hilbert-Huang spectral analysis. Part 1: Normal modes. Earthq. Eng. Struct. Dyn. 2003;32:1443–1467. doi: 10.1002/eqe.287. [ DOI ] [ Google Scholar ] 27. Yang J.N., Lei Y., Pan S., Huang N. System identification of linear structures based on Hilbert–Huang spectral analysis. Part 2: Complex modes. Eartq. Eng. Struct. Dyn. 2003;32:1533–1554. doi: 10.1002/eqe.288. [ DOI ] [ Google Scholar ] 28. Khan I., Shan D., Li Q., Jie H. Continuous modal parameter identification of cable-stayed bridges based on a novel improved ensemble empirical mode decomposition. Struct. Infrastruct. Eng. 2018;14:177–191. doi: 10.1080/15732479.2017.1338734. [ DOI ] [ Google Scholar ] 29. Pai P.F., Palazotto A.N. Detection and identification of nonlinearities by amplitude and frequency modulation analysis. Mech. Syst. Signal Process. 2008;22:1107–1132. doi: 10.1016/j.ymssp.2007.11.006. [ DOI ] [ Google Scholar ] 30. Lee Y.S., Tsakirtzis S., Vakakis A.F., Bergman L.A., McFarland D.M. A time-domain nonlinear system identification method based on multiscale dynamic partitions. Meccanica. 2011;46:625–649. doi: 10.1007/s11012-010-9327-7. [ DOI ] [ Google Scholar ] 31. Eriten M., Kurt M., Luo G., McFarland D.M., Bergman L.A., Vakakis A.F. Nonlinear system identification of frictional effects in a beam with a bolted joint connection. Mech. Syst. Signal Process. 2013;39:245–264. doi: 10.1016/j.ymssp.2013.03.003. [ DOI ] [ Google Scholar ] 32. Goodarzi H., Sabzehparvar M. Identifying flight modes of Aerial Planting Projectile using Hilbert-Huang transformation. Mech. Syst. Signal Process. 2017;96:333–347. doi: 10.1016/j.ymssp.2017.04.012. [ DOI ] [ Google Scholar ] 33. Chen H., Kurt M., Lee Y.S., McFarland D.M., Bergman L.A., Vakakis A.F. Experimental system identification of the dynamics of a vibro-impact beam with a view towards structural health monitoring and damage detection. Mech. Syst. Signal Process. 2014;46:91–113. doi: 10.1016/j.ymssp.2013.12.014. [ DOI ] [ Google Scholar ] 34. Poon C.W., Chang C.C. Identification of nonlinear elastic structures using empirical mode decomposition and nonlinear normal modes. Smart Struct. Syst. 2007;3:423–437. doi: 10.12989/sss.2007.3.4.423. [ DOI ] [ Google Scholar ] 35. Pai P.F. Time-frequency characterization of nonlinear normal modes and challenges in nonlinearity identification of dynamical systems. Mech. Syst. Signal Process. 2011;25:2358–2374. doi: 10.1016/j.ymssp.2011.02.013. [ DOI ] [ Google Scholar ] 36. Bustos A., Rubio H., Castejón C., García-Prada J.C. EMD-Based Methodology for the Identification of a High-Speed Train Running in a Gear Operating State. Sensors. 2018;18:793. doi: 10.3390/s18030793. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 37. Van M., Kang H.J. Bearing-fault diagnosis using non-local means algorithm and empirical mode decomposition-based feature extraction and two-stage feature selection. Sci. Meas. Technol. 2015;9:671–680. doi: 10.1049/iet-smt.2014.0228. [ DOI ] [ Google Scholar ] 38. Wang Y., Lu C., Liu H., Wang Y. Fault diagnosis for centrifugal pumps based on complementary ensemble empirical mode decomposition, sample entropy and random forest; Proceedings of the IEEE Conference on Intelligent Control and Automation; Guilin, China. 12–15 June 2016; pp. 1317–1320. [ Google Scholar ] 39. Ali J.B., Fnaiech N., Saidi L., Chebel-Morello B., Fnaiech F. Application of empirical mode decomposition and artificial neural network for automatic bearing fault diagnosis based on vibration signals. Appl. Acoust. 2015;89:16–27. [ Google Scholar ] 40. Zhang X., Liang Y., Zhou J. A novel bearing fault diagnosis model integrated permutation entropy, ensemble empirical mode decomposition and optimized SVM. Measurement. 2015;69:164–179. doi: 10.1016/j.measurement.2015.03.017. [ DOI ] [ Google Scholar ] 41. Georgoulas G., Loutas T., Stylios C.D., Kostopoulos V. Bearing fault detection based on hybrid ensemble detector and empirical mode decomposition. Mech. Syst. Signal Process. 2013;41:510–525. doi: 10.1016/j.ymssp.2013.02.020. [ DOI ] [ Google Scholar ] 42. Georgoulas G., Tsoumas I.P., Antonino-Daviu J.A., Climente-Alarcón V., Stylios C.D., Mitronikas E.D., Safacas A.N. Automatic pattern identification based on the complex empirical mode decomposition of the startup current for the diagnosis of rotor asymmetries in asynchronous machines. IEEE Trans. Ind. Electron. 2014;61:4937–4946. doi: 10.1109/TIE.2013.2284143. [ DOI ] [ Google Scholar ] 43. Van M., Kang H.J., Shin K.S. Rolling element bearing fault diagnosis based on non-local means de-noising and empirical mode decomposition. IET Sci. Meas. Technol. 2014;8:571–578. doi: 10.1049/iet-smt.2014.0023. [ DOI ] [ Google Scholar ] 44. Yan R., Gao R.X. Hilbert-Huang Transform-Based Vibration Signal Analysis for Machine Health Monitoring. IEEE Trans. Instrum. Meas. 2006;55:2320–2329. doi: 10.1109/TIM.2006.887042. [ DOI ] [ Google Scholar ] 45. Zhao S.F., Liang L., Xu G.H., Wang J., Zhang W. Quantitative diagnosis of a spall-like fault of a rolling element bearing by empirical mode decomposition and the approximate entropy method. Mech. Syst. Signal Process. 2013;40:154–177. doi: 10.1016/j.ymssp.2013.04.006. [ DOI ] [ Google Scholar ] 46. Djebala A., Babouri M., Ouelaa N. Rolling bearing fault detection using a hybrid method based on Empirical Mode Decomposition and optimized wavelet multi-resolution analysis. Int. J. Adv. Manuf. Technol. 2015;79:2093–2105. doi: 10.1007/s00170-015-6984-7. [ DOI ] [ Google Scholar ] 47. Saidi L., Ali J.B., Fnaiech F. Bi-spectrum based-EMD applied to the non-stationary vibration signals for bearing faults diagnosis. ISA Trans. 2014;53:1650–1660. doi: 10.1016/j.isatra.2014.06.002. [ DOI ] [ PubMed ] [ Google Scholar ] 48. Le D.H., Cheng J., Yang Y., Tran T., Pham V. Gears Fault Diagnosis Method Using Ensemble Empirical Mode Decomposition Energy Entropy Assisted ACROA-RBF Neural Network. J. Comput. Theor. Nanosci. 2016;13:3222–3232. doi: 10.1166/jctn.2016.4979. [ DOI ] [ Google Scholar ] 49. Wang H., Li R., Tang G., Yuan H., Zhao Q., Cao X. A Compound fault diagnosis for rolling bearings method based on blind source separation and ensemble empirical mode decomposition. PLoS ONE. 2014;9:e109166. doi: 10.1371/journal.pone.0109166. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 50. Meng Z., Yan X.L. Fault Diagnosis Method for Rolling Bearing Based on Differential-based Empirical Mode Decomposition and Hidden Markov Model. Acta Metrol. Sin. 2015;36:482–486. [ Google Scholar ] 51. Zheng J., Cheng J., Yang Y. Generalized empirical mode decomposition and its applications to rolling element bearing fault diagnosis. Mech. Syst. Signal Process. 2013;40:136–153. doi: 10.1016/j.ymssp.2013.04.005. [ DOI ] [ Google Scholar ] 52. Feng Z., Zuo M.J., Hao R., Chu F., Lee J. Ensemble empirical mode decomposition-based Teager energy spectrum for bearing fault diagnosis. J. Vib. Acoust. 2013;135:031013. doi: 10.1115/1.4023814. [ DOI ] [ Google Scholar ] 53. Zheng J., Cheng J., Yang Y. Partly ensemble empirical mode decomposition: An improved noise-assisted method for eliminating mode mixing. Signal Process. 2014;96:362–374. doi: 10.1016/j.sigpro.2013.09.013. [ DOI ] [ Google Scholar ] 54. Jiang H., Li C., Li H. An improved EEMD with multiwavelet packet for rotating machinery multi-fault diagnosis. Mech. Syst. Signal Process. 2013;36:225–239. doi: 10.1016/j.ymssp.2012.12.010. [ DOI ] [ Google Scholar ] 55. Lei Y., Zuo M.J. Fault diagnosis of rotating machinery using an improved HHT based on EEMD and sensitive IMFs. Meas. Sci. Technol. 2009;20:125701. doi: 10.1088/0957-0233/20/12/125701. [ DOI ] [ Google Scholar ] 56. Zhang J., Yan R., Gao R.X., Feng Z. Performance enhancement of ensemble empirical mode decomposition. Mech. Syst. Signal Process. 2010;24:2104–2123. doi: 10.1016/j.ymssp.2010.03.003. [ DOI ] [ Google Scholar ] 57. Sharma R., Vignolo L., Schlotthauer G., Colominas M.A., Rufiner H.L., Prasanna S.R.M. Empirical Mode Decomposition for adaptive AM-FM analysis of Speech: A review. Speech Commun. 2017;88:39–64. doi: 10.1016/j.specom.2016.12.004. [ DOI ] [ Google Scholar ] 58. Feng Z., Liang M., Zhang Y., Hou S. Fault diagnosis for wind turbine planetary gearboxes via demodulation analysis based on ensemble empirical mode decomposition and energy separation. Renew. Energy. 2012;47:112–126. doi: 10.1016/j.renene.2012.04.019. [ DOI ] [ Google Scholar ] 59. Gilles J. Empirical wavelet transform. IEEE Trans. Signal Process. 2013;61:3999–4010. doi: 10.1109/TSP.2013.2265222. [ DOI ] [ Google Scholar ] 60. Pudn.com. [(accessed on 11 April 2015)]; Available online: http://www.pudn.com/Download/item/id/2735946.html . 61. Yuan M., Sadhu A., Liu K. Condition assessment of structure with tuned mass damper using empirical wavelet transform. J. Vib. Control. 2017 doi: 10.1177/1077546317736433. [ DOI ] [ Google Scholar ] 62. Hu J., Wang J. Short-term wind speed prediction using empirical wavelet transform and Gaussian process regression. Energy. 2015;93:1456–1466. doi: 10.1016/j.energy.2015.10.041. [ DOI ] [ Google Scholar ] 63. Reddy G.R.S., Rao R. Empirical Wavelet Transform Based Approach for Extraction of Fundamental Component and Estimation of Time-Varying Power Quality Indices in Power Quality Disturbances. Int. J. Signal Process. Image Process. Pattern Recognit. 2016;9:161–180. doi: 10.14257/ijsip.2016.9.11.15. [ DOI ] [ Google Scholar ] 64. Thirumala K., Umarikar A.C., Jain T. Estimation of single-phase and three-phase power-quality indices using empirical wavelet transform. IEEE Trans. Power Deliv. 2015;30:445–454. doi: 10.1109/TPWRD.2014.2355296. [ DOI ] [ Google Scholar ] 65. Thirumala K., Jain T., Umarikar A.C. Visualizing time-varying power quality indices using generalized empirical wavelet transform. Electr. Power Syst. Res. 2017;143:99–109. doi: 10.1016/j.epsr.2016.10.017. [ DOI ] [ Google Scholar ] 66. Li Y., Xue B., Hong H., Zhu X. Instantaneous pitch estimation based on empirical wavelet transform; Proceedings of the IEEE 19th International Conference on Digital Signal Processing (DSP); Hong Kong, China. 20–23 August 2014; pp. 250–253. [ Google Scholar ] 67. Liu T., Li J., Cai X., Yan S. A time-frequency analysis algorithm for ultrasonic waves generating from a debonding defect by using empirical wavelet transform. Appl. Acoust. 2018;131:16–27. doi: 10.1016/j.apacoust.2017.10.002. [ DOI ] [ Google Scholar ] 68. Gilles J., Heal K. A parameterless scale-space approach to find meaningful modes in histograms-Application to image and spectrum segmentation. Int. J. Wavel. Multiresolut. Inf. Process. 2014;12:1–17. doi: 10.1142/S0219691314500441. [ DOI ] [ Google Scholar ] 69. Zheng J., Pan H., Yang S., Cheng J. Adaptive parameterless empirical wavelet transform based time-frequency analysis method and its application to rotor rubbing fault diagnosis. Signal Process. 2017;130:305–314. doi: 10.1016/j.sigpro.2016.07.023. [ DOI ] [ Google Scholar ] 70. Jiang X., Li S., Cheng C., Li A. A new method ewt-based for rolling element bearing weak fault diagnosis; Proceedings of the ASME 2015 International Design Engineering Technical Conferences and Computers and Information in Engineering Conference; Boston, MA, USA. 2–5 August 2015. [ Google Scholar ] 71. Kedadouche M., Thomas M., Tahan A. A comparative study between Empirical Wavelet Transforms and Empirical Mode Decomposition Methods: Application to bearing defect diagnosis. Mech. Syst. Signal Process. 2016;81:88–107. doi: 10.1016/j.ymssp.2016.02.049. [ DOI ] [ Google Scholar ] 72. Kedadouche M., Liu Z., Vu V.H. A new approach based on OMA-empirical wavelet transforms for bearing fault diagnosis. Measurement. 2016;90:292–308. doi: 10.1016/j.measurement.2016.04.069. [ DOI ] [ Google Scholar ] 73. Chen J., Pan J., Li Z., Zi Y., Chen X. Generator bearing fault diagnosis for wind turbine via empirical wavelet transform using measured vibration signal. Renew. Energy. 2016;89:80–92. doi: 10.1016/j.renene.2015.12.010. [ DOI ] [ Google Scholar ] 74. Pan J., Chen J., Zi Y., Li Y., He Z. Mono-component feature extraction for mechanical fault diagnosis using modified empirical wavelet transform via data-driven adaptive Fourier spectrum segment. Mech. Syst. Signal Process. 2016;72:160–183. doi: 10.1016/j.ymssp.2015.10.017. [ DOI ] [ Google Scholar ] 75. Hu Y., Li F., Li H., Liu C. An enhanced empirical wavelet transform for noisy and non-stationary signal processing. Digit. Signal Process. 2017;60:220–229. doi: 10.1016/j.dsp.2016.09.012. [ DOI ] [ Google Scholar ] 76. Huang N., Chen H., Zhang S., Cai G., Li W., Xu D., Fang L. Mechanical fault diagnosis of high voltage circuit breakers based on wavelet time-frequency entropy and one-class support vector machine. Entropy. 2015;18:7. doi: 10.3390/e18010007. [ DOI ] [ Google Scholar ] 77. Merainani B., Benazzouz D., Bouamama B.O., Rahmoune C. Early fault diagnosis of gearbox using empirical wavelet transform and hilbert transform; Proceedings of the Mediterranean Conference on Control and Automation; Athens, Greece. 21–24 June 2016. [ Google Scholar ] 78. Merainani B., Benazzouz D., Rahmoune C. Early detection of tooth crack damage in gearbox using empirical wavelet transform combined by Hilbert transform. J. Vib. Control. 2017;23:1623–1634. doi: 10.1177/1077546315597820. [ DOI ] [ Google Scholar ] 79. Jiang Y., Zhu H., Li Z. A new compound faults detection method for rolling bearings based on empirical wavelet transform and chaotic oscillator. Chaos Solitons Fractals. 2016;89:8–19. doi: 10.1016/j.chaos.2015.09.007. [ DOI ] [ Google Scholar ] 80. Li Z., Zhu M., Chu F., Xiao Y. Mechanical fault diagnosis method based on empirical wavelet transform. Chin. J. Sci. Inst. 2014;35:2423–2432. [ Google Scholar ] 81. Li Z., Gao J. Random-noise attenuation by an amplitude-preserved time-frequency peak based on empirical wavelet transform predictive filtering; Proceedings of the SEG International Exposition and Annual Meeting; Dallas, TX, USA. 16–21 October 2016; Dallas, TX, USA: Technical Program Expanded Abstracts; Society of Exploration Geophysicists; 2016. pp. 4830–4834. [ Google Scholar ] 82. Thirumala K., Umarikar A.C., Jain T. A generalized empirical wavelet transform for classification of power quality disturbances; Proceedings of the International Conference on Power System Technology; Wollongong, Australia. 28 September–1 October 2016. [ Google Scholar ] 83. Dragomiretskiy K., Zosso D. Variational mode decomposition. IEEE Trans. Signal Process. 2014;62:531–544. doi: 10.1109/TSP.2013.2288675. [ DOI ] [ Google Scholar ] 84. Upadhyay A., Sharma M., Pachori R.B. Determination of instantaneous fundamental frequency of speech signals using variational mode decomposition. Comput. Electr. Eng. 2017;62:630–647. doi: 10.1016/j.compeleceng.2017.04.027. [ DOI ] [ Google Scholar ] 85. Upadhyay A., Pachori R.B. Instantaneous voiced/non-voiced detection in speech signals based on variational mode decomposition. J. Frankl. Inst. 2015;352:2679–2707. doi: 10.1016/j.jfranklin.2015.04.001. [ DOI ] [ Google Scholar ] 86. Yin A., Ren H. A propagating mode extraction algorithm for microwave waveguide using variational mode decomposition. Meas. Sci. Technol. 2015;26:095009. doi: 10.1088/0957-0233/26/9/095009. [ DOI ] [ Google Scholar ] 87. Gao Z., Wang X., Lin J., Liao Y. Online evaluation of metal burn degrees based on acoustic emission and variational mode decomposition. Measurement. 2017;103:302–310. doi: 10.1016/j.measurement.2017.02.049. [ DOI ] [ Google Scholar ] 88. Li Y., Li Y., Chen X., Yu J. A Novel Feature Extraction Method for Ship-Radiated Noise Based on Variational Mode Decomposition and Multi-Scale Permutation Entropy. Entropy. 2017;19:342. [ Google Scholar ] 89. Wang Y., Markert R., Xiang J., Zheng W. Research on variational mode decomposition and its application in detecting rub-impact fault of the rotor system. Mech. Syst. Signal Process. 2015;60:243–251. doi: 10.1016/j.ymssp.2015.02.020. [ DOI ] [ Google Scholar ] 90. Aneesh C., Kumar S., Hisham P.M., Soman K.P. Performance Comparison of Variational Mode Decomposition over Empirical Wavelet Transform for the Classification of Power Quality Disturbances Using Support Vector Machine. Procedia Comput. Sci. 2015;46:372–380. doi: 10.1016/j.procs.2015.02.033. [ DOI ] [ Google Scholar ] 91. Gupta K.K., Raju K.S. Bearing fault analysis using variational mode decomposition; Proceedings of the IEEE International Conference on Industrial and Information Systems; Gwalior, India. 15–17 December 2014; pp. 1–6. [ Google Scholar ] 92. Lv Z., Tang B., Zhou Y., Zhou C. A novel method for mechanical fault diagnosis based on variational mode decomposition and multikernel support vector machine. Shock Vib. 2016;2016:3196465. doi: 10.1155/2016/3196465. [ DOI ] [ Google Scholar ] 93. Muralidharan A., Sugumaran V., Soman K.P., Amarnath M. Fault diagnosis of helical gear box using variational mode decomposition and random forest algorithm. SDHM Struct. Durab. Health Monit. 2015;10:5–80. [ Google Scholar ] 94. Liu C., Wu Y., Zhen C. Rolling bearing fault diagnosis based on variational mode decomposition and fuzzy C means clustering. Zhongguo Dianji Gongcheng Xuebao/Proc. Chin. Soc. Electr. Eng. 2015;35:3358–3365. [ Google Scholar ] 95. Tang G., Luo G., Zhang W., Yang C., Wang H. Underdetermined blind source separation with variational mode decomposition for compound roller bearing fault signals. Sensors. 2016;16:897. doi: 10.3390/s16060897. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 96. Zhang S., Wang Y., He S., Jiang Z. Bearing fault diagnosis based on variational mode decomposition and total variation denoising. Meas. Sci. Technol. 2016;27:075101. doi: 10.1088/0957-0233/27/7/075101. [ DOI ] [ Google Scholar ] 97. Zhang M., Jiang Z., Feng K. Research on variational mode decomposition in rolling bearings fault diagnosis of the multistage centrifugal pump. Mech. Syst. Signal Process. 2017;93:460–493. doi: 10.1016/j.ymssp.2017.02.013. [ DOI ] [ Google Scholar ] 98. An X., Pan L. Bearing fault diagnosis of a wind turbine based on variational mode decomposition and permutation entropy. J. Risk Reliab. 2017;231:200–206. doi: 10.1177/1748006X17693492. [ DOI ] [ Google Scholar ] 99. An X., Zeng H., Li C. Envelope demodulation based on variational mode decomposition for gear fault diagnosis. ARCHIVE Proc. Inst. Mech. Eng. Part. 2017;231:864–870. doi: 10.1177/0954408916644271. [ DOI ] [ Google Scholar ] 100. An X., Tang Y. Application of variational mode decomposition energy distribution to bearing fault diagnosis in a wind turbine. Trans. Inst. Meas. Control. 2016;5:753–772. doi: 10.1177/0142331215626247. [ DOI ] [ Google Scholar ] 101. Yang Y., Jiang D. Casing vibration fault diagnosis based on variational mode decomposition, local linear embedding, and support vector machine. Shock Vib. 2017;2017 doi: 10.1155/2017/5963239. [ DOI ] [ Google Scholar ] 102. Huang N., Chen H., Cai G., Fang L., Wang Y. Mechanical fault diagnosis of high voltage circuit breakers based on variational mode decomposition and multi-layer classifier. Sensors. 2016;16:1887. doi: 10.3390/s16111887. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 103. Yi C., Lv Y., Dang Z. A fault diagnosis scheme for rolling bearing based on particle swarm optimization in variational mode decomposition. Shock Vib. 2016;2:1–10. doi: 10.1155/2016/9372691. [ DOI ] [ Google Scholar ] 104. Liu S., Tang G., Wang X., He Y. Time-frequency analysis based on improved variational mode decomposition and teager energy operator for rotor system fault diagnosis. Math. Probl. Eng. 2016 doi: 10.1155/2016/1713046. [ DOI ] [ Google Scholar ] 105. Zhu J., Wang C., Hu Z., Kong F., Liu X. Adaptive variational mode decomposition based on artificial fish swarm algorithm for fault diagnosis of rolling bearings. ARCHIVE Proc. Inst. Mech. Eng. Part. 2017;231:635–654. doi: 10.1177/0954406215623311. [ DOI ] [ Google Scholar ] 106. An X., Yang J. Denoising of hydropower unit vibration signal based on variational mode decomposition and approximate entropy. Trans. Inst. Meas. Control. 2015;38:282–292. doi: 10.1177/0142331215592064. [ DOI ] [ Google Scholar ] 107. Liu Y., Yang G., Li M., Yin H. Variational mode decomposition denoising combined the detrended fluctuation analysis. Signal Process. 2016;125:349–364. doi: 10.1016/j.sigpro.2016.02.011. [ DOI ] [ Google Scholar ] 108. Yao J., Xiang Y., Qian S., Wang S., Wu S. Noise source identification of diesel engine based on variational mode decomposition and robust independent component analysis. Appl. Acoust. 2017;116:184–194. doi: 10.1016/j.apacoust.2016.09.026. [ DOI ] [ Google Scholar ] 109. Yang W., Peng Z., Wei K., Shi P., Tian W. Superiorities of variational mode decomposition over empirical mode decomposition particularly in time-frequency feature extraction and wind turbine condition monitoring. IET Renew. Power Gener. 2017;11:443–452. doi: 10.1049/iet-rpg.2016.0088. [ DOI ] [ Google Scholar ] 110. Zhao L., Li C., Zhu Y., Chen X., Guo X., Gao Y. Power transformer partial discharge signal de-noising based on variational mode decomposition; Proceedings of the 2015 International Conference on Intelligent Systems Research and Mechatronics Engineering; Zhengzhou, China. 11–13 April 2015. [ Google Scholar ] 111. Lahmiri S., Boukadoum M. Physiological signal denoising with variational mode decomposition and weighted reconstruction after DWT thresholding; Proceedings of the IEEE International Symposium on Circuits and Systems; Lisbon, Portugal. 24–27 May 2015; pp. 806–809. [ Google Scholar ] 112. Vold H., Leuridan J. High Resolution Order Tracking at Extreme Slew Rate, Using Kalman Tracking Filters. SAE; Warrendale, PA, USA: 1993. SAE Paper No. 931288. [ Google Scholar ] 113. Vold H., Mains M., Blough J. Theoretical Foundation for High Performance Order Tracking with the Vold-Kalman Tracking Filter. SAE Paper No. 972007; SAE; Warrendale, PA, USA: 1997. [ Google Scholar ] 114. Vold H., Herlufsen H., Mains M., Corwin-Renner D. Multi axle order tracking with the Vold-Kalman tracking filter. Sound Vib. 1997;31:30–35. [ Google Scholar ] 115. Pan M.C., Lin Y.F. Further exploration of Vold–Kalman-filtering order tracking with shaft-speed information—I: Theoretical part, numerical implementation and parameter investigations. Mech. Syst. Signal Process. 2006;20:1134–1154. doi: 10.1016/j.ymssp.2005.01.005. [ DOI ] [ Google Scholar ] 116. Pan M.C., Lin Y.F. Further exploration of Vold-Kalman-filtering order tracking with shaft-speed information—II: Engineering applications. Mech. Syst. Signal Process. 2006;20:1410–1428. doi: 10.1016/j.ymssp.2005.01.007. [ DOI ] [ Google Scholar ] 117. Abbasbandy S., Ezzati R., Jafarian A. LU decomposition method for solving fuzzy system of linear equations. Appl. Math. Comput. 2006;172:633–643. doi: 10.1016/j.amc.2005.02.018. [ DOI ] [ Google Scholar ] 118. Haykin S. Adaptive Filter Theory. Prentice-Hall; New York, NY, USA: 1996. [ Google Scholar ] 119. Wu J.D., Huang C.W., Huang R. An application of a recursive Kalman filtering algorithm in rotating machinery fault diagnosis. Ndt E Int. 2004;37:411–419. doi: 10.1016/j.ndteint.2003.11.006. [ DOI ] [ Google Scholar ] 120. Pan M.C., Wu C.X. Adaptive Vold-Kalman filtering order tracking. Mech. Syst. Signal Process. 2007;21:2957–2969. doi: 10.1016/j.ymssp.2007.06.002. [ DOI ] [ Google Scholar ] 121. Pan M.C., Chu W.C., Le D.D. Adaptive angular-velocity Vold-Kalman filter order tracking—Theoretical basis, numerical implementation and parameter investigation. Mech. Syst. Signal Process. 2016;81:148–161. doi: 10.1016/j.ymssp.2016.03.013. [ DOI ] [ Google Scholar ] 122. Pan M.C., Wu C.X. Extended angular-velocity Vold-Kalman order tracking. J. Dyn. Syst. Meas. Control. 2010;132:031001. doi: 10.1115/1.4001326. [ DOI ] [ Google Scholar ] 123. Wang K.S., Heyns P.S. Application of computed order tracking, Vold-Kalman filtering and EMD in rotating machine vibration. Mech. Syst. Signal Process. 2011;25:416–430. doi: 10.1016/j.ymssp.2010.09.003. [ DOI ] [ Google Scholar ] 124. Wang K.S., Heyns P.S. Vold-kalman filter order tracking in vibration monitoring of electrical machines. J. Vib. Control. 2009;15:1325–1347. doi: 10.1177/1077546308094431. [ DOI ] [ Google Scholar ] 125. Guo Y., Tan K.K. High efficient crossing-order decoupling in Vold–Kalman filtering order tracking based on independent component analysis. Mech. Syst. Signal Process. 2010;24:1756–1766. doi: 10.1016/j.ymssp.2010.02.002. [ DOI ] [ Google Scholar ] 126. Feng Z., Qin S., Liang M. Time-frequency analysis based on Vold-Kalman filter and higher order energy separation for fault diagnosis of wind turbine planetary gearbox under nonstationary conditions. Renew. Energy. 2016;85:45–56. doi: 10.1016/j.renene.2015.06.041. [ DOI ] [ Google Scholar ] 127. Abadi M.K.B., Hajnayeb A., Hosseingholizadeh A., Ghasemloonia A. Single and multiple misfire detection in internal combustion engines using Vold-Kalman filter order-tracking; Proceedings of the SAE 2011 Noise and Vibration Conference and Exhibition; Grand Rapids, MI, USA. 16–19 May 2011. [ Google Scholar ] 128. Pan M.C., Li P.C., Cheng Y.R. Remote online machine condition monitoring system. Measurement. 2008;41:912–921. doi: 10.1016/j.measurement.2008.01.004. [ DOI ] [ Google Scholar ] 129. Wang K., Heyns P.S. A comparison between two conventional order tracking techniques in rotating machine diagnostics; Proceedings of the 2011 IEEE International Conference on Quality, Reliability, Risk, Maintenance, and Safety Engineering (ICQR2MSE); Xi’an, China. 17–19 June 2011; pp. 478–481. [ Google Scholar ] 130. Wang K.S., Guo D., Heyns P.S. The application of order tracking for vibration analysis of a varying speed rotor with a propagating transverse crack. Eng. Fail. Anal. 2012;21:91–101. doi: 10.1016/j.engfailanal.2011.11.020. [ DOI ] [ Google Scholar ] 131. Bai M., Huang J., Hong M., Su F. Fault diagnosis of rotating machinery using an intelligent order tracking system. J. Sound Vib. 2005;280:699–718. doi: 10.1016/j.jsv.2003.12.036. [ DOI ] [ Google Scholar ] 132. Urresty J.C., Riba J.R., Romeral L. Diagnosis of interturn faults in PMSMs operating under nonstationary conditions by applying order tracking filtering. IEEE Trans. Power Electron. 2013;28:507–515. doi: 10.1109/TPEL.2012.2198077. [ DOI ] [ Google Scholar ] 133. Hang J., Zhang J., Cheng M. Fault diagnosis of wind turbine using control loop current signals; Proceedings of the IEEE Conference on Energy Conversion Congress and Exposition; Pittsburgh, PA, USA. 14–18 September 2014; pp. 3119–3124. [ Google Scholar ] 134. Yeh T.C., Pan M.C. Online Real-Time Monitoring System through Using Adaptive Angular-Velocity VKF Order Tracking; Proceedings of the ASME 2012 International Design Engineering Technical Conferences and Computers and Information in Engineering Conference on American Society of Mechanical Engineers; Chicago, IL, USA. 12–15 August 2012; pp. 159–163. [ Google Scholar ] 135. Wu J.D., Bai M.R., Su F.C., Huang C.W. An expert system for the diagnosis of faults in rotating machinery using adaptive order-tracking algorithm. Expert Syst. Appl. 2009;36:5424–5431. doi: 10.1016/j.eswa.2008.06.059. [ DOI ] [ Google Scholar ] 136. Zhang Y., Lian J., Liu F. An improved filtering method based on EEMD and wavelet-threshold for modal parameter identification of hydraulic structure. Mech. Syst. Signal Process. 2016;68:316–329. doi: 10.1016/j.ymssp.2015.06.020. [ DOI ] [ Google Scholar ] 137. Ta M.N., Lardiès J. Identification of weak nonlinearities on damping and stiffness by the continuous wavelet transform. J. Sound Vib. 2006;293:16–37. doi: 10.1016/j.jsv.2005.09.021. [ DOI ] [ Google Scholar ] 138. Li Y., Liang X., Xu M., Huang W. Early fault feature extraction of rolling bearing based on ICD and tunable Q-factor wavelet transform. Mech. Syst. Signal Process. 2017;86:204–223. doi: 10.1016/j.ymssp.2016.10.013. [ DOI ] [ Google Scholar ] 139. Gao Z., Lin J., Wang X., Xu X. Bearing fault detection based on empirical wavelet transform and correlated kurtosis by acoustic emission. Materials. 2017;10:571. doi: 10.3390/ma10060571. [ DOI ] [ PMC free article ] [ PubMed ] [ Google Scholar ] 140. Li Y., Xu M., Liang X., Huang W. Application of bandwidth EMD and adaptive multiscale morphology analysis for incipient fault diagnosis of rolling bearings. IEEE Trans. Ind. Electron. 2017;64:6506–6517. doi: 10.1109/TIE.2017.2650873. [ DOI ] [ Google Scholar ] 141. Yang Y., Zhang W., Peng Z., Meng G. Multicomponent Signal Analysis Based on Polynomial Chirplet Transform. IEEE Trans. Ind. Electron. 2013;60:3948–3956. doi: 10.1109/TIE.2012.2206331. [ DOI ] [ Google Scholar ] 142. Peng Z., Meng G., Chu F.L., Lang Z.Q., Zhang W.M., Yang Y. Polynomial Chirplet Transform with Application to Instantaneous Frequency Estimation. IEEE Trans. Instrum. Meas. 2011;60:3222–3229. doi: 10.1109/TIM.2011.2124770. [ DOI ] [ Google Scholar ] 143. Feng Z., Chen X., Liang M. Iterative generalized synchrosqueezing transform for fault diagnosis of wind turbine planetary gearbox under nonstationary conditions. Mech. Syst. Signal Process. 2015;52:360–375. doi: 10.1016/j.ymssp.2014.07.009. [ DOI ] [ Google Scholar ] Articles from Sensors (Basel, Switzerland) are provided here courtesy of Multidisciplinary Digital Publishing Institute (MDPI)", "page_scrape_duration": "1.469874217s"}, {"result_index": 0, "engine": "Google", "title": "Comparison of empirical modal decomposition class ...", "link": "https://www.sciencedirect.com/science/article/abs/pii/S037877882300083X", "description": "by Y Li · 2023 · Cited by 17 — This paper provides a framework for a comprehensive comparison of EMD, Ensemble Empirical Mode Decomposition (EEMD), Variational Mode Decomposition (VMD) and ...", "source": "https://www.sciencedirect.com › article › abs › piihttps://www.sciencedirect.com › article › abs › pii", "main_content": "Energy and Buildings Volume 284 , 1 April 2023 , 112853 Comparison of empirical modal decomposition class techniques applied in noise cancellation for building heating consumption prediction based on time-frequency analysis Author links open overlay panel <PERSON><PERSON> , <PERSON><PERSON> , <PERSON><PERSON> Show more Add to Mendeley Share Cite https://doi.org/10.1016/j.enbuild.2023.112853 Get rights and content Abstract Empirical Modal Decomposition (EMD), and improved or modified techniques derived from EMD, collectively referred to as Empirical Modal Decomposition class (EMDC) techniques. EMDC techniques have a wide range of applications in building energy analysis , especially time–frequency analysis based noise cancellation in data-driven building energy prediction. However, there is a gap in the literature related to the choice of EMDC techniques in data-driven models. This paper provides a framework for a comprehensive comparison of EMD, Ensemble Empirical Mode Decomposition (EEMD), Variational Mode Decomposition (VMD) and Empirical Wavelet Transform (EWT) techniques for building heat consumption prediction modeling. A real building is used as an example to compare the noise cancellation potential of these techniques and the prediction accuracy under various data-driven models. The results demonstrated that noise cancellation using the EMDC techniques significantly improves Signal-Noise Ratio, regularity, and consistency with the original signal trend. The prediction models trained using the noise-cancelled data have the Root Mean Squared Error and the Mean Absolute Error reductions of 22.5 % and 31.3 % on average, respectively. Meanwhile, the predicted signals of the models inherit the noise cancellation benefits of the noise-cancelled training data. Introduction Heating energy consumption in China has grown significantly as a percentage of total energy consumption, driven by strong economic expansion and rapid urbanization. Although China uses less energy for heating than most developed economies do, it nevertheless accounts for more than 25% of all energy used in buildings [1]. In addition, it has been noted that heating energy consumption is continuously and quickly rising along with the growing need for a more comfortable indoor environment [2]. In order to significantly improve building heating energy efficiency and occupant comfort, effective building control and operation strategies are essential. High-fidelity building load forecasting and energy prediction models are utilized to achieve these strategies and can be used to simulate the real-time energy consumption of buildings. With the widespread use of building automation systems in China during the past decade, massive quantities of heating system data from sensors and other sources are continuously collected from buildings. These data of the heating systems offer an excellent chance to develop data-driven heating load prediction models, which frequently perform better than competitors in terms of real-time controllability, simplicity, and low engineering costs [3]. Data-driven modelling can be fully automated, which allows for faster and more widespread application than physics-based models, that require a great deal of domain expertise and a lengthy modelling period. However, the quality of the data has a significant impact on the accuracy of data-driven models [4]. The collected data signals, which are not usually smooth and regular in reality, may contain extremely complex physical processes and other types of disturbing information that is typically only understood qualitatively and is characterized as different categories of noise. When accompanied by input data that contains noise, the prediction models may also learn the noise features and convey them to the prediction results, distorting and making errors in the prediction. Therefore, noise cancellation is usually utilized as a pre-processing step before modelling. Time-frequency analysis can increase the stability, robustness and accuracy of the prediction model by locating the corresponding frequency of noise in the frequency domain to execute directional removal while reserving the primary features [5]. The Discrete Wavelet Transform (DWT) and Empirical Modal Decomposition (EMD) are the two techniques of time–frequency analysis that are most commonly applied in building energy consumption prediction models [4]. Numerous researches have verified the benefits of the DWT [6], [7], [8], however the criteria for choosing the initial basis function of wavelet are also very complicated [9]. In recent years, EMD has received a lot of attention as an option to the DWT&#x27;s defect. The fact that EMD has excellent adaptivity and does not require basis functions [9] gives it a significant advantage over DWT. Without pre-processing or manual settings, EMD can decompose a complex signal into several Intrinsic Mode Functions (IMFs) components, each of which describes the local characteristics of the signal. Building heating consumption signal is an example of a nonlinear and non-smooth natural signal sequence that EMD can analyze and process quickly and efficiently. However, there are some problems of EMD, e.g. modes mixing [10] and boundary effect [11]. When there are signals with different scales present in the same IMF or when there are signals with scales that are similar but present in different IMFs, this is referred to as modes mixing. The features of single can be lost in modes mixing, and the IMFs lose their original physical significance. While the boundary effect is referred to a distortion of the IMFs at the ends because there are no sample points to add to the decomposition before the beginning of the signal or after it ends. Improvements of EMD, including Ensemble Empirical Mode Decomposition (EEMD) [12], Complementary Ensemble Empirical Mode Decomposition (CEEMD) [13], Complete Ensemble Empirical Mode Decomposition with Adaptive Noise (CEEMDAN) [14], Improved Complete Ensemble EMD with Adaptive Noise (ICEEMDAN) [15], etc., have been proposed to solve the problems of EMD. Furthermore, some modifications have also been proposed that are based on the idea of EMD, such as Variational Mode Decomposition (VMD) [16], which gets IMFs by solving Variational Problem, and Empirical Wavelet Transform (EWT) [17], which combines wavelet transform with EMD. In this study, these algorithms are collectively referred to as Empirical Modal Decomposition Class (EMDC) algorithms for convenience. However, the most of the existing researches concentrate on using a single type of EMDC technique in the building energy modeling process, the authors not discuss the criteria used to choose it or evaluate how well it performs in comparison to other EMDC technologies. Besides, some new modifications and improvements of EMDC technologies, such as VMD and EWT, have been used for noise cancellation in electric power or renewable energy predicting [18], [19], [20], but for building heating consumption predicting, the performance and effectiveness of those technologies have not been fully proven. Therefore, this study plans to choose representative EMDC technologies for the comparison and analyze their noise cancelling optimization effect on building heating consumption models, and provide a guide for the selection of EMDC technologies for noise cancellation in building heating consumption predicting. Table 1 shows data-driven heating load/demand/consumption prediction models researches in recent years. Two evolving trends are discernible. The first is the transition from straightforward single models to elaborate hybrid models. Researchers suggest that hybrid approaches are effective methods for resolving the non-linear and non-stationary characteristics of building energy consumption because they overcome the inherent limitations of a single model [9].For example, Abinet Tesfaye Eseye and Matti Lehtonen [21] created a hybrid model using EMD, Imperialistic Competitive Algorithm (ICA), and Support Vector Machine (SVM) to predict a building&#x27;s short-term heating demand; Hongtao Wang [22] used a combinatorial Wavelet Back Propagation Neural Network (BPNN) to predict cooling load, thermal load, and electricity load; Xiaoyu Gao et al. [23] built a CEEMDAN-SVR hybrid model to predict the daily heat load of buildings with heat metering on the demand side in intelligent district heating system. According to their results, the hybrid models show a higher prediction accuracy. The second is the extensive use of machine learning and deep learning. Traditional machine learning algorithms including artificial neural networks (ANN) and SVM or Support Vector Regression (SVR) has been thoroughly studied in building heating consumption prediction. For example, Hessam Golmohamadi [24] used ANN to predict the energy consumption of residential buildings, and compared the results with Linear Regression Models (LRM), k-Nearest Neighbors (k-NN) to prove a higher prediction accuracy; Xavier Godinho et al. [25] used both ANN and SVR to predict heating and cooling energy demand in an office building. Deep learning is expected to promote neural network-based prediction models by providing higher levels of abstraction, better scalability, and automatic hierarchical feature learning [6]. For example, Jihoon Jang et al. [26] predicted building heat energy employing Long Short-Term Memory (LSTM), a type of deep learning, and obtained reliable results; Yiwei Xie et al. [27] applied LSTM on 1 h ahead load forecasting and coupled several models to improve prediction accuracy. Moreover, there hasn&#x27;t been much adjustment in the feature data sets chosen for the researches. The most frequently used features are historical energy data, weather factors, and occupancy. Table 2 shows EMDC applied for energy prediction in literatures. Two categories of applications can be summarized: (1) applying EMDC as preprocessing of data to remove noises before modelling; (2) merging EMDC into the models. The first application is the main topic of this paper as described in Section 1.1. For example, Jun Li et al. [20] used VMD to eliminate the effect of nonlinearity and volatility in the load sequence; Deepak Ranjan Dash et al. [28] employed EWT to eliminate the stochastic nature of the original solar power data. The second application is commonly to input each IMF into the model to guarantee that important features are extracted, as demonstrated by the EMD-SVR model created by Dandan Liu et al. [29] and the VMD-LSTM built by Yusheng Huang [30]. Additionally, the choice of various noise cancellation techniques and parameter settings can affect the model results. Liang Zhang et al [4] noticed that problem and compared DWT and EMD techniques in a study published in 2021. However, no relevant researches have compared EMDC techniques so far, although EMDC techniques have been used more frequently for noise cancellation in building energy prediction. There is a gap in the literature, especially related to building heating consumption predicting, to guide the selection and tuning of EMDC techniques. To fill the previously mentioned research gap, four representative EMDC technologies, including EMD, EEMD, VMD and EWT, are chosen according to Table 2 to compare and analyze the noise cancellation performance in building heating consumption predicting. Based on Table 1, the chosen prediction models for this comparison study are RF, SVR, BPNN, and LSTM, which are frequently used as analysis models for validating the effects of EMDC technologies. Furthermore, RF, SVR, BPNN, and LSTM are, respectively, the representative model of Tree-based Algorithms, Support Vector Machine, Neural Network, and Deep Learning, which are the most widely used data-driven types for predicting building energy [6]. The findings of this study are therefore universal and may be more useful to further researches. The comparison study is based on the heating consumption data of a real building. The noise cancellation effects and the prediction model accuracy improvements of chosen EMDC technologies are evaluated and compared. This study aims to explore the selection and tuning of EMDC techniques under various data-driven models for building heating consumption predicting. It also aims to draw conclusions to guide the future use of EMDC techniques for noise cancellation in building heating consumption predicting. A brief flowchart of this study is as shown in Fig. 1. This paper is organized as follows. Section 2 introduces the principles of EMD, EEMD, VMD and EWT. Section 3 introduces the framework for the comparison study. Section 4 introduces the results of comparison. Section 5 provides conclusions and guidance. Section 6 discusses limitations. Section snippets The principles of EMDCs Since some literature does not include detailed description of algorithms, that may confuse other researchers to understand the algorithm as well as the results, this paper elaborates the derivation principles of the algorithm and produces detailed flowcharts. Data set An office building in Inner Mongolia, China was selected as the case building for collecting data. According to Table. 1 in Section 1.2, the feature set  X  contains Solar Radiation Intensity (W/m 2 ), Ambient Temperature (℃), Relative Humidity (%), Wind Speed (m/s), Occupancy (%) and the average value of previous 6 time moments heating consumption (Historic Data, GJ). The target set Y contains only the First-order Forward Difference of heating consumption [58], i.e. the predict target is the Decomposition results Fig. 9 shows the results of decomposing the Y train using EMD, EEMD, VMD, and EWT. The decomposition results of EMD, EEMD and VMD include 8 IMFs, 9 IMFs and 15 IMFs, respectively. In addition, they both also each obtained one residual mode. The decomposition results of EWT includes 32 MRAs. The results of VMD and EWT are only partially shown in Fig. 9 due to space limitation. In Fig. 9, the IMFs and MRAs are arranged sequentially from high frequency to low frequency. It can be observed that the Conclusions EMD, and its improvement EEMD, modifications VMD and EWT, are the noise cancellation techniques of time–frequency analysis that are most commonly applied in data-driven building energy consumption prediction models. However, the effectiveness of these techniques have not been fully proven in predicting building heating consumption, and no relevant researches have made a comparison of these techniques. This study built a framework for a comparison of EMD, EEMD, VMD and EWT, and achieved a series Discussion First of all, the comparison study only used data from one building, which could bias the results. More buildings with various building types and locations will need to be tested in the future. Furthermore, the trend of the data used in this study is relatively steady, while the heating consumption shows upward or downward trend at the beginning and end of the heating season, the piecewise process is recommended. In addition, the data used in this study are 1-hour interval, and different Declaration of Competing Interest The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper. Acknowledgments This work was supported by the Program of National Science and Technology of China during the Thirteenth Five-year Plan (grant No. 2016YFC0700707). Recommended articles References (68) X.W. Li et al. Review of building energy modeling for control and operation Renew. Sustainable Energy Rev. (2014) M. Protic et al. Forecasting of consumers heat load in district heating systems using the support vector machine with a discrete wavelet transform algorithm Energy (2015) J.D. Zheng et al. Partly ensemble empirical mode decomposition: An improved noise-assisted method for eliminating mode mixing Signal Process. (2014) M.A. Colominas et al. Improved complete ensemble EMD: A suitable tool for biomedical signal processing Biomed. Signal Process Control (2014) Y.W. Xie et al. A hybrid short-term load forecasting model and its application in ground source heat pump with cooling storage system Renew. Energy (2020) D.R. Dash et al. Short term solar power forecasting using hybrid minimum variance expanded RVFLN and Sine-Cosine Levy Flight PSO algorithm Renew. Energy (2021) Y. Liu et al. Enhancing building energy efficiency using a random forest model: A hybrid prediction approach Energy Rep. (2021) M. Zekic-Susac et al. Predicting energy cost of public buildings by artificial neural networks, CART, and random forest Neurocomputing (2021) M. Kedadouche et al. A comparative study between Empirical Wavelet Transforms and Empirical Mode Decomposition Methods: Application to bearing defect diagnosis Mech. Syst. Sig. Process. (2016) Z. Qian et al. A review and discussion of decomposition-based hybrid models for wind energy forecasting applications Appl. Energy (2019) Q.R. Wang et al. Heating energy use in China: the current situation, challenges, and possibilities 9th International Conference on Future Environment and Energy (ICFEE), Osaka, Japan (2019) C. Chang et al. Using a novel method to obtain heating energy benchmarks in a cold region of China for the preparation of formulating incentive energy policies Sustainable Cities Soc. (2020) L. Zhang et al. Comparison of time-frequency-analysis techniques applied in building energy data noise cancellation for building load forecasting: A real-building case study Energ. Build. (2021) M. Mohammadi et al. Small-Scale Building Load Forecast based on Hybrid Forecast Engine Neural Process. Lett. (2018) L. Zhang et al. A review of machine learning in building load prediction Appl. Energy (2021) Y. Ding et al. Ultra-Short-Term Building Cooling Load Prediction Model Based on Feature Set Construction and Ensemble Machine Learning IEEE Access (2020) Y.M. Huang et al. Multivariate empirical mode decomposition based hybrid model for day-ahead peak load forecasting Energy (2022) G.M. Xu et al. Study on mode mixing problem of empirical mode decomposition Z.H. Nie et al. An EMD-SVR model for short-term prediction of ship motion using mirror symmetry and SVR algorithms to eliminate EMD boundary effect Ocean Eng. (2020) L. Tang et al. A novel CEEMD-based EELM ensemble learning paradigm for crude oil price forecasting Int. J. Inf. Technol. Decis. Mak. (2015) M.E. Torres et al. IEEE, A complete ensemble empirical mode decomposition with adaptive noise K. Dragomiretskiy et al. Variational mode decomposition IEEE Trans. Signal Process. (2014) J. Gilles Empirical wavelet transform IEEE Trans. Signal Process. (2013) J. Li et al. Short-term wind power combined prediction based on EWT-SMMKL methods Arch. Elect. Eng. (2021) Y.J. Ruan et al. A hybrid model for power consumption forecasting using VMD-based the long short-term memory neural network Front. Energy Res. (2022) J. Li et al. A wind power forecasting method based on optimized decomposition prediction and error correction Electr. Power Syst. Res. (2022) A.T. Eseye et al. IEEE, day-ahead prediction of building district heat demand for smart energy management and automation in decentralized energy systems H.T. Wang Typical building thermal and thermal load forecasting based on wavelet neural network X.Y. Gao et al. Forecasting the heat load of residential buildings with heat metering based on CEEMDAN-SVR Energies (2020) H. Golmohamadi Data-driven approach to forecast heat consumption of buildings with high-priority weather data Buildings (2022) X. Godinho et al. A data-driven approach to forecasting heating and cooling energy demand in an office building as an alternative to multi-zone dynamic simulation Appl. Sci.-Basel (2021) J. Jang et al. Prediction of heating energy consumption with operation pattern variables for non-residential buildings using LSTM networks Energ. Buildings (2022) D.D. Liu, Q.Q. Yang, F. Yang, Ieee, Predicting Building Energy Consumption by Time Series Model Based on Machine... Y.S. Huang et al. A new crude oil price forecasting model based on variational mode decomposition Knowl.-Based Syst. (2021) View more references Cited by (16) A novel prediction model for integrated district energy system based on secondary decomposition and artificial rabbits optimization 2024, Energy and Buildings Show abstract Energy predictions for buildings are the basis for energy efficiency and the implementation of smart technologies to cope with operational and energy planning issues in buildings, playing a crucial role in the implementation of environmental protection measures. Despite numerous methods proposed in current research to forecast energy, dealing with seasonal and non-linear data, particularly heat loads, presents significant volatility, resulting in less precise and poorly fitted predictions. This study introduces an artificial rabbits optimization architecture based on secondary decomposition to provide a solution for the prediction of heat loads. Leveraging secondary decomposition proves effective in discerning data trends and seasonality while simplifying the original data, thereby boosting prediction accuracy. Intelligent optimization is added for neural network parameter optimization and the trained model is used to predict the individual decomposed data to improve the fitness between the data and the model. Extensive assessments show that the proposed framework excels with an R 2 of 98.87% and outperforms other models, achieving the highest 6.11% accuracy boost. Accurate prediction of building heat loads is necessary for the energy transition in the construction industry, driving the development of new technologies in building technology and accelerating the transition to clean and renewable energy. A hybrid model based on multivariate fast iterative filtering and long short-term memory for ultra-short-term cooling load prediction 2024, Energy and Buildings Citation Excerpt : Notably, the full potential and applicability of the direct approach remain relatively uncharted territory in the literature, with significantly fewer instances compared to their multi-component approach counterparts. Further, decomposition is applied to training and testing data separately to prevent information leakage from test data to train data, which is a common problem in decomposition-based hybrid models [38,47]. The prediction model aims to provide a precise cooling load on an ultra-short-term basis, like 15 min ahead, which is necessary for automating the dynamic control of the chiller system. Show abstract The current ultra-short-term cooling load forecasting models have not given due attention to the data pre-processing stage. In this paper, multivariate signal decomposition methods MEMD and MvFIF are used in the preprocessing phase to replace the complex signal with simpler subcomponents. The resulting increase in the number of features is tackled through a dimensionality reduction technique, PCA. Finally, prediction is done using two rigorous machine learning algorithms – LSTM and XGBoost. By combining these algorithms at different stages, four hybrid algorithms are formed - MEMD-PCA-LSTM, MEMD-PCA-XGBoost and MvFIF-PCA-LSTM, and MvFIF-PCA-XGBoost. Following a thorough performance comparison, this paper proposes MvFIF-PCA-LSTM for the prediction of ultra-short-term cooling loads. Additionally, experiments are performed to compare the running time of the proposed model, to endorse the importance of using PCA in the proposed model, and to evaluate the choice of parameters that undergo feature reduction. Compared to the base LSTM model assayed on the same datasets, the proposed model offered an improvement of 24.94%, 33.65%, and 23.82% in R2 values for SIT@Dover, SIT@NYP, and simulated datasets, respectively. MAPE achieved by the proposed model is exceptionally low, measuring at 1.13% for the SIT@Dover dataset, 1.42% for the SIT@NYP dataset, and a mere 0.36% for the simulated dataset. The best values of performance metrics computed for the proposed model demonstrate its accuracy in ultra-short-term cooling load prediction. A Welch-EWT-SVD time–frequency feature extraction model for deformation monitoring data 2023, Measurement: Journal of the International Measurement Confederation Citation Excerpt : Gill [27] proposed an alternative multi-component signal decomposition approach called the Empirical Wavelet Transform (EWT) to solve the drawbacks of EMD. The EWT combines advantages of wavelet transform and EMD, making it widely studied and applied in time–frequency feature extraction of signals [28,29]. However, EWT suffers from significant over-decomposition issues in the presence of strong noise interference. Show abstract It is a considerable challenge to accurately extract time–frequency features from non-linear and non-stationary deformation monitoring data. In this contribution, a time–frequency feature extraction model that integrates the Welch algorithm, empirical wavelet transform (EWT), and singular value decomposition (SVD) (Welch-EWT-SVD) is proposed. To avoid the impact of unreasonable spectral segmentation, the signal is decomposed at multiple levels based on the Welch power spectrum. The decomposed signal is then processed by EWT to obtain Intrinsic Mode Functions (IMFs). Finally, SVD is adopted to further denoise the IMFs to finely extract time–frequency features. Simulation tests and a field test at the Great Wall are conducted to validate the proposed method. The results demonstrate that Welch-EWT-SVD outperforms EWT, EMD, and Welch-EWT methods in terms of accuracy for time–frequency feature extraction. According to the simulation results, the relative error rate of its extracted frequency is kept within a maximum of 0.10%. Time-sensitive cancellation refund in advance booking: Effect of online-to-offline marketing policy 2023, Computers and Industrial Engineering Citation Excerpt : They could then dynamically determine the optimal availability ofpre-paid rooms, with the goal of maximising both online and offline revenue. In addition, importance of O2O and its implication in customers’ behavior, pricing, revenue and coordination have been highlighted by Taleizadeh and Mokhtarzadeh (2020), Jin et al. (2021), Swoboda and Winters (2021a & Swoboda and Winters, 2021b), Cui et al. (2022), Kim et al. (2022), Li et al. (2013) and Hu et al. (2023). One of the common reservation methods in tourism is advance booking (Nicolau and Masiero, (2017)). Show abstract Due to heightened competition, current selling approaches are rapidly advancing as firms aim to enhance customer satisfaction to increase their market share and extend their customers’ zone. As an efficient advertising method, the online-to-offline (O2O) marketing strategy is extensively used by most of the competing vendors who tend to sell their commodities via advertisement in both virtual and traditional stores. By doing it this way, they can reach both online and offline-oriented customers, who prefer to buy the required commodities from online stores and real shopping centres respectively. In this research, we study the behaviour of two complementary firms in the airline and hotel industry using an advance booking policy. Here, it is supposed that cancellation is allowed and customers can enjoy a partial refund paid by the firms selling their products via both online and offline stores. The principal goal of this study is to investigate the behaviour of the partners of firms with/without an O2O strategy where the firms use partial refund for cancelled orders under time-sensitive cancellation rates. The optimal selling prices of commodities in advance and in a spot selling period are the decision variables, whose values are examined by numerical examples and are assessed using sensitivity analyses. A novel hybrid model for building heat load forecasting based on multivariate Empirical modal decomposition 2023, Building and Environment Citation Excerpt : However, the research trend in recent years is transiting from straightforward single models to elaborate hybrid models because hybrid approaches are effective methods for resolving the non-linear and non-stationary characteristics of building energy consumption and overcoming the inherent limitations of a single model [5]. Coupling EMD class algorithms with the original model is one of the main approaches to build hybrid models [23]. Table 1 shows the researches on hybrid models for building energy forecasting based on EMD class algorithms in recent years. Show abstract Accurate heat load forecasting is crucial for the high precise real-time operational control of buildings in winter. The inconsistency of frequencies between features and heat load, however, constrains the improvement of the ultra-short-term forecasting accuracy of heat load. This study proposed a novel hybrid model built upon Multivariate Empirical Mode Decomposition (MEMD) and Support Vector Regression (SVR) with hyper-parameters optimized by Particle Swarm Optimization (PSO), which is able to improve forecasting accuracy significantly. Meanwhile, Sliding Window (SW) is employed to overcome the limitations of MEMD in forecasting, and feature selection is carried out using eXtreme Gradient Boosting (XGBoost) before modeling to minimize errors and reduce the workload. The principle of the proposed SW-MEMD-PSO-SVR hybrid model is to decompose the associated features and heat load into several groups of components by MEMD, maintaining a constant frequency of feature components and heat load components for each forecasting. A real building in Inner Mongolia, China, have been considered as an example to verify the superiority of the proposed hybrid model. The proposed SW-MEMD-PSO-SVR hybrid model has the best values of MAPE, NMBE, CVRMSE and R 2 , being 2.68%, 0.09%, 3.52%, and 84.90%, respectively. The results demonstrated that the proposed hybrid model is a promising alternative for improving accuracy of ultra-short-term building heat load forecasting. Symplectic period mode decomposition method and its application in fault diagnosis of rolling bearing 2024, JVC/Journal of Vibration and Control View all citing articles on Scopus View full text © 2023 Elsevier B.V. All rights reserved.", "page_scrape_duration": "2.050655192s"}, {"result_index": 0, "engine": "Google", "title": "Superiorities of variational mode decomposition over ...", "link": "https://ietresearch.onlinelibrary.wiley.com/doi/10.1049/iet-rpg.2016.0088", "description": "by W Yang · 2017 · Cited by 108 — However, the comparison shows that the VMD is still superior to the EMD because the EMD generates unidentified components while the VMD does not ...", "source": "https://ietresearch.onlinelibrary.wiley.com › doi › iet-rpg...https://ietresearch.onlinelibrary.wiley.com › doi › iet-rpg...", "main_content": "You have to enable JavaScript in your browser's settings in order to use the eReader.\n\nOr try downloading the content offline\n\nReader environment loaded\n\nLoading publication (6.1 MB)\n\nLarge documents might take a while\n\n", "page_scrape_duration": "2.388116439s"}, {"result_index": 0, "engine": "Google", "title": "Variational Mode Decomposition", "link": "https://ww3.math.ucla.edu/camreport/cam13-22.pdf", "description": "Abstract—In the late nineties, <PERSON> introduced the Hilbert-. <PERSON> transform, also known as Empirical Mode Decomposition. The goal is to recursively ...12 pages", "source": "https://ww3.math.ucla.edu › cam13-22https://ww3.math.ucla.edu › cam13-22", "main_content": "%PDF-1.5 %���� 14 0 obj > stream x�� [\u0003} �݂�\u0012'L� ��j�>�\u0005\u0001 0un�Uw��> \u001a�2���SV3��ޡ��)��.����YM\u0010 �\u0014�U�c6����$�\u0000F��wK�@���R��f9��5�� 4A�\u0016v#��,�X�ui_9{��=\b9�\u000eF�J�B����\u0002����%\u001c\"F��\u0013�����\u0007�]�����$E�0�\u001f\u0018��ty� |�O��-\u0019�|����\u001b�����|N*��ē4�\u0002X{\"\u0007%����\u0011\u0010o\u001e M��\u0017�\u000b\u00158.7���\u001fK⒛��a���\u0007P\u001aF���i3�,8[����+�\u0003(drxX\u0012H$K����M��\u0005\u0010��wg��#��0�ˈ�\u0019.����/D�\u0013�\u001f��@L H�f�=J�1>�$@E$���\u00187\u0002�L�\u0016َ{mS��% *��\u0015\u0018S�뭬\u0017+ߋ��������F@Z�7�ӑs\u000f g�� ^��\u0017��=iC \u00136|\\uB \u001eA)1\u0011Į\u0014�\u0000�\u001b��k9�z�,\u001a�nθ2�ᜒ+���ŤN�[�\u0017b�\u001b9!�&8���N�-t���\u0019�>ٻ5\u0010�\u0002\u0003D�+i5� ��K�5�\u0011ؕ�F�M8ְ�b\u001e�0|���\u0011J���\u0007�c�hZ 9�`�[\u0005\u0012ӡ�\u0004�ْ\u0005\u001ci΂\u001f��R��2_7�Z�%\u0017n\u0005/d����؊���d� �\u0010g��\"��l_�86\u0015� \"� z�`i/\u0013� h�@��t�&�M~w�J���F���]3�G ���\u001a��8\u000b��&\u001b]2�H�#pd�X�W�v�؟�|l��P &Z�S�9�W���%��� �\u000e���%=� (��:i�('�q��aC�a⊢��~�~��./�{ht����g i�\u0010)\u0018�n\u0018���Qn\u0010JR�\u0012\u0016E\u000eZ�9��Ӑ�\u0004>ADf��\u0012�o����r�\u0005gP;�\b�\u0015����OGm.��m^�,i�hW\u0003�W*r�X�4J�\u0019�m�\u0016���l#�\u0005�O^�����b����\u0014t@e�PN��\u0003'��\u0004h��\u001eM�����\\�Qo�@* 18\u001d\u0010��a�kG\bkͱL�\u000e���%��\u001d7\u0016ا�\bn�O�\b$�!I=LǊ#�'�F���[\u0004\b���\b >�XI��k�Q�@aiW5��`\u000b\\��j�����\u0013G�a��\u0002\u001d�>h\u0000IfY�v.㯷6�9��`�Z�Z��\u0003��TB�fn��Z��f\u001cL����v�;� �'\u000b���?} �� d�g�\u001b \u0000����Ieu՞��oXd|P��U�V9\u0006����]؂R;>Y\u0011b'4�� \u0004��.]2\u0012���t`���1YCh]p�I�:�'Y\u000fx\u0016��\u0011F��\u0007F�\u0005|�0\u001a�B6��C�!�\u0001-8���,�i���s���f �I-/\u0018b�m~�]��l�p\u0019�ܠj����` !�|���Z ���\u0002 e\u0014��D� ����Kf��\u0006�\u001d�ʃ�ڍ�=��Y �^�¶�q �}\u000e�ٷD\u0018x\u001c]!�U�\u0002K7\u001bh\u0007j����ۋ`\u0002�Α�� \u001a���(���嚅�5\u000b\u0004^�� �d���~�\u001bi��7ل�@l-�D!�\u001b9�8\u0014ڀ�//�\u0006`# \u0007-\u0017Ɂ�%�ml����g/��D�g�O~y�\u0006�[�E\u0012��Q�\b,��\u0017�ݓ�7�b\u0003(\u0018���x�@ wx/\u0017\u0007P*\u0016���W�:2���z*\\D�5���L\u0013E:\u0012\u0012\u0006���h�>�*\u0002\u0005�v`U�F*�@Y�c�� �B/���m��\u001e\u001b~���0�(@�+GG\u0006�O�8��4-���.�t9��0�E��/Q�y �v&\u0015$�wO_�#�� ��zǅ$PA\u0010\b������K[�K�ը�w�ϗ3w�Sn��Ƚ�j\u0013X_�\u0012��������V�+ccl��\u0013����`����E�f������\u0003gG{ʍ�@\u0006I�F�����Kl�\u0007K#��U2�t�\u0011a�A�m�Nn�R\u0001���{VC�(\u0002���r�\"vv��z��u�n���\\�M����l\u000f\u001182�\u0005C�Un{�\u000fMwf�yc3��:��\u001a~\u000fF�#\u001e�a�P J봶����VJ��o����\u0012[����{&���jLz\u001aE9JҀ��� �\"]�e\u0010�\u0018���\u0019��� �4�=a��y��\u0005�\u001eo\u000b��u%�~q#�uF��\bm�\u000b�8۠.�&\u0002�o���=\u001e�g�пat9�x��-z ]ӖYC n�� � �l��0��pN,�U5\u0017h��l\u0019�mF� \u001c`\u0015E\u0003\u001f�_N3P�\u001e9L������ʦ\"q�\u0007�5B�I����ȹ�o�R2oN\u001ay����\u0007Ɗ4��9.��\u001cCk�\u0011\u0002�[2j���,m�!��t�HcoA��ra�\u0003�Qb/\u000es���z��% m\u0007��\u0005���� 6A�,)��\u000fb! k��X�M˷�K���L/���Ė}\u0005n\u0003�\u0001+c].�o���2\u0007�\u0000!C��\u0013�J\u0002$�\u0011я\"4Y\b�\u001cE#�:�\u0002)�Y�k|JsO�D%v\u0011 �\u001a\u000b��\u0004�O ?��oS�W:���d\b�����$q��,�0��\u0017�\u001arpœ�U�w�/�\u00164KCI�ɓЋ�\\�z;N]*/$/�&�� \\^Q\u0014�\u001e���?�xb\u0014\u0010� \u000b��{s:����dR?�7���V�Px�T\u001cV�u�\"�d�5�kI\u001erBQyj�m��b���V��\u0000���L9��+v.\u0010:��\b��\u0007paڬ��{5�3]��\u0018�\b\u0011��4e��X��I;o��9p#\u000f� a�o\u0003>�Ҙ^n\u0002c����g&5n\u001c�!^{ @�h��\u0019��:�iosu)��\u001a8ku�6\u0012W�S#ze9'�(���!�9\"�qC%�ۛ�\u0013h���M�\u0001��n��'>\u001e g��s�4���s�G�9���:\u001a�9 ��]\u000e\u0004�\u0014�`��oW��i\u0013�\u001c�b�\\(>i!�\u000f�U�\u0006\u0013��8\u0002\u0013��(��jo��>n���(=�-&��FCn��\u0010\u000b��,(��d�\u0013\u001bBI��?MG���)\"v+*�Ci�Ys�S1�\u001b!�IT����ɱ\u001b��\u0001u\u000eT�\u0019n>`%s$\u0006\u001d�ǣF*��3g7s��xw8,��O�\u0018LF�\u0011��뛼���dos�ʼ�\u001df�\u0010\u0002��y�M\u001a5�1�&���g���\u00004�ʄ�\u0017'\u0014\b��$�I���D��?��L�@��m\u001a$�}6�`^��1����H��:ͱ\u001b\u000ec��S�߭�\u0013_��\u001b\b\u001c0�\u0011�����T� ��!��߾�)�\u0013�O\u001d���\u000fq�]\u000fT�]\u000f��h~�\u001c\u0005��u�Ww�\u000e�� \u000b��:�2�zQ��DM��!�Ç\u0013������'���#���\u0015�h��B\u0003E��B�eEiq�?����a��\u00063�Ã�\u001bB(��C���ǒ1�\u0002��}\u000e��%_��P\u001e�÷��QmW�`�xOٴu��\u0004,�\u0010O\"��xP\u00182�T;�8��_(\u0019p�1�Η\u0001P�_�\u0016\u0000�>2�I�q.�.�0�/�H ���L� {��o������c� q��9���o\u000f>�\u0003\u001f\u001cЩ���\u0014�\u001aQ�4��b\u0001ޅ������\u000f���o��p��\u0015��O�K�\\\u001e��\"Ot�Xm��\u001d��ܓ\u0006���P\u000e�^�!\u0016|�%\u0017E��ߣ\u0019�=���\u001dW�\u00169O��B��c\u0001y�����\u0004A�}�������*y�w��Lp\u0004\u0006\u0015u7�+�n���3�Cq+ly�\u001d�i.�\u001f���!\u0017�O �~oX\u000f\"\u001fE\u001f�)����?��`��m�|�d$�otkfr7��b��Z�\u001f��-P���\u001d|��������\u00186j~ �\b:%�\u0013�\u001d������_��v��N����y�k�\u0003�oND\u0000#i�یM � Hf �}Gƍb�!!�&Z��j��U�\u0011xM�f.��� %\u001aI��S�g�O�\u0017�a\u0001g endstream endobj 30 0 obj > stream x��[M��F���W����& \u0014P���\u001c4��鉱4;�\u0018���\u0001$�M�@�\u0006@�ۿ~�ef\u0001 J\u000e�7����D����e\u0016�/\u001e\u0017���\u0017����4[�^�����a\u0011�^f�Ebc/���~��qin>��ma�����\u0017�ݫW�nV�����|���˛�_~s���;��}#�ww߽� ���ߥ�����7�޽�{�ݭ\u0010�Ŵ�{���R߼U��\u0004&[*��\u001f~���߿��^��\u001b�|u���\u0017\u00015�E��\u0003τ�\"N�\u0017��b����>X|ۼ�� W��^x�f�͝R�e��'�\u0014Ǿ\u0017G�\u001cH��o\u0003Y����ٕ�u^I;��J �\u0010'vrB6��4]����,խ��N��-�� �$��(�e��\u0013z��O(�UL\u000fh�pB/M���9QrbH\u0017���2+\\�\u001e�'�~,���,&l?���Y���ɚf\u0005�z�o>#/K�5��g����\u0012����*�\u0013��\u000bb����#��提R�%��\u0003�\u0014�61E����/1���L�\u0005Y���\\\u0013�)[�%1\u0005�'��o��f��E\u0015�^�%��^Ί*�왠�>\u0017�� ��+��gN�䃟э���\u0011e����Nh�\u0014T�gehI� i�ֳQ�k/\u001f)ҙ(^\u0006�L��I\u0014$�h��]>��N�U�/�b+�5�����;���E[�J��\u0010�cW\b!��* /Ѧ�%\u001aRIk����x�\u0012�NgT�d��3�pZ��$���\u000b��Z��R�\u0002�X��\u0002�I\u0016+��8\u0019�\u0004.B\u0013�)\u000fv��?~���/\u001b/���ۛU�$�M�az37!)m0qf!�.��% >IX\u00052�P�%�4Qzj\u0006'3��\u0005&]L�~�\u0001�:\u0015�\u0013>� S>��g\u00123fp\\��+\u0001�4���5 .��S�Q\u0004��U��\u001c���ϫ� /\u0015�d\u001b'˳��a\u0015\u0004���s� 33d�SQ8\u000fq�mX>����w��\u00174���&d^\u0010���\"�8�س�`���Q6E��8��\u0010E)��| �$T62\u0015��\u0005���\u0003bK�{��\u001cj\u0012;�\u001a��_B�*��*�c}c�屗�6?�[�?���Ǣ��\u0004v�,�������2�˦������c���K��\u0004Ѹ���H�V f}\u0012��\u001a\u001a*�����Z��+�G�mʪ\u001a�C\\.���7�~]���\u0018 w=]\\ �r�c\u0001e)�i�i�+�ܞ��\u0013�Z�qa0�i�M\u0005|�y\u0011A�\u0013�}[\u001c�z[֏��F4 O�C4�M��B\u001b���0��\u0004ˢ\u0004S�l�\u000b5�7;}�/�}'�}�,����iיU��m�eM�\u0019N7N�/��q�$g|���\u001d�������9���?\u001fć��!���Yv2~h\u001b��O喭\u0005#��\u0012�#�ͣ\u0004��,�[y>� G\u0012�D�*�zi\u0015���|�t�?JOZ�\u000e�xd��b��j�P�\u0007��anIp�e��|\u0000���?4�t�\u0007�R�ñ=4t\u001c�?\u0010=�O\u00182�L>����L\u0012��6\u0005B�I\"������c'C�� O,�\\\u001e��BW^U���'\u0004��-�\u00050���>���G\u001e���-b��W�+Q��655\u0010���6�㥧\u001c��*7%)`�U��o�E%Ǖ�{ɳ����t�c\u0010}����~WrH ����5\u001c�͒�PU �-���� �\u0004��I��F@í�B�؈΂\u0005:\u000br.��X \u001a\u001d\u000e��g�E\u001f��sW>�Vg\b\u0007sm\u001a\u0002A5 ��=\u0002M\")\u0017\u0014\u0001XC��>VnY\u0001@�!w��[�9\u0015'g���[g\u001aA\"�-\u0000*؁�f>�$m�\u0013�������&\u0001\u0014\u00191A�\u0018�\u0016�\u0015\u000e��B \b�\u0016\u0004�'\u000b\u001c \u0001�h��+\\͹�n�4��L�,j��~�9\u0014����8�sy`��k�f���!\u001f���2�X�|fv�W�MK���1��e)6|\u0016jG��ʝ�k�(&\u0003\u0017Y+\u0017�Cr�,>�G��AX9�j'��|\u001d�F��_����\u001e�����eݔ]qՋ�� ��H��\u001a �H2)�g�p+�a�`���&Wһ����d^�m]�&�fǮ6���\u000e^ ��8��\u0016\u0012�$U\u001b�K��\u0018�\u000f��d��1��D�i��]�Sq�#*��b5w���g6J��O�Z\\�'�ҕj�\u0015\u0019�\u0013�E@���\u0006�\b;\u0011��k�癩a����*\u0014@Kd�)�\u00004s���\u0004�u�\u001c�\u0014��B�ʕ;��T!��B�\u0017�Ņb?�_\u0011\u0012VG\u001ee���e���G:=�C^wt�{OH/\u001fo��J8Q�`o�\u0019Hb������-�\u0016N\u000ff���Ә�d .�`��B�u-��A:�k�\u0011 ��HV��\u0017��!\u0000�?�r�\u0005��A\"\u0011vr�U�닄e��v��ka,�\u0002�\"N +FZ��\u001e�k9��^gf�\u0016 Î7Kݳ�kGq\u0004�. ��U��\u0019�L|JA�1\u0005�t!�\u0015w�\u0019�\b��� p�2�f���&\u001c�h#*�\u0004�\u0004�9o@\u001c�z�Ѓ0{�\u0018�4�\u0003�\u0001�tS\"� \u001d\u0010j \u000f>�C�W���\\\u0012~�@��\"re�PvO����i�T[��\u0004~�c�S ��Z�\u0018v��f�q(e�\u0014f� �\u0013i�#��z�\b�r�\u000b\u0005'!\u001e\u0006�o�I\u0014k�B�ﭰV�F�\u001b:�i� �N�k#Ѭzv������P̔3H �KB�翐;R�� �Z�0$��l���\b�yI� �3n�\u0016!�\u0011�y12A��J����\u0010�@^�p�z\u000e��\u0012��\u001e��u� )X\"�c� & X\u00109\u0017G5E��%�^\u00159L��m��ϫ�4�Y�T�f\u001a���� �i�\u0017N��i`�,�@�|C�De��W��G3c�\u0010�$߮v�F\u0002J��z�\u0015-�|�Q�\u0012|\u0004͋�g��\u0011�\u001bN�\u0015�Q�,�2�E��K��S���h��\\k.��u���k��Gݭ�T�hn\u001b�\u000fԪ\u001b�%(�7V3�W�\u0005i˚�)\u001e��j\"ضn����� \u001b��\u0013���@����T���i�\u000byv3\u001d惡�P�P \u0001P?ZF\u001a�}����x� �y��m \bl\u000f�\u0015O \u0006�Xi�s�T2��ӥ��r\u0012.9z�j�{�Z�H�\u000e�Ql\u000b��ZRa\u001a\u001a=8\u0018ă��n���l���e\u00046��\u0004>n��P�\"E�S* l�w��t��0 Y�:*1��늮s\u0017\u001f�y4w�T��\\�aX,4u�7�3I�R���%X�\\�`Ai��\u0005�sHc\"b��RW\u0000f\u0015\u0007=Ft�2�Մ�>w�\u0012\u0006����H\u0018\u0004 !AGZ���\b��\u0017��|�m]\u0006(Γ�ctF���\u0017Z�'C�G\u001c���� YE�@B��� *A\u0019 �ZD���U\bs�rՊ��-j ��P:�K��ݱ&\u0012J����\u0001K_]&!��EUj𿘁,�*� ��ox��\u001ap\u0018���0w9!��tZ��3��O\u0001���h�*�;��O�'Շ0v;���|\u000eJ����ީ��O���\u0007R���I�\u0003��x\"����\u0015�*h:�Tτ��?9љ�8��Y�d0�o���/�\u001e�Nq����DG�El�\u00136�\u001c\u0001 4.+�w\u0013��+!�� �\u0003h��q��6�K\\ P�-`\u0006T^bl�h|�}c{����L�\"s�ŏ�\u0013���T��f B��L���\u0019�\"��[��$ ��O�b ����m�G������]���P\u000f\\��Z��/�|v�1 o�m��;\u0019\u001a \u0016CC�7\\w�Ｎ\u0016\u001ae��\u001awg`N~�`��B/zI����|Է9\u0012������m�rh�����\u0001�,\u0003� endstream endobj 4 0 obj >/Pattern >/ExtGState >/Font > /ProcSet [ /PDF /Text ] >> /Length 9602 /Filter /FlateDecode >> stream xڭ�;�\u001d�q���\u00147�\u0003^���Ѐ%�\u0019�\u0005\u001cȊ,X\u000e$\u0003���}~�\u0018���҂V��%����t��T��)�?���7?T�Y^�U�{�W�����?�����~����G��������\u0010��?�YߺA�������9��O��+�џ�ٟ� �Q߻��^������G��\u001ea=����\u001b������^?�wܴ���A�=YU���\u001fr��kǻ�z:W֭Y�~����\u001f������g���\u000fcf{���3����1�����\u0019���Øx�\u0018\u0011����o}AQ�g3}���,�� \u0019�}{�Dtqkڣ{{�\u0019)W����߽��x��n�G\u0014\u001b���.�\u0011)�\u0011}�W-u~\u001c��~�]?�䇥��?\u0019a{��M�1\u001f����}}��;� e���_�e��y}�z{�����_���t������|�\u0000���?�~�\u000f�\u001f�����/?~1�\u000f#t��_�n�˹}�\u000bӯ���~��{y��N�\u0007|;�_���\u000f���\u001eni\u0003��\u0007|���a���}��\u001f\u001f������� ����/Y�q�}���A��{.��v\u0011�z�7�\u0010���e\u0018K��_� k�o��*Ĉ��2��}}� _=��e�w��2,3�_� �����w�!F�=��|� _=��e�w��2(8����B-z�Y���bȇ��?qm_���Eb��{�Ou�� Dζ�F\u000fg��O�s�\u0017�㣏�!1�^��K)?�3 \u0018Ϟ��]���ׯ\u001ao��y����G{��2�9��]Ӱ����.�Oq�|H�����՘�_�'���,��*]Kr�/{\u0015\u0019�������Q��C��{|�����\u0002��b\u001f�ķ���>b���~�����\u000fv��gq�8u;F�{r�������mu᤮w�sw��Y�z������2!�>Y���\u000e�% k�Z��n\u001d�eK�>�6�c���]�]@�:�\\!�E0S�zۘ�XMxk!�̸ט�P���QЁ?Y�1���4�r���h@�w�$��Z/U��|v�\u000b{=zk!���q�(S\u001eA�7j[!����^%n5O���^�������D`���� ��D�9-n~x3�)z��)\b����ڻ]����j��h��/\u0013�r��i�wI���Y �j\u0007���\u0019��\u0010�(��4ݢ�\u00015�;�q�XE�~�K���h���\u0015\u0000\u001e\u001b_a2�`��m��\"��e��w䥧V�M�\u001eeY1�s�� �P��\b�; 8�aP�\u0018��\u001e�=v\u0015��+i7TV��/�\u0000�e��x�g�q����7o(��!������묻 �����RmGf[�zC(W�-\u0010�����R\u000fs�� �,�dB44\u0012�e˽)R0�>i.#�[�E��\\^V�q\u0013����\u0003�gE����\u0016M��ʋǚ��z=5���S�m){�\u001a�-\u0017`�\u0010���\u0003�.8���~\u0006*-!�\"���.� Gqr�ό�\u0015X&��\u0015�/�#@�AJG�g�P��0�%�\u001c�D�\u0019�$٭7&�\u001b\u0011U�=���b!\u0007R Mr��]A��eH��h)\\eo��{�|Ȩ\u001c�)_�[ /OS�2\u001c�K�8jpNf���\u0013�B֩�\\�n)c�4��c�\u0004~�LDw\u001crH(�\u000fLw�U�k�y�i�z�\u0014*b\u0018�\u0013�\b]VH��ya]������*k���r��0|���$�\u0000�ni����Pv^ � ���\\\u0011�X�\u0017@�\u001c\u0000|Y;�1�\u0015=Gcz�\u0006���Rs��6,��O�u\b��Ks���\u0002\u0017v�%\u0003yzL\u0018���\u0004�i���f>H�\u001f8oy���\u0014\\ˮ ���r,��\u0000\u0018y�P�ZV��\u001a�E�+�2/�H��_��i s �o�:̳�X������8�[^��\u0003@,V�GH\u001d�V+?Q�h!��:\u0018��G�P�\"�\u0010ٲ$��\u000e�� D�\u0014*�X\u0016���\u0017?8\u001e�V�LG%5�����GQt,��Y\u001f�e0�L ��5�s�����\u0010)�?^!,;b��Z\u0014\u0003��\u0013Ov JO\u0007�,� a%T��� _saRQ�ۨMʨ*w�@>�\u0012�\u001cx��\u0018!��x6W�9��Uq_����\u00150��-_�\u000b\u0004���0�''��\u001dBy2� �\u0002��,츝 \u0011Q��\u0017� W�IYUVj��t�kNT\u000f۾��z\u001f_K��:��'�M\u001e\u000e��bm��w\u001c�*>�L�\u001cB��a��ٞ|�Z��\u001f��+\u001a�\u0013!\u001c\u0007&��\u0014*���_Y�M�e��F_*��e\u001a��'��3��H-\u0005[�v\u0014W\u0002�\u00170dJ\u0010�) �pt3l\u0018!C�NO��e�>�p���_%����F \u000b9 �ޭ\u0012a� ��B\u0014���}��\u0014ݳ%]��Z�z\u0001�@�*�%�$9|�^񐆇�\u001a��\u0018\u001a�\u0005\u000eJy�����N�S�Ȯh\u00055�8\u0001������ !z���R&׬i6Zo9�`������/� �Z F\u0016\u001e��t��\u0015\u0018�rt�#��jQ�uJh\u001d$+�EׄV���6�Z��^��\\�է\u0015qN \u0007x\u0015�\u0015 ��\"�\u0004�Y�N��\b=�\u001cH�4'�G�ҳ���\u001ca���FT�(�\u0013\u0016��\b���iW,k�*��)�$_���N��ĩ-\u000f����W�h\u0000��\u0003��� �B���)ؓBVC,��\u001d\u0004N=��q*lZB��\\�B�� 0} 9�v \u0018@V\"�c�ƛ|d>A�\u0002\u0002vG��WW�Z \u0015��\u0015�\u0013�>\u0003pI�:���P�\u000f�\u0003\u0007+@�b��۰��bs��\u0014������m^.�+à�,\\�\\ҥ �\u0018��yɑ��`@iճ\u0016��Lo 3�DW@�\u000b��b�܉_���L/��˫%\u0000�ʋ��F\u001e(!��i�\u001f�jK�\u0005n��/�Ƌη\u0007\u0010)��\u001fzӂ*\u0006\u0003\u0005\u0010v>]���\u001d}oϧ)V�fn���wd΋JH�,:��}���nɉ+�l��\u0018\u0011��]��j'�*�'�2�΂\u0006Ԉ\u0005�\u0018�L��u���\u0001% %�4�\u001c\u0003T\"\u000e���\\�\u0013%� \u0012 �uw�m��(�H��؜�5\"��\u0005��\u0011,�\u000e hx%P(z�H�XG�Wn#��*���d�\u0013jFq�L+|\b/��j���1�9Cu���w���f�\u0014V��X�nyG�_g\u001d�\u0013�0Bقջ���;�\u0015n���2z>{��I�\u0002RI��nZ�O!��ܪ�\u0000\u0018k(��6&be��0�Ӊ�\u000e\u0000�r���Z{$�#-QB��\u0014P�#k$�\u001ewҩ��\u0017\u0000P�%��B9��܂2b�/���uG�j|ǎ��� 5hW멦\u0007UH`��ȷ�`��\u001cyn�no}\u0006^K4)2�ТA�J\u0013�@��)�\u0013�\u0002�]E�\u0014B�1L�\b�QA��;\u0010���t�\u001cD���@���\u001eP��\u0018\u000e\u000e�\u000b\u001a)�˫�Z9w�d�G)����^�\u0014���v���W\u001f�\u000f���\u0005cI�+�7_\u0006Ջc69��\u0016\u0010�F �\u0004\u001d�Q�nP�\u001d)4Tט�\"� ��Y��V����v��� *&͔Ƀ\u0019p�Az�\u0001'��\u001c�(\u000b���\u000e e�\\@^ǵU�>��9���H!�jݾ���{*�,V�QV���0r�U��n-�V\u0006���1��\"�$XF�j���ej}!䄐�bL\u0004���H\u0005����%S�!�^Q\u0015dw��AP�\u0006K\u000b4�lt`[ҩV\u001c\u001a�\u0014W�\u0014�d �ʒ�T(�}�����y�ݞG�`�모��� ��\u0005\u0007�����#C!��\u0000�F�S\u0002��\u0001�1�&\u001c����=| �8�s �;:7\biS�\u001c)� \u0019��f$#��0\b��[ƣH��9��ͩ:�\u0007�ɨ\u0010(!��\u001e\u001a#�d�ꪵ=\u000f\u0012 :�:��[��(�Y�B��L!u�m��$�!��HR��\u0019��/\u001d-\"p%�N\u0002Nk\u0011T�7�\u001c�&\u001c1)SԽ%R\"G郖��a\u0003�\"��z�D\"� U�7(]˾vʌ����\u0016n \bq�.��zS��+GlD��*�?�˂+8P�)�S\u0016\u001c8��\u0011oF�\u0018\u0006�±m~��)2Z\u001d\u0004�h+ �\u0001\u0019)~��փ�[�#9�\u0010�\u000bעY�V��\u0019\u0007e\u0000�$J-������&H�8u �&TQ�� v� �tڠ��1 ���\u0002�k�j$���Z7G\u001fs\u0017bRH28��W\b�ɽ(ز?\"��T�qtu;��Pi�6\u0016�)�'�p�#�\u0016�J^���Z�b��G\u0006G]X��Հa�\u0005lC�:��\u000e�p0����@߀ۧh\u0010�`���\b!\u0014\u000fcm\u000eT� ����B\u0006���%...", "page_scrape_duration": "2.696074361s"}, {"result_index": 0, "engine": "Google", "title": "Comparison of Variational Mode Decomposition and ...", "link": "https://ieeexplore.ieee.org/document/9299321/", "description": "by OF Karaaslan · 2020 · Cited by 6 — For this purpose, it is proposed to use empirical mode decomposition and variational mode decomposition methods as a comparison. Initially, the conversion of ...", "source": "https://ieeexplore.ieee.org › documenthttps://ieeexplore.ieee.org › document", "main_content": "A not-for-profit organization, IEEE is the world's largest technical professional organization dedicated to advancing technology for the benefit of humanity. &copy; Copyright 2025 IEEE - All rights reserved. Use of this web site signifies your agreement to the terms and conditions.\n\n", "page_scrape_duration": "1.332480085s"}]}, {"query": "variational mode decomposition vs empirical mode decomposition", "papers": [{"title": "Variational mode decomposition", "description": "… We perform variational modes decomposition into three modes, without Lagrangian multipliers in order to remove the noise. The signal, and the three components estimated using VMD …", "citation_count": 8063, "publication_info": "<PERSON>, <PERSON> - IEEE transactions on signal …, 2013 - ieeexplore.ieee.org", "link": "https://ieeexplore.ieee.org/abstract/document/6655981/", "authors": [{"name": "K Dragomiretskiy", "link": "https://scholar.google.com/citations?user=_il8cIMAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=_il8cIMAAAAJ&engine=google_scholar_author&hl=en", "author_id": "_il8cIMAAAAJ"}, {"name": "<PERSON>", "link": "https://scholar.google.com/citations?user=I8p3r-8AAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=I8p3r-8AAAAJ&engine=google_scholar_author&hl=en", "author_id": "I8p3r-8AAAAJ"}], "year": null, "journal": null, "is_pdf": false}, {"title": "Empirical mode decomposition vs. variational mode decomposition on ECG signal processing: A comparative study", "description": "… Variational mode decomposition Variational mode decomposition [16] is a sequential process that decomposes the input signal into different amplitude and frequency modulated wave …", "citation_count": 48, "publication_info": "U Maji, S Pal - 2016 international conference on advances in …, 2016 - ieeexplore.ieee.org", "link": "https://ieeexplore.ieee.org/abstract/document/7732196/", "authors": [{"name": "<PERSON>", "link": "https://scholar.google.com/citations?user=suC-Ld4AAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=suC-Ld4AAAAJ&engine=google_scholar_author&hl=en", "author_id": "suC-Ld4AAAAJ"}, {"name": "S Pal", "link": "https://scholar.google.com/citations?user=ilDM0uMAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=ilDM0uMAAAAJ&engine=google_scholar_author&hl=en", "author_id": "ilDM0uMAAAAJ"}], "year": null, "journal": null, "is_pdf": false}, {"title": "Superiorities of variational mode decomposition over empirical mode decomposition particularly in time–frequency feature extraction and wind turbine condition …", "description": "… So far, only empirical mode decomposition (EMD) and its extension forms can extract intra-… Recently, an alternative TFA method, namely variational mode decomposition (VMD), was …", "citation_count": 108, "publication_info": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - IET Renewable Power …, 2017 - Wiley Online Library", "link": "https://ietresearch.onlinelibrary.wiley.com/doi/abs/10.1049/iet-rpg.2016.0088", "authors": [{"name": "<PERSON>", "link": "https://scholar.google.com/citations?user=-LkOyxAAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=-LkOyxAAAAAJ&engine=google_scholar_author&hl=en", "author_id": "-LkOyxAAAAAJ"}, {"name": "Z Peng", "link": "https://scholar.google.com/citations?user=Qv9wI8YAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=Qv9wI8YAAAAJ&engine=google_scholar_author&hl=en", "author_id": "Qv9wI8YAAAAJ"}], "year": null, "journal": null, "is_pdf": false}, {"title": "Comparing variational and empirical mode decomposition in forecasting day-ahead energy prices", "description": "… empirical and variational mode decomposition domains,” IET Healthcare Technol. Lett., vol. 1, no. … <PERSON><PERSON><PERSON><PERSON>, “Physiological signal denoising with variational mode decomposition and …", "citation_count": 159, "publication_info": "<PERSON> - IEEE Systems Journal, 2015 - ieeexplore.ieee.org", "link": "https://ieeexplore.ieee.org/abstract/document/7305766/", "authors": [{"name": "S Lahmiri", "link": "https://scholar.google.com/citations?user=y-SD-gMAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=y-SD-gMAAAAJ&engine=google_scholar_author&hl=en", "author_id": "y-SD-gMAAAAJ"}], "year": null, "journal": null, "is_pdf": false}, {"title": "Composite fault diagnosis of gearbox based on empirical mode decomposition and improved variational mode decomposition", "description": "… The components with strong correlation are selected to form the combined mode … variational mode decomposition method needs to manually determine the number of mode …", "citation_count": 29, "publication_info": "<PERSON>, <PERSON>, <PERSON>, <PERSON> - Journal of Low Frequency …, 2021 - journals.sagepub.com", "link": "https://journals.sagepub.com/doi/abs/10.1177/1461348420908364", "authors": [{"name": "<PERSON>", "link": "https://scholar.google.com/citations?user=pJ816NIAAAAJ&hl=en&num=5&oi=sra", "serpapi_scholar_link": "https://serpapi.com/search.json?author_id=pJ816NIAAAAJ&engine=google_scholar_author&hl=en", "author_id": "pJ816NIAAAAJ"}], "year": null, "journal": null, "is_pdf": false}]}], "criteria": "Analyze and compare variational mode decomposition (VMD) and empirical mode decomposition (EMD) in terms of underlying principles, algorithmic approach, computational complexity, resistance to mode mixing, robustness to noise, and typical applications."}, "output_result": {"status": "success", "tool_name": "evaluate_data", "timestamp": "2025-05-22T12:47:50.243137Z", "request_id": "evaluate-data-6f6b3d53", "execution_time": 111.17610192298889, "metadata": {"criteria_description": "Analyze and compare variational mode decomposition (VMD) and empirical mode decomposition (EMD) in terms of underlying principles, algorithmic approach, computational complexity, resistance to mode mixing, robustness to noise, and typical applications.", "evaluation_score": 88, "evaluation_rating": "Excellent", "strengths_count": 5, "weaknesses_count": 5, "recommendations_count": 5, "model_used": "o4-mini"}, "data": {"summary": "Empirical Mode Decomposition (EMD) and Variational Mode Decomposition (VMD) are adaptive signal-decomposition methods widely used to analyze non-stationary and nonlinear data. EMD is a data-driven, sifting-based method that iteratively extracts Intrinsic Mode Functions (IMFs) but can suffer from mode-mixing and endpoint artifacts. VMD formulates mode extraction as a constrained variational optimization, solving for all modes and their center frequencies concurrently via Wiener-filter–style updates, which gives it superior frequency resolution and robustness to mode-mixing.\n\nIn practice, EMD is simple to implement and effective for exploratory analysis, while VMD typically yields cleaner, more sharply separated modes at the cost of higher algorithmic complexity and the need to pre-specify the number of modes. Both methods have found applications in mechanical fault diagnosis, biomedical signal processing, and time-frequency feature extraction, with VMD often outperforming EMD when precise mode separation is critical.", "detailed_analysis": {"underlying_principles": {"EMD": "Sifts the signal via envelope interpolation of extrema to extract IMFs one by one; fully data-driven with no basis functions.", "VMD": "Poses mode extraction as a constrained variational problem minimizing the sum of mode bandwidths in the spectral domain, using analytic demodulation and Wiener-filter updates."}, "algorithmic_approach": {"EMD": "Recursive sifting: identify extrema → interpolate upper/lower envelopes → subtract mean → repeat until mode criteria met → remove mode → iterate on residual.", "VMD": "Alternate Direction Method of Multipliers (ADMM): update each modeʼs frequency-shifted analytic signal via a Wiener-filter update; update center frequencies from mode spectra; iterate to convergence."}, "computational_complexity": {"EMD": "O(K·N·I) where N = signal length, I = sifting iterations, K = number of modes; typically light but unstable I.", "VMD": "Each ADMM iteration requires FFTs (O(N log N)) per mode; total cost O(M·N log N·L) where M = modes, L = iterations; heavier but predictable."}, "resistance_to_mode_mixing": {"EMD": "Prone to mode-mixing when components overlap or are intermittent; improved variants (EEMD, CEEMDAN) inject noise or ensemble averaging to alleviate mixing.", "VMD": "Inherently less mode-mixing due to global variational formulation and explicit bandwidth constraint; modes are concurrently optimized and sharply separated."}, "robustness_to_noise": {"EMD": "Sensitive to wideband noise and endpoint effects; ensemble variants mitigate noise but increase computations.", "VMD": "Wiener-filter-style updates give intrinsic noise-rejection; robust to additive noise without needing noise injection."}, "typical_applications": {"EMD": ["Biomedical signal processing (ECG, EEG denoising)", "Mechanical fault diagnosis (bearing, gear vibration analysis)", "Geophysical/climate time-frequency analysis"], "VMD": ["High-resolution fault detection in mechanical systems", "Time-frequency feature extraction in power and wind-turbine signals", "Denoising and separation of overlapping spectral components"]}}, "strengths": ["EMD is fully data-driven and requires no predefined basis, facilitating exploratory analysis.", "VMD delivers superior frequency resolution and clean mode separation, reducing mode-mixing.", "VMD is intrinsically robust to noise without artificial noise injection.", "EMD is simple to implement and computationally light for low-mode, small-data tasks.", "Both methods support adaptive time-frequency analysis of non-stationary signals."], "weaknesses": ["EMD suffers from mode-mixing, endpoint artifacts, and sensitivity to noise.", "VMD requires pre-specifying the number of modes and penalty parameters.", "VMD has higher computational cost due to iterative FFTs and ADMM loops.", "EMDʼs convergence (sifting iterations) lacks a rigorous stopping criterion.", "Both methods can struggle when signal components heavily overlap in spectrum."], "recommendations": ["Use VMD when precise mode separation and noise robustness are paramount, especially in fault diagnosis.", "Employ ensemble EMD variants (EEMD/CEEMDAN) if sticking with EMD but needing to reduce mode-mixing.", "Automate mode-number selection in VMD via model-selection criteria or adaptive penalty tuning.", "Combine EMD or VMD with complementary denoising (e.g., wavelet thresholding) for enhanced noise removal.", "<PERSON><PERSON>mark both methods on representative signals to choose the best trade-off between speed and separation accuracy."], "overall_assessment": {"score": 88, "rating": "Excellent", "conclusion": "VMD is generally superior to EMD for applications demanding precise, noise-robust mode separation, at the cost of higher complexity. EMD remains a quick, fully adaptive choice for exploratory tasks or low-cost implementations."}}}}