#!/usr/bin/env python3
"""
Test script for GaiaV2 streaming.
"""

import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the GaiaV2 client
from clients import get_gaiav2_client

async def test_streaming():
    """Test the GaiaV2 streaming functionality."""
    print("Testing GaiaV2 streaming...")
    
    # Create a GaiaV2 client
    client = await get_gaiav2_client()
    
    # Test query
    query = "plan my trip to thailand, find the top 2 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD"
    
    print(f"Query: {query}")
    print("=" * 80)
    print("Streaming response:")
    
    # Stream the response
    async for chunk in client.process_query_stream(query):
        print(chunk, end="", flush=True)
        # Small delay to make the output more readable
        await asyncio.sleep(0.01)
    
    print("\n" + "=" * 80)
    print("Streaming complete!")

if __name__ == "__main__":
    asyncio.run(test_streaming())
