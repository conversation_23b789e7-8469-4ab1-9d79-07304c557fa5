2025-05-18 23:23:10,093 - config - INFO - Found source_type in x-chatui-source-type header: Web
2025-05-18 23:23:10,093 - config - INFO - ----------------------------------
2025-05-18 23:23:10,093 - config - INFO - Source type: Web
2025-05-18 23:23:10,093 - config - INFO - ----------------------------------
2025-05-18 23:23:10,098 - root - INFO - Using max_tokens=15 for model gpt-4o
2025-05-18 23:23:10,599 - config - INFO - ----------------------------------
2025-05-18 23:23:10,600 - config - INFO - search received
2025-05-18 23:23:10,600 - config - INFO - ----------------------------------
2025-05-18 23:23:10,600 - config - INFO - ----------------------------------
2025-05-18 23:23:10,600 - config - INFO - Source type: Auto
2025-05-18 23:23:10,600 - config - INFO - ----------------------------------
2025-05-18 23:23:10,601 - config - INFO - Streaming GaiaV2 search for query: plan my trip to thiland, find the top 2 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 23:23:10,607 - config - INFO - Initialized GaiaV2 client with search type: Web
2025-05-18 23:23:10,607 - config - INFO - Processing query with GaiaV2 (streaming): plan my trip to thiland, find the top 2 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 23:23:10,709 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 23:23:10,709 - root - INFO - Setting up parameters for thinking model: o3
2025-05-18 23:23:10,709 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-05-18 23:23:10,709 - root - INFO - Using thinking model: o3
2025-05-18 23:23:10,709 - root - INFO - Setting reasoning_effort=high for model o3
2025-05-18 23:23:11,382 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:23:11,497 - root - INFO - OpenAI streaming complete: 6 chunks, 31 chars total
2025-05-18 23:24:20,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:24:20,502 - root - INFO - DECOMPOSE | Raw response content received (first 500 chars): {
  "tasks": [
    {
      "id": "t1_global_search_cities",
      "type": "primitive",
      "name": "Search web for best cities to visit in Thailand",
      "orchestration_action": "global_search",
      "parameters": {
        "query": "best cities to visit in Thailand",
        "max_results": 20
      },
      "preconditions": []
    },
    {
      "id": "t2_rank_cities",
      "type": "primitive",
      "name": "Rank Thai cities for tourism suitability",
      "orchestration_action": "rank_d...
2025-05-18 23:24:20,503 - root - INFO - DECOMPOSE | Successfully parsed raw response as JSON.
2025-05-18 23:24:20,503 - root - INFO - DECOMPOSE | Detected HTN-like plan structure with 'tasks' key.
2025-05-18 23:24:20,503 - root - INFO - DECOMPOSE | Successfully processed into primitives-only plan with 12 task(s).
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't1_global_search_cities' has no dependencies.
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_global_search_cities']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't3_select_top_cities' depends on: ['t2_rank_cities']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't4_search_hotel_city1' depends on: ['t3_select_top_cities']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't5_select_hotel_city1' depends on: ['t4_search_hotel_city1']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't6_search_activity_city1' depends on: ['t5_select_hotel_city1']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't7_select_activity_city1' depends on: ['t6_search_activity_city1']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't8_search_hotel_city2' depends on: ['t3_select_top_cities']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't9_select_hotel_city2' depends on: ['t8_search_hotel_city2']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't10_search_activity_city2' depends on: ['t9_select_hotel_city2']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't11_select_activity_city2' depends on: ['t10_search_activity_city2']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO -   Task 't12_generate_final_answers' depends on: ['t3_select_top_cities', 't5_select_hotel_city1', 't7_select_activity_city1', 't9_select_hotel_city2', 't11_select_activity_city2']
2025-05-18 23:24:20,666 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-05-18 23:24:20,667 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-05-18 23:24:20,667 - gaiav2.execution.tool_executor - INFO - Adding task 't1_global_search_cities' to current execution batch.
2025-05-18 23:24:20,667 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_global_search_cities']
2025-05-18 23:24:20,667 - gaiav2.execution.tool_executor - INFO - Executing task 't1_global_search_cities': Search web for best cities to visit in Thailand (Action: global_search)
2025-05-18 23:24:20,668 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-05-18 23:24:43,272 - gaiav2.execution.tool_executor - INFO - Task 't1_global_search_cities' (Search web for best cities to visit in Thailand) raw result: Type <class 'dict'>
2025-05-18 23:24:43,273 - gaiav2.execution.tool_executor - INFO - Task 't1_global_search_cities' completed successfully.
2025-05-18 23:24:43,274 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't1_global_search_cities' to context/tasks/t1_global_search_cities_result.json
2025-05-18 23:24:43,274 - gaiav2.execution.tool_executor - INFO - Adding task 't2_rank_cities' to current execution batch.
2025-05-18 23:24:43,274 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t2_rank_cities']
2025-05-18 23:24:43,275 - gaiav2.execution.tool_executor - INFO - Executing task 't2_rank_cities': Rank Thai cities for tourism suitability (Action: rank_data)
2025-05-18 23:24:43,276 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:24:43,277 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:18,107 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:18,115 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' (Rank Thai cities for tourism suitability) raw result: Type <class 'dict'>
2025-05-18 23:25:18,116 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' completed successfully.
2025-05-18 23:25:18,119 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't2_rank_cities' to context/tasks/t2_rank_cities_result.json
2025-05-18 23:25:18,119 - gaiav2.execution.tool_executor - INFO - Adding task 't3_select_top_cities' to current execution batch.
2025-05-18 23:25:18,119 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t3_select_top_cities']
2025-05-18 23:25:18,120 - gaiav2.execution.tool_executor - INFO - Executing task 't3_select_top_cities': Select the top 2 cities (Action: select_data)
2025-05-18 23:25:18,124 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:27,013 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:27,014 - gaiav2.execution.tool_executor - INFO - Task 't3_select_top_cities' (Select the top 2 cities) raw result: Type <class 'dict'>
2025-05-18 23:25:27,014 - gaiav2.execution.tool_executor - INFO - Task 't3_select_top_cities' completed successfully.
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't3_select_top_cities' to context/tasks/t3_select_top_cities_result.json
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Adding task 't4_search_hotel_city1' to current execution batch.
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Adding task 't8_search_hotel_city2' to current execution batch.
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Executing batch of 2 tasks: ['t4_search_hotel_city1', 't8_search_hotel_city2']
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Executing task 't4_search_hotel_city1': Search hotels for first selected city (Action: search_hotel)
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - INFO - Executing task 't8_search_hotel_city2': Search hotels for second selected city (Action: search_hotel)
2025-05-18 23:25:27,017 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-05-18 23:25:30,597 - gaiav2.execution.tool_executor - INFO - Task 't8_search_hotel_city2' (Search hotels for second selected city) raw result: Type <class 'dict'>
2025-05-18 23:25:32,128 - gaiav2.execution.tool_executor - INFO - Task 't4_search_hotel_city1' (Search hotels for first selected city) raw result: Type <class 'dict'>
2025-05-18 23:25:32,129 - gaiav2.execution.tool_executor - INFO - Task 't4_search_hotel_city1' completed successfully.
2025-05-18 23:25:32,129 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't4_search_hotel_city1' to context/tasks/t4_search_hotel_city1_result.json
2025-05-18 23:25:32,129 - gaiav2.execution.tool_executor - INFO - Task 't8_search_hotel_city2' completed successfully.
2025-05-18 23:25:32,130 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't8_search_hotel_city2' to context/tasks/t8_search_hotel_city2_result.json
2025-05-18 23:25:32,130 - gaiav2.execution.tool_executor - INFO - Adding task 't5_select_hotel_city1' to current execution batch.
2025-05-18 23:25:32,130 - gaiav2.execution.tool_executor - INFO - Adding task 't9_select_hotel_city2' to current execution batch.
2025-05-18 23:25:32,130 - gaiav2.execution.tool_executor - INFO - Executing batch of 2 tasks: ['t5_select_hotel_city1', 't9_select_hotel_city2']
2025-05-18 23:25:32,131 - gaiav2.execution.tool_executor - INFO - Executing task 't5_select_hotel_city1': Select best hotel in first city (Action: select_data)
2025-05-18 23:25:32,131 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:25:32,131 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:32,135 - gaiav2.execution.tool_executor - INFO - Executing task 't9_select_hotel_city2': Select best hotel in second city (Action: select_data)
2025-05-18 23:25:32,135 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:25:32,135 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:41,667 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:41,671 - gaiav2.execution.tool_executor - INFO - Task 't5_select_hotel_city1' (Select best hotel in first city) raw result: Type <class 'dict'>
2025-05-18 23:25:46,033 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:46,036 - gaiav2.execution.tool_executor - INFO - Task 't9_select_hotel_city2' (Select best hotel in second city) raw result: Type <class 'dict'>
2025-05-18 23:25:46,036 - gaiav2.execution.tool_executor - INFO - Task 't5_select_hotel_city1' completed successfully.
2025-05-18 23:25:46,036 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't5_select_hotel_city1' to context/tasks/t5_select_hotel_city1_result.json
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Task 't9_select_hotel_city2' completed successfully.
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't9_select_hotel_city2' to context/tasks/t9_select_hotel_city2_result.json
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Adding task 't10_search_activity_city2' to current execution batch.
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Adding task 't6_search_activity_city1' to current execution batch.
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Executing batch of 2 tasks: ['t10_search_activity_city2', 't6_search_activity_city1']
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - INFO - Executing task 't10_search_activity_city2': Search activities near selected hotel in second city (Action: search_activity)
2025-05-18 23:25:46,037 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lat' in path 'data.selected_options[0].option.lat': 'lat'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,038 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lng' in path 'data.selected_options[0].option.lng': 'lng'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,038 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-05-18 23:25:46,038 - gaiav2.execution.tool_executor - INFO - Executing task 't6_search_activity_city1': Search activities near selected hotel in first city (Action: search_activity)
2025-05-18 23:25:46,039 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lat' in path 'data.selected_options[0].option.lat': 'lat'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,039 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lng' in path 'data.selected_options[0].option.lng': 'lng'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,039 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-05-18 23:25:46,868 - gaiav2.execution.tool_executor - INFO - Task 't10_search_activity_city2' (Search activities near selected hotel in second city) raw result: Type <class 'dict'>
2025-05-18 23:25:46,927 - gaiav2.execution.tool_executor - INFO - Task 't6_search_activity_city1' (Search activities near selected hotel in first city) raw result: Type <class 'dict'>
2025-05-18 23:25:46,927 - gaiav2.execution.tool_executor - INFO - Task 't10_search_activity_city2' completed successfully.
2025-05-18 23:25:46,929 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't10_search_activity_city2' to context/tasks/t10_search_activity_city2_result.json
2025-05-18 23:25:46,929 - gaiav2.execution.tool_executor - INFO - Task 't6_search_activity_city1' completed successfully.
2025-05-18 23:25:46,933 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't6_search_activity_city1' to context/tasks/t6_search_activity_city1_result.json
2025-05-18 23:25:46,933 - gaiav2.execution.tool_executor - INFO - Adding task 't7_select_activity_city1' to current execution batch.
2025-05-18 23:25:46,933 - gaiav2.execution.tool_executor - INFO - Adding task 't11_select_activity_city2' to current execution batch.
2025-05-18 23:25:46,933 - gaiav2.execution.tool_executor - INFO - Executing batch of 2 tasks: ['t7_select_activity_city1', 't11_select_activity_city2']
2025-05-18 23:25:46,934 - gaiav2.execution.tool_executor - INFO - Executing task 't7_select_activity_city1': Select best activity near first city hotel (Action: select_data)
2025-05-18 23:25:46,934 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,934 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:46,936 - gaiav2.execution.tool_executor - INFO - Executing task 't11_select_activity_city2': Select best activity near second city hotel (Action: select_data)
2025-05-18 23:25:46,937 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:25:46,937 - root - INFO - Using thinking model: o4-mini
2025-05-18 23:25:51,545 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:51,548 - gaiav2.execution.tool_executor - INFO - Task 't7_select_activity_city1' (Select best activity near first city hotel) raw result: Type <class 'dict'>
2025-05-18 23:25:58,659 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:25:58,662 - tools.llm_tool_base - ERROR - select_data: Error executing tool: Failed to parse LLM response: the JSON object must be str, bytes or bytearray, not NoneType
2025-05-18 23:25:58,662 - gaiav2.execution.tool_executor - INFO - Task 't11_select_activity_city2' (Select best activity near second city hotel) raw result: Type <class 'dict'>
2025-05-18 23:25:58,663 - gaiav2.execution.tool_executor - INFO - Task 't7_select_activity_city1' completed successfully.
2025-05-18 23:25:58,664 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't7_select_activity_city1' to context/tasks/t7_select_activity_city1_result.json
2025-05-18 23:25:58,664 - gaiav2.execution.tool_executor - INFO - Task 't11_select_activity_city2' completed successfully.
2025-05-18 23:25:58,666 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't11_select_activity_city2' to context/tasks/t11_select_activity_city2_result.json
2025-05-18 23:25:58,667 - gaiav2.execution.tool_executor - INFO - Adding task 't12_generate_final_answers' to current execution batch.
2025-05-18 23:25:58,667 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t12_generate_final_answers']
2025-05-18 23:25:58,667 - gaiav2.execution.tool_executor - INFO - Executing task 't12_generate_final_answers': Generate final travel plan answer (Action: generate_final_answers)
2025-05-18 23:25:58,667 - gaiav2.execution.tool_executor - WARNING - Error accessing '0' in path 'data.selected_options[0].option': list index out of range. Current object type: <class 'list'>.
2025-05-18 23:25:58,667 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.selected_options[0].option': 'data'. Current object type: <class 'dict'>.
2025-05-18 23:25:58,668 - root - INFO - Using thinking model with streaming: o4-mini
2025-05-18 23:26:12,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 23:26:22,656 - root - INFO - OpenAI streaming complete: 955 chunks, 3353 chars total
2025-05-18 23:26:22,659 - gaiav2.execution.tool_executor - INFO - Task 't12_generate_final_answers' (Generate final travel plan answer) raw result: Type <class 'dict'>
2025-05-18 23:26:22,660 - gaiav2.execution.tool_executor - INFO - Task 't12_generate_final_answers' completed successfully.
2025-05-18 23:26:22,661 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't12_generate_final_answers' to context/tasks/t12_generate_final_answers_result.json
2025-05-18 23:26:22,662 - gaiav2.execution.tool_executor - INFO - All 12 tasks accounted for. Completed: 12, Failed: 0.
2025-05-18 23:26:22,669 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250518_232622.json
2025-05-18 23:26:22,720 - config - INFO - Completed tasks: {'t5_select_hotel_city1', 't4_search_hotel_city1', 't3_select_top_cities', 't9_select_hotel_city2', 't2_rank_cities', 't7_select_activity_city1', 't11_select_activity_city2', 't12_generate_final_answers', 't1_global_search_cities', 't10_search_activity_city2', 't6_search_activity_city1', 't8_search_hotel_city2'}
2025-05-18 23:26:22,720 - config - INFO - Results keys: dict_keys(['t1_global_search_cities', 't2_rank_cities', 't3_select_top_cities', 't4_search_hotel_city1', 't8_search_hotel_city2', 't5_select_hotel_city1', 't9_select_hotel_city2', 't10_search_activity_city2', 't6_search_activity_city1', 't7_select_activity_city1', 't11_select_activity_city2', 't12_generate_final_answers'])
2025-05-18 23:26:22,720 - config - INFO - Potential final answer tasks: ['t12_generate_final_answers']
2025-05-18 23:26:22,720 - config - INFO - Found result for task t12_generate_final_answers: <class 'dict'>
2025-05-18 23:26:22,720 - config - INFO - No answer found in final tasks, checking all tasks
2025-05-18 23:26:22,721 - config - INFO - Final answer found: I'm sorry, I couldn't find a good answer to your query....
2025-05-18 23:26:22,721 - config - INFO - Transitioning from logs to answer content
2025-05-18 23:26:22,721 - config - INFO - Streaming final answer character by character
2025-05-18 23:26:22,721 - config - INFO - Yielding content chunk: I...
2025-05-18 23:26:22,722 - config - INFO - Yielding content chunk: '...
2025-05-18 23:26:22,723 - config - INFO - Yielding content chunk: m...
2025-05-18 23:26:22,725 - config - INFO - Yielding content chunk: s...
2025-05-18 23:26:22,727 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,728 - config - INFO - Yielding content chunk: r...
2025-05-18 23:26:22,729 - config - INFO - Yielding content chunk: r...
2025-05-18 23:26:22,731 - config - INFO - Yielding content chunk: y...
2025-05-18 23:26:22,732 - config - INFO - Yielding content chunk: ,...
2025-05-18 23:26:22,734 - config - INFO - Yielding content chunk: I...
2025-05-18 23:26:22,737 - config - INFO - Yielding content chunk: c...
2025-05-18 23:26:22,738 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,739 - config - INFO - Yielding content chunk: u...
2025-05-18 23:26:22,741 - config - INFO - Yielding content chunk: l...
2025-05-18 23:26:22,742 - config - INFO - Yielding content chunk: d...
2025-05-18 23:26:22,743 - config - INFO - Yielding content chunk: n...
2025-05-18 23:26:22,744 - config - INFO - Yielding content chunk: '...
2025-05-18 23:26:22,746 - config - INFO - Yielding content chunk: t...
2025-05-18 23:26:22,748 - config - INFO - Yielding content chunk: f...
2025-05-18 23:26:22,749 - config - INFO - Yielding content chunk: i...
2025-05-18 23:26:22,751 - config - INFO - Yielding content chunk: n...
2025-05-18 23:26:22,752 - config - INFO - Yielding content chunk: d...
2025-05-18 23:26:22,754 - config - INFO - Yielding content chunk: a...
2025-05-18 23:26:22,757 - config - INFO - Yielding content chunk: g...
2025-05-18 23:26:22,757 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,758 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,760 - config - INFO - Yielding content chunk: d...
2025-05-18 23:26:22,762 - config - INFO - Yielding content chunk: a...
2025-05-18 23:26:22,764 - config - INFO - Yielding content chunk: n...
2025-05-18 23:26:22,766 - config - INFO - Yielding content chunk: s...
2025-05-18 23:26:22,767 - config - INFO - Yielding content chunk: w...
2025-05-18 23:26:22,769 - config - INFO - Yielding content chunk: e...
2025-05-18 23:26:22,770 - config - INFO - Yielding content chunk: r...
2025-05-18 23:26:22,772 - config - INFO - Yielding content chunk: t...
2025-05-18 23:26:22,774 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,777 - config - INFO - Yielding content chunk: y...
2025-05-18 23:26:22,778 - config - INFO - Yielding content chunk: o...
2025-05-18 23:26:22,780 - config - INFO - Yielding content chunk: u...
2025-05-18 23:26:22,782 - config - INFO - Yielding content chunk: r...
2025-05-18 23:26:22,784 - config - INFO - Yielding content chunk: q...
2025-05-18 23:26:22,786 - config - INFO - Yielding content chunk: u...
2025-05-18 23:26:22,787 - config - INFO - Yielding content chunk: e...
2025-05-18 23:26:22,788 - config - INFO - Yielding content chunk: r...
2025-05-18 23:26:22,789 - config - INFO - Yielding content chunk: y...
2025-05-18 23:26:22,791 - config - INFO - Yielding content chunk: ....
2025-05-18 23:26:22,792 - config - INFO - GaiaV2 search streaming complete. Sent 74 chunks.
