2025-05-18 21:32:05,565 - config - INFO - Found source_type in x-chatui-source-type header: Web
2025-05-18 21:32:05,565 - config - INFO - ----------------------------------
2025-05-18 21:32:05,565 - config - INFO - Source type: Web
2025-05-18 21:32:05,565 - config - INFO - ----------------------------------
2025-05-18 21:32:05,568 - root - INFO - Using max_tokens=15 for model gpt-4o
2025-05-18 21:32:05,921 - config - INFO - ----------------------------------
2025-05-18 21:32:05,921 - config - INFO - search received
2025-05-18 21:32:05,921 - config - INFO - ----------------------------------
2025-05-18 21:32:05,921 - config - INFO - ----------------------------------
2025-05-18 21:32:05,921 - config - INFO - Source type: Auto
2025-05-18 21:32:05,921 - config - INFO - ----------------------------------
2025-05-18 21:32:05,922 - config - INFO - Streaming GaiaV2 search for query: plan my trip to thiland, find the top 3 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 21:32:05,928 - config - INFO - Initialized GaiaV2 client with search type: Web
2025-05-18 21:32:05,928 - config - INFO - Processing query with GaiaV2 (streaming): plan my trip to thiland, find the top 3 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 21:32:06,030 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 21:32:06,031 - root - INFO - Setting up parameters for thinking model: o3
2025-05-18 21:32:06,031 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-05-18 21:32:06,031 - root - INFO - Using thinking model: o3
2025-05-18 21:32:06,031 - root - INFO - Setting reasoning_effort=high for model o3
2025-05-18 21:32:07,637 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:32:07,646 - root - INFO - OpenAI streaming complete: 4 chunks, 24 chars total
