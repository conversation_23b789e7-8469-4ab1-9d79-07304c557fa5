2025-05-18 21:32:47,616 - config - INFO - Found source_type in x-chatui-source-type header: Web
2025-05-18 21:32:47,616 - config - INFO - ----------------------------------
2025-05-18 21:32:47,616 - config - INFO - Source type: Web
2025-05-18 21:32:47,616 - config - INFO - ----------------------------------
2025-05-18 21:32:47,622 - root - INFO - Using max_tokens=15 for model gpt-4o
2025-05-18 21:32:47,972 - config - INFO - ----------------------------------
2025-05-18 21:32:47,973 - config - INFO - search received
2025-05-18 21:32:47,973 - config - INFO - ----------------------------------
2025-05-18 21:32:47,973 - config - INFO - ----------------------------------
2025-05-18 21:32:47,973 - config - INFO - Source type: Auto
2025-05-18 21:32:47,973 - config - INFO - ----------------------------------
2025-05-18 21:32:47,974 - config - INFO - Streaming GaiaV2 search for query: plan my trip to thiland, find the top 3 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 21:32:47,980 - config - INFO - Initialized GaiaV2 client with search type: Web
2025-05-18 21:32:47,980 - config - INFO - Processing query with GaiaV2 (streaming): plan my trip to thiland, find the top 3 cities and find me the best hotel in each city also find the best activity nearby the selected hotel for each city, plan for oct 1 to oct 30 2025 for 1 adult please use USD
2025-05-18 21:32:48,083 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 21:32:48,083 - root - INFO - Setting up parameters for thinking model: o3
2025-05-18 21:32:48,083 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-05-18 21:32:48,083 - root - INFO - Using thinking model: o3
2025-05-18 21:32:48,083 - root - INFO - Setting reasoning_effort=high for model o3
2025-05-18 21:32:49,160 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:32:49,218 - root - INFO - OpenAI streaming complete: 4 chunks, 24 chars total
2025-05-18 21:34:19,118 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:34:19,147 - root - INFO - DECOMPOSE | Raw response content received (first 500 chars): {
  "tasks": [
    {
      "id": "t1_search_cities",
      "type": "primitive",
      "name": "Search web for best tourist cities in Thailand",
      "orchestration_action": "global_search",
      "parameters": {
        "query": "best tourist cities in Thailand top destinations 2025",
        "engines": ["google"],
        "max_results": 20
      },
      "preconditions": []
    },
    {
      "id": "t2_rank_cities",
      "type": "primitive",
      "name": "Rank Thai cities to find top 3",
   ...
2025-05-18 21:34:19,148 - root - INFO - DECOMPOSE | Successfully parsed raw response as JSON.
2025-05-18 21:34:19,148 - root - INFO - DECOMPOSE | Detected HTN-like plan structure with 'tasks' key.
2025-05-18 21:34:19,148 - root - INFO - DECOMPOSE | Successfully processed into primitives-only plan with 15 task(s).
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't4_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't5_search_activities_city1' depends on: ['t4_select_hotel_city1', 't2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_activity_city1' depends on: ['t5_search_activities_city1']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_hotels_city2' depends on: ['t2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't8_select_hotel_city2' depends on: ['t7_search_hotels_city2']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't9_search_activities_city2' depends on: ['t8_select_hotel_city2', 't2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t9_search_activities_city2']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't11_search_hotels_city3' depends on: ['t2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't12_select_hotel_city3' depends on: ['t11_search_hotels_city3']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't13_search_activities_city3' depends on: ['t12_select_hotel_city3', 't2_rank_cities']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't14_select_activity_city3' depends on: ['t13_search_activities_city3']
2025-05-18 21:34:19,344 - gaiav2.execution.tool_executor - INFO -   Task 't15_generate_final_answers' depends on: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't2_rank_cities', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3']
2025-05-18 21:34:19,345 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-05-18 21:34:19,345 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-05-18 21:34:19,345 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_cities' to current execution batch.
2025-05-18 21:34:19,345 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_search_cities']
2025-05-18 21:34:19,345 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_cities': Search web for best tourist cities in Thailand (Action: global_search)
2025-05-18 21:34:19,347 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-05-18 21:34:44,992 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' (Search web for best tourist cities in Thailand) raw result: Type <class 'dict'>
2025-05-18 21:34:44,993 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' completed successfully.
2025-05-18 21:34:44,995 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't1_search_cities' to context/tasks/t1_search_cities_result.json
2025-05-18 21:34:44,995 - gaiav2.execution.tool_executor - INFO - Adding task 't2_rank_cities' to current execution batch.
2025-05-18 21:34:44,995 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t2_rank_cities']
2025-05-18 21:34:44,995 - gaiav2.execution.tool_executor - INFO - Executing task 't2_rank_cities': Rank Thai cities to find top 3 (Action: rank_data)
2025-05-18 21:34:45,003 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:09,486 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:35:09,491 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' (Rank Thai cities to find top 3) raw result: Type <class 'dict'>
2025-05-18 21:35:09,491 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' completed successfully.
2025-05-18 21:35:09,497 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't2_rank_cities' to context/tasks/t2_rank_cities_result.json
2025-05-18 21:35:09,497 - gaiav2.execution.tool_executor - INFO - Adding task 't7_search_hotels_city2' to current execution batch.
2025-05-18 21:35:09,497 - gaiav2.execution.tool_executor - INFO - Adding task 't3_search_hotels_city1' to current execution batch.
2025-05-18 21:35:09,497 - gaiav2.execution.tool_executor - INFO - Adding task 't11_search_hotels_city3' to current execution batch.
2025-05-18 21:35:09,497 - gaiav2.execution.tool_executor - INFO - Executing batch of 3 tasks: ['t7_search_hotels_city2', 't3_search_hotels_city1', 't11_search_hotels_city3']
2025-05-18 21:35:09,498 - gaiav2.execution.tool_executor - INFO - Executing task 't7_search_hotels_city2': Search hotels in second ranked city (Action: search_hotel)
2025-05-18 21:35:09,498 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-05-18 21:35:09,499 - gaiav2.execution.tool_executor - INFO - Executing task 't3_search_hotels_city1': Search hotels in first ranked city (Action: search_hotel)
2025-05-18 21:35:09,499 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-05-18 21:35:09,500 - gaiav2.execution.tool_executor - INFO - Executing task 't11_search_hotels_city3': Search hotels in third ranked city (Action: search_hotel)
2025-05-18 21:35:09,502 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-05-18 21:35:12,978 - gaiav2.execution.tool_executor - INFO - Task 't7_search_hotels_city2' (Search hotels in second ranked city) raw result: Type <class 'dict'>
2025-05-18 21:35:15,708 - gaiav2.execution.tool_executor - INFO - Task 't3_search_hotels_city1' (Search hotels in first ranked city) raw result: Type <class 'dict'>
2025-05-18 21:35:18,320 - gaiav2.execution.tool_executor - INFO - Task 't11_search_hotels_city3' (Search hotels in third ranked city) raw result: Type <class 'dict'>
2025-05-18 21:35:18,323 - gaiav2.execution.tool_executor - INFO - Task 't7_search_hotels_city2' completed successfully.
2025-05-18 21:35:18,325 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't7_search_hotels_city2' to context/tasks/t7_search_hotels_city2_result.json
2025-05-18 21:35:18,325 - gaiav2.execution.tool_executor - INFO - Task 't3_search_hotels_city1' completed successfully.
2025-05-18 21:35:18,326 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't3_search_hotels_city1' to context/tasks/t3_search_hotels_city1_result.json
2025-05-18 21:35:18,326 - gaiav2.execution.tool_executor - INFO - Task 't11_search_hotels_city3' completed successfully.
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't11_search_hotels_city3' to context/tasks/t11_search_hotels_city3_result.json
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Adding task 't4_select_hotel_city1' to current execution batch.
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Adding task 't8_select_hotel_city2' to current execution batch.
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Adding task 't12_select_hotel_city3' to current execution batch.
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Executing batch of 3 tasks: ['t4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3']
2025-05-18 21:35:18,328 - gaiav2.execution.tool_executor - INFO - Executing task 't4_select_hotel_city1': Select best hotel in first city (Action: select_data)
2025-05-18 21:35:18,329 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:18,331 - gaiav2.execution.tool_executor - INFO - Executing task 't8_select_hotel_city2': Select best hotel in second city (Action: select_data)
2025-05-18 21:35:18,333 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:18,333 - gaiav2.execution.tool_executor - INFO - Executing task 't12_select_hotel_city3': Select best hotel in third city (Action: select_data)
2025-05-18 21:35:18,335 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:39,724 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:35:39,726 - gaiav2.execution.tool_executor - INFO - Task 't8_select_hotel_city2' (Select best hotel in second city) raw result: Type <class 'dict'>
2025-05-18 21:35:40,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:35:40,495 - gaiav2.execution.tool_executor - INFO - Task 't4_select_hotel_city1' (Select best hotel in first city) raw result: Type <class 'dict'>
2025-05-18 21:35:40,686 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:35:40,688 - gaiav2.execution.tool_executor - INFO - Task 't12_select_hotel_city3' (Select best hotel in third city) raw result: Type <class 'dict'>
2025-05-18 21:35:40,688 - gaiav2.execution.tool_executor - INFO - Task 't4_select_hotel_city1' completed successfully.
2025-05-18 21:35:40,690 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't4_select_hotel_city1' to context/tasks/t4_select_hotel_city1_result.json
2025-05-18 21:35:40,690 - gaiav2.execution.tool_executor - INFO - Task 't8_select_hotel_city2' completed successfully.
2025-05-18 21:35:40,692 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't8_select_hotel_city2' to context/tasks/t8_select_hotel_city2_result.json
2025-05-18 21:35:40,692 - gaiav2.execution.tool_executor - INFO - Task 't12_select_hotel_city3' completed successfully.
2025-05-18 21:35:40,693 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't12_select_hotel_city3' to context/tasks/t12_select_hotel_city3_result.json
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Adding task 't5_search_activities_city1' to current execution batch.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Adding task 't13_search_activities_city3' to current execution batch.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Adding task 't9_search_activities_city2' to current execution batch.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Executing batch of 3 tasks: ['t5_search_activities_city1', 't13_search_activities_city3', 't9_search_activities_city2']
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Executing task 't5_search_activities_city1': Search activities near selected hotel in first city (Action: search_activity)
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lat' in path 'data.selected_options[0].option.lat': 'lat'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lng' in path 'data.selected_options[0].option.lng': 'lng'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-05-18 21:35:40,694 - gaiav2.execution.tool_executor - INFO - Executing task 't13_search_activities_city3': Search activities near selected hotel in third city (Action: search_activity)
2025-05-18 21:35:40,695 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lat' in path 'data.selected_options[0].option.lat': 'lat'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,695 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lng' in path 'data.selected_options[0].option.lng': 'lng'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,695 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-05-18 21:35:40,696 - gaiav2.execution.tool_executor - INFO - Executing task 't9_search_activities_city2': Search activities near selected hotel in second city (Action: search_activity)
2025-05-18 21:35:40,696 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lat' in path 'data.selected_options[0].option.lat': 'lat'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,697 - gaiav2.execution.tool_executor - WARNING - Error accessing 'lng' in path 'data.selected_options[0].option.lng': 'lng'. Current object type: <class 'dict'>.
2025-05-18 21:35:40,697 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-05-18 21:35:41,636 - gaiav2.execution.tool_executor - INFO - Task 't9_search_activities_city2' (Search activities near selected hotel in second city) raw result: Type <class 'dict'>
2025-05-18 21:35:41,972 - gaiav2.execution.tool_executor - INFO - Task 't5_search_activities_city1' (Search activities near selected hotel in first city) raw result: Type <class 'dict'>
2025-05-18 21:35:42,530 - gaiav2.execution.tool_executor - INFO - Task 't13_search_activities_city3' (Search activities near selected hotel in third city) raw result: Type <class 'dict'>
2025-05-18 21:35:42,530 - gaiav2.execution.tool_executor - INFO - Task 't5_search_activities_city1' completed successfully.
2025-05-18 21:35:42,533 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't5_search_activities_city1' to context/tasks/t5_search_activities_city1_result.json
2025-05-18 21:35:42,533 - gaiav2.execution.tool_executor - INFO - Task 't13_search_activities_city3' completed successfully.
2025-05-18 21:35:42,534 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't13_search_activities_city3' to context/tasks/t13_search_activities_city3_result.json
2025-05-18 21:35:42,534 - gaiav2.execution.tool_executor - INFO - Task 't9_search_activities_city2' completed successfully.
2025-05-18 21:35:42,536 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't9_search_activities_city2' to context/tasks/t9_search_activities_city2_result.json
2025-05-18 21:35:42,536 - gaiav2.execution.tool_executor - INFO - Adding task 't6_select_activity_city1' to current execution batch.
2025-05-18 21:35:42,536 - gaiav2.execution.tool_executor - INFO - Adding task 't14_select_activity_city3' to current execution batch.
2025-05-18 21:35:42,536 - gaiav2.execution.tool_executor - INFO - Adding task 't10_select_activity_city2' to current execution batch.
2025-05-18 21:35:42,536 - gaiav2.execution.tool_executor - INFO - Executing batch of 3 tasks: ['t6_select_activity_city1', 't14_select_activity_city3', 't10_select_activity_city2']
2025-05-18 21:35:42,537 - gaiav2.execution.tool_executor - INFO - Executing task 't6_select_activity_city1': Select best activity near selected hotel in first city (Action: select_data)
2025-05-18 21:35:42,539 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:42,541 - gaiav2.execution.tool_executor - INFO - Executing task 't14_select_activity_city3': Select best activity near selected hotel in third city (Action: select_data)
2025-05-18 21:35:42,544 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:42,545 - gaiav2.execution.tool_executor - INFO - Executing task 't10_select_activity_city2': Select best activity near selected hotel in second city (Action: select_data)
2025-05-18 21:35:42,548 - root - INFO - Using thinking model: o4-mini
2025-05-18 21:35:59,994 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:35:59,999 - gaiav2.execution.tool_executor - INFO - Task 't6_select_activity_city1' (Select best activity near selected hotel in first city) raw result: Type <class 'dict'>
2025-05-18 21:36:22,259 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:36:22,261 - gaiav2.execution.tool_executor - INFO - Task 't10_select_activity_city2' (Select best activity near selected hotel in second city) raw result: Type <class 'dict'>
2025-05-18 21:36:43,543 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:36:43,549 - gaiav2.execution.tool_executor - INFO - Task 't14_select_activity_city3' (Select best activity near selected hotel in third city) raw result: Type <class 'dict'>
2025-05-18 21:36:43,550 - gaiav2.execution.tool_executor - INFO - Task 't6_select_activity_city1' completed successfully.
2025-05-18 21:36:43,552 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't6_select_activity_city1' to context/tasks/t6_select_activity_city1_result.json
2025-05-18 21:36:43,552 - gaiav2.execution.tool_executor - INFO - Task 't14_select_activity_city3' completed successfully.
2025-05-18 21:36:43,555 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't14_select_activity_city3' to context/tasks/t14_select_activity_city3_result.json
2025-05-18 21:36:43,555 - gaiav2.execution.tool_executor - INFO - Task 't10_select_activity_city2' completed successfully.
2025-05-18 21:36:43,558 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't10_select_activity_city2' to context/tasks/t10_select_activity_city2_result.json
2025-05-18 21:36:43,558 - gaiav2.execution.tool_executor - INFO - Adding task 't15_generate_final_answers' to current execution batch.
2025-05-18 21:36:43,558 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t15_generate_final_answers']
2025-05-18 21:36:43,558 - gaiav2.execution.tool_executor - INFO - Executing task 't15_generate_final_answers': Generate final trip plan (Action: generate_final_answers)
2025-05-18 21:36:43,566 - root - INFO - Using thinking model with streaming: o4-mini
2025-05-18 21:36:53,037 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 21:37:07,756 - root - INFO - OpenAI streaming complete: 931 chunks, 3419 chars total
2025-05-18 21:37:07,758 - gaiav2.execution.tool_executor - INFO - Task 't15_generate_final_answers' (Generate final trip plan) raw result: Type <class 'dict'>
2025-05-18 21:37:07,758 - gaiav2.execution.tool_executor - INFO - Task 't15_generate_final_answers' completed successfully.
2025-05-18 21:37:07,761 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't15_generate_final_answers' to context/tasks/t15_generate_final_answers_result.json
2025-05-18 21:37:07,761 - gaiav2.execution.tool_executor - INFO - All 15 tasks accounted for. Completed: 15, Failed: 0.
2025-05-18 21:37:07,768 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250518_213707.json
2025-05-18 21:37:07,819 - config - ERROR - Error in GaiaV2 search stream: 'set' object has no attribute 'items'
2025-05-18 21:37:07,904 - config - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/api_wrapper/streaming.py", line 373, in _stream_gaiav2_response
    async for chunk in gaiav2_client.process_query_stream(query):
  File "/Users/<USER>/code/snipesearch/api_wrapper/gaiav2_client.py", line 180, in process_query_stream
    for task_id, result in streaming_executor.completed_tasks.items():
AttributeError: 'set' object has no attribute 'items'

