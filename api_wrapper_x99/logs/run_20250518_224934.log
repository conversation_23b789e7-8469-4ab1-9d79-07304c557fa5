2025-05-18 22:52:05,409 - config - INFO - Found source_type in x-chatui-source-type header: Web
2025-05-18 22:52:05,409 - config - INFO - ----------------------------------
2025-05-18 22:52:05,409 - config - INFO - Source type: Web
2025-05-18 22:52:05,409 - config - INFO - ----------------------------------
2025-05-18 22:52:05,414 - root - INFO - Using max_tokens=15 for model gpt-4o
2025-05-18 22:52:05,817 - config - INFO - ----------------------------------
2025-05-18 22:52:05,817 - config - INFO - search received
2025-05-18 22:52:05,817 - config - INFO - ----------------------------------
2025-05-18 22:52:05,818 - config - INFO - ----------------------------------
2025-05-18 22:52:05,818 - config - INFO - Source type: Auto
2025-05-18 22:52:05,818 - config - INFO - ----------------------------------
2025-05-18 22:52:05,819 - config - INFO - Streaming GaiaV2 search for query: Searching...
Starting GaiaV2 search...
Decomposing query...
Query decomposed into 0 main questions
Building search plan...
Executing task: Search web for best tourist cities in Thailand
Completed task: Search web for best tourist cities in Thailand
Executing task: Rank Thai cities for tourism
Completed task: Rank Thai cities for tourism
Executing task: Search for hotels in top city 2
Executing task: Search for hotels in top city 3
Executing task: Search for hotels in top city 1
Completed task: Search for hotels in top city 3
Completed task: Search for hotels in top city 1
Completed task: Search for hotels in top city 2
Executing task: Select best hotel in city 1
Executing task: Select best hotel in city 3
Executing task: Select best hotel in city 2
Completed task: Select best hotel in city 3
Completed task: Select best hotel in city 2
Completed task: Select best hotel in city 1
Executing task: Search for activities near selected hotel in city 2
Executing task: Search for activities near selected hotel in city 1
Executing task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 1
Completed task: Search for activities near selected hotel in city 2
Executing task: Select best activity near selected hotel in city 1
Executing task: Select best activity near selected hotel in city 3
Executing task: Select best activity near selected hotel in city 2
Completed task: Select best activity near selected hotel in city 3
Completed task: Select best activity near selected hotel in city 1
Completed task: Select best activity near selected hotel in city 2
Executing task: Generate final trip plan answers
Completed task: Generate final trip plan answers
Search complete.
2025-05-18 22:52:05,825 - config - INFO - Initialized GaiaV2 client with search type: Web
2025-05-18 22:52:05,825 - config - INFO - Processing query with GaiaV2 (streaming): Searching...
Starting GaiaV2 search...
Decomposing query...
Query decomposed into 0 main questions
Building search plan...
Executing task: Search web for best tourist cities in Thailand
Completed task: Search web for best tourist cities in Thailand
Executing task: Rank Thai cities for tourism
Completed task: Rank Thai cities for tourism
Executing task: Search for hotels in top city 2
Executing task: Search for hotels in top city 3
Executing task: Search for hotels in top city 1
Completed task: Search for hotels in top city 3
Completed task: Search for hotels in top city 1
Completed task: Search for hotels in top city 2
Executing task: Select best hotel in city 1
Executing task: Select best hotel in city 3
Executing task: Select best hotel in city 2
Completed task: Select best hotel in city 3
Completed task: Select best hotel in city 2
Completed task: Select best hotel in city 1
Executing task: Search for activities near selected hotel in city 2
Executing task: Search for activities near selected hotel in city 1
Executing task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 1
Completed task: Search for activities near selected hotel in city 2
Executing task: Select best activity near selected hotel in city 1
Executing task: Select best activity near selected hotel in city 3
Executing task: Select best activity near selected hotel in city 2
Completed task: Select best activity near selected hotel in city 3
Completed task: Select best activity near selected hotel in city 1
Completed task: Select best activity near selected hotel in city 2
Executing task: Generate final trip plan answers
Completed task: Generate final trip plan answers
Search complete.
2025-05-18 22:52:05,928 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 22:52:05,929 - root - INFO - Setting up parameters for thinking model: o3
2025-05-18 22:52:05,929 - root - INFO - Setting reasoning_effort=high for thinking model: o3
2025-05-18 22:52:05,929 - root - INFO - Using thinking model: o3
2025-05-18 22:52:05,929 - root - INFO - Setting reasoning_effort=high for model o3
2025-05-18 22:52:07,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 22:52:07,728 - root - INFO - OpenAI streaming complete: 5 chunks, 30 chars total
2025-05-18 22:52:11,173 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 22:52:11,178 - root - INFO - DECOMPOSE | Raw response content received (first 500 chars): Hello! How can I assist you? Please tell me what you would like to plan or search for so I can create a detailed plan for you....
2025-05-18 22:52:11,178 - root - INFO - DECOMPOSE | Failed to parse raw response as JSON. Assuming it's a question.
2025-05-18 22:52:11,178 - config - ERROR - Failed to decompose query: Searching...
Starting GaiaV2 search...
Decomposing query...
Query decomposed into 0 main questions
Building search plan...
Executing task: Search web for best tourist cities in Thailand
Completed task: Search web for best tourist cities in Thailand
Executing task: Rank Thai cities for tourism
Completed task: Rank Thai cities for tourism
Executing task: Search for hotels in top city 2
Executing task: Search for hotels in top city 3
Executing task: Search for hotels in top city 1
Completed task: Search for hotels in top city 3
Completed task: Search for hotels in top city 1
Completed task: Search for hotels in top city 2
Executing task: Select best hotel in city 1
Executing task: Select best hotel in city 3
Executing task: Select best hotel in city 2
Completed task: Select best hotel in city 3
Completed task: Select best hotel in city 2
Completed task: Select best hotel in city 1
Executing task: Search for activities near selected hotel in city 2
Executing task: Search for activities near selected hotel in city 1
Executing task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 3
Completed task: Search for activities near selected hotel in city 1
Completed task: Search for activities near selected hotel in city 2
Executing task: Select best activity near selected hotel in city 1
Executing task: Select best activity near selected hotel in city 3
Executing task: Select best activity near selected hotel in city 2
Completed task: Select best activity near selected hotel in city 3
Completed task: Select best activity near selected hotel in city 1
Completed task: Select best activity near selected hotel in city 2
Executing task: Generate final trip plan answers
Completed task: Generate final trip plan answers
Search complete.
2025-05-18 22:52:11,230 - config - INFO - GaiaV2 search streaming complete. Sent 4 chunks.
