"""
Request handlers for API endpoints.
"""

import json
import traceback
from typing import Dict, List, Any, Union
from fastapi import Request, Response, HTTPException
from fastapi.responses import StreamingResponse
from gaia.llm.llm import LLM

from config import logger
from utils import parse_chat_request, check_search_enabled, check_deep_snipe_enabled, get_source_type
from search import perform_gaia_search
from streaming import stream_gaia_response, stream_gaia_search_response
from formatters import format_non_streaming_response, format_completions_response

async def handle_chat_request(
    llm: LLM,
    request_params: Dict[str, Any],
    tools_support: bool = True
) -> Union[Response, StreamingResponse]:
    """
    Handle a chat request based on the parsed parameters.

    Args:
        llm: The LLM client
        request_params: The parsed request parameters
        tools_support: Whether to support tools in this request

    Returns:
        Union[Response, StreamingResponse]: The API response
    """
    # Extract parameters
    messages = request_params['messages']
    model = request_params['model']
    temperature = request_params['temperature']
    max_tokens = request_params['max_tokens']
    stream = request_params['stream']
    query = request_params['query']
    use_deep_snipe = request_params['use_deep_snipe']
    use_search = request_params['use_search']
    source_type = request_params['source_type']
    tools = request_params['tools'] if tools_support else None
    tool_choice = request_params['tool_choice'] if tools_support else None

    # If any search functionality is enabled, use Gaia search
    if use_search:
        if stream:
            return StreamingResponse(
                stream_gaia_search_response(query, model, use_deep_snipe=use_deep_snipe, source_type=source_type),
                media_type="text/event-stream"
            )
        else:
            # Non-streaming search
            response = await perform_gaia_search(query, use_deep_snipe=use_deep_snipe, source_type=source_type)

            # Format the response to match OpenAI's format
            openai_response = format_non_streaming_response(response, model)

            return Response(
                content=json.dumps(openai_response),
                media_type="application/json"
            )


            
    else:
        # Regular LLM request without search
        if stream:
            return StreamingResponse(
                stream_gaia_response(llm, messages, model, temperature, max_tokens, tools, tool_choice),
                media_type="text/event-stream"
            )
        else:
            # For non-streaming responses, use generate
            generate_kwargs = {
                "messages": messages,
                "model": model,
                "temperature": temperature,
                "max_tokens": max_tokens
            }

            # Add tools parameters if they exist and are supported
            if tools and tools_support:
                generate_kwargs["tools"] = tools
            if tool_choice and tools_support:
                generate_kwargs["tool_choice"] = tool_choice

            response = await llm.generate(**generate_kwargs)

            # Format the response to match OpenAI's format
            openai_response = format_non_streaming_response(response, model)

            return Response(
                content=json.dumps(openai_response),
                media_type="application/json"
            )

async def handle_simpleai_chat_completions(request: Request, llm: LLM):
    """
    Handle SimpleAI chat completions using Gaia - a simplified version without tools support.

    This endpoint provides compatibility with SimpleAI clients.
    """
    try:
        # Get raw request body
        body = await request.json()

        # Log the request without sensitive data
        log_body = body.copy() if isinstance(body, dict) else body
        if isinstance(log_body, dict) and 'messages' in log_body:
            log_body['messages'] = f"[{len(log_body['messages'])} messages]"

        # Parse request parameters
        request_params = await parse_chat_request(body, request)

        # Handle the request based on the parsed parameters
        return await handle_chat_request(
            llm=llm,
            request_params=request_params,
            tools_support=False  # SimpleAI endpoint doesn't support tools
        )

    except Exception as e:
        logger.error(f"SimpleAI Error: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

async def handle_chat_completions(request: Request, llm: LLM):
    """
    Handle regular OpenAI chat completions API calls using Gaia.

    This endpoint provides full compatibility with the OpenAI chat completions API,
    including support for tools.
    """
    try:
        # Get raw request body
        body = await request.json()

        # Log the request without sensitive data
        log_body = body.copy() if isinstance(body, dict) else body
        if isinstance(log_body, dict) and 'messages' in log_body:
            log_body['messages'] = f"[{len(log_body['messages'])} messages]"
        logger.info(f"Received OpenAI request: {log_body}")

        # Parse request parameters
        request_params = await parse_chat_request(body, request)

        # If both search and deep snipe are enabled, log that we're prioritizing deep snipe
        if request_params['use_search'] and request_params['use_deep_snipe']:
            logger.info("Both web search and deep snipe enabled - using deep snipe (Gaia Fallback)")

        # Handle the request based on the parsed parameters
        return await handle_chat_request(
            llm=llm,
            request_params=request_params,
            tools_support=True  # OpenAI endpoint supports tools
        )

    except Exception as e:
        logger.error(f"OpenAI Error: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")


#TODO remove this
async def handle_completions(request: Request, llm: LLM):
    """
    Handle text completions API calls using Gaia.

    This endpoint provides compatibility with the OpenAI completions API.
    """
    try:
        body = await request.json()

        # Extract parameters
        prompt = body.get('prompt', '')
        model = body.get('model', 'gpt-4.1')
        temperature = body.get('temperature', 0.7)
        max_tokens = body.get('max_tokens', 1024)
        stream = body.get('stream', False)

        # Convert the prompt to messages format for LLM
        messages = [{"role": "user", "content": prompt}]

        # Check feature flags
        search_enabled = check_search_enabled(body)
        deep_snipe_enabled = check_deep_snipe_enabled(body)
        source_type = get_source_type(body, request)

        # Log source type
        logger.info(f"Source type: {source_type}")

        # If both are enabled, log that we're prioritizing deep snipe
        if search_enabled and deep_snipe_enabled:
            logger.info("Both web search and deep snipe enabled - using deep snipe (Gaia Fallback)")

        # Priority: If deep_snipe is enabled, use it regardless of web search setting
        use_deep_snipe = deep_snipe_enabled
        use_search = search_enabled or deep_snipe_enabled

        if use_search:
            if stream:
                return StreamingResponse(
                    stream_gaia_search_response(prompt, model, use_deep_snipe=use_deep_snipe, source_type=source_type),
                    media_type="text/event-stream"
                )
            else:
                # Non-streaming search
                response = await perform_gaia_search(prompt, use_deep_snipe=use_deep_snipe, source_type=source_type)

                # Format for completions endpoint
                openai_response = format_completions_response(response, model)

                return Response(
                    content=json.dumps(openai_response),
                    media_type="application/json"
                )
        else:
            if stream:
                return StreamingResponse(
                    stream_gaia_response(llm, messages, model, temperature, max_tokens),
                    media_type="text/event-stream"
                )
            else:
                response = await llm.generate(
                    messages=messages,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                # Format for completions endpoint
                openai_response = format_completions_response(response, model)

                return Response(
                    content=json.dumps(openai_response),
                    media_type="application/json"
                )

    except Exception as e:
        logger.error(f"Error processing completions request: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")
