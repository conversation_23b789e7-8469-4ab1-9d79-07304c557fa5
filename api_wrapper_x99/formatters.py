"""
Response formatting functions for API responses.
"""

import asyncio
from typing import Dict, Any

def format_non_streaming_response(response_text: str, model: str) -> Dict[str, Any]:
    """
    Format a non-streaming response to match OpenAI's chat completion format.
    
    Args:
        response_text: The response text from Gaia or LLM
        model: The model name to include in the response
        
    Returns:
        Dict[str, Any]: A dictionary formatted like OpenAI's chat completion response
    """
    current_time = int(asyncio.get_event_loop().time())
    
    return {
        "id": f"chatcmpl-{current_time}",
        "object": "chat.completion",
        "created": current_time,
        "model": model,
        "choices": [
            {
                "index": 0,
                "message": {
                    "role": "assistant", 
                    "content": response_text
                },
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 0,  # We don't have this information from Gaia
            "completion_tokens": 0,  # We don't have this information from Gaia
            "total_tokens": 0  # We don't have this information from Gaia
        }
    }

def format_completions_response(response_text: str, model: str) -> Dict[str, Any]:
    """
    Format a response to match OpenAI's completions format.
    
    Args:
        response_text: The response text from Gaia or LLM
        model: The model name to include in the response
        
    Returns:
        Dict[str, Any]: A dictionary formatted like OpenAI's completions response
    """
    current_time = int(asyncio.get_event_loop().time())
    
    return {
        "id": f"cmpl-{current_time}",
        "object": "text_completion",
        "created": current_time,
        "model": model,
        "choices": [
            {
                "text": response_text,
                "index": 0,
                "finish_reason": "stop"
            }
        ],
        "usage": {
            "prompt_tokens": 0,  # We don't have this information from Gaia
            "completion_tokens": 0,  # We don't have this information from Gaia
            "total_tokens": 0  # We don't have this information from Gaia
        }
    }

def _clean_log_content(content: str) -> str:
    """
    Clean up log content for display to the user.
    
    Args:
        content: The raw log content
        
    Returns:
        str: The cleaned log content, or None if it should be skipped
    """
    # Extract just the meaningful part of the log message
    # Remove timestamp, logger name, and log level
    cleaned_content = content
    
    # Extract the actual message from the log format
    if " - INFO: " in content:
        # Get everything after "INFO: "
        cleaned_content = content.split(" - INFO: ", 1)[1]
    elif " - WARNING: " in content:
        cleaned_content = content.split(" - WARNING: ", 1)[1]
    elif " - ERROR: " in content:
        cleaned_content = content.split(" - ERROR: ", 1)[1]
    
    # Further clean up common patterns
    
    # Clean up progress percentage messages
    if cleaned_content.startswith("["):
        # For progress logs, keep just the percentage and message
        if "%" in cleaned_content and "(time:" in cleaned_content:
            parts = cleaned_content.split("(time:", 1)[0].strip()
            cleaned_content = parts
    
    # Remove technical details about HTTP requests, tokens, etc.
    if "HTTP Request:" in cleaned_content or "chunks, " in cleaned_content:
        return None  # Skip these technical logs entirely
    
    # Skip logs about specific API calls
    if "api.openai.com" in cleaned_content:
        return None  # Skip OpenAI API call logs
    
    # Skip httpx logs
    if cleaned_content.startswith("HTTP Request:") or cleaned_content.startswith("HTTP/1.1"):
        return None
        
    return cleaned_content

def _should_skip_log(log_content: str) -> bool:
    """
    Determine if a log message should be skipped.
    
    Args:
        log_content: The log content to check
        
    Returns:
        bool: True if the log should be skipped, False otherwise
    """
    return any([
        "Using max_tokens=" in log_content,
        "Failed to fetch" in log_content,
        "HTTP Request:" in log_content,
        "HTTP/1.1" in log_content,
        "api.openai.com" in log_content,
        "Starting WebSearcherManager asynchronously" in log_content,
        log_content.startswith("Attempt 1 failed"),
        log_content.startswith("Ignoring wrong pointing object"),
        log_content.startswith("Retrying request to /cha"),
    ])
