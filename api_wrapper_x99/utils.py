"""
Utility functions for request parsing and handling.
"""

from typing import Dict, List, Any, Optional
from fastapi import Request
from config import logger, DEFAULT_SEARCH_TYPE, VALID_SOURCE_TYPES

def check_search_enabled(body: Dict[str, Any]) -> bool:
    """
    Check if web search is enabled in the request.

    This detects search in multiple ways:
    1. Looking for webSearch flag in the request body directly
    2. Checking for webSearch flag in the last message

    Args:
        body: The request body as a dictionary

    Returns:
        bool: True if web search is enabled, False otherwise
    """
    # Direct webSearch flag in body
    if body.get('webSearch', False):
        return True

    # Check the last message for webSearch flag
    messages = body.get('messages', [])
    if messages and len(messages) > 0:
        last_message = messages[-1]
        if isinstance(last_message, dict) and last_message.get('webSearch', False):
            return True

    return False

def check_deep_snipe_enabled(body: Dict[str, Any]) -> bool:
    """
    Check if deep snipe is enabled in the request.

    This detects deep snipe in multiple ways:
    1. Looking for deepSnipe or deep_snipe flag in the request body directly
    2. Checking for deepSnipe or deep_snipe flag in the last message

    Args:
        body: The request body as a dictionary

    Returns:
        bool: True if deep snipe is enabled, False otherwise
    """
    # Direct deep_snipe flag in body (check both snake_case and camelCase)
    if body.get('deep_snipe', False) or body.get('deepSnipe', False):
        return True

    # Check the last message for deep_snipe flag (check both snake_case and camelCase)
    messages = body.get('messages', [])
    if messages and len(messages) > 0:
        last_message = messages[-1]
        if isinstance(last_message, dict) and (
            last_message.get('deep_snipe', False) or
            last_message.get('deepSnipe', False)
        ):
            return True

    return False

def get_source_type(body: Dict[str, Any], request: Optional[Request] = None) -> str:
    """
    Get the source type from the request.

    This detects source type in multiple ways:
    1. Looking for source_type flag in the request body directly (snake_case)
    2. Looking for sourceType flag in the request body (camelCase)
    3. Checking for source_type or sourceType in the last message
    4. Checking for X-ChatUI-Source-Type header
    5. Checking for X-Source-Type header
    6. Defaulting to 'Web' if not found

    Args:
        body: The request body as a dictionary
        request: The FastAPI request object, optional

    Returns:
        str: The source type (Web, Scholar, Video, Image, Internal, Auto)
    """
    # Direct source_type flag in body (snake_case)
    source_type = body.get('source_type', None)
    if source_type:
        return _validate_source_type(source_type)

    # Direct sourceType flag in body (camelCase)
    source_type = body.get('sourceType', None)
    if source_type:
        return _validate_source_type(source_type)

    # Check the last message for source_type flag (both snake_case and camelCase)
    messages = body.get('messages', [])
    if messages and len(messages) > 0:
        last_message = messages[-1]
        # Check for snake_case
        if isinstance(last_message, dict) and last_message.get('source_type'):
            source_type = last_message.get('source_type')
            logger.info(f"Found source_type in last message (snake_case): {source_type}")
            return _validate_source_type(source_type)
        # Check for camelCase
        if isinstance(last_message, dict) and last_message.get('sourceType'):
            source_type = last_message.get('sourceType')
            logger.info(f"Found sourceType in last message (camelCase): {source_type}")
            return _validate_source_type(source_type)

    # Check headers if request is provided
    if request:
        # Check for X-ChatUI-Source-Type header
        source_type_header = request.headers.get('x-chatui-source-type', None)
        if source_type_header:
            logger.info(f"Found source_type in x-chatui-source-type header: {source_type_header}")
            return _validate_source_type(source_type_header)

        # Check for X-Source-Type header
        source_type_header = request.headers.get('x-source-type', None)
        if source_type_header:
            logger.info(f"Found source_type in x-source-type header: {source_type_header}")
            return _validate_source_type(source_type_header)

    # Default to Web if not found
    logger.info("No source_type found, defaulting to Web")
    return DEFAULT_SEARCH_TYPE

def _validate_source_type(source_type: str) -> str:
    """
    Validate and normalize the source type.

    Args:
        source_type: The source type to validate

    Returns:
        str: The validated and normalized source type
    """
    # Convert to title case for consistent formatting
    normalized = source_type.title()

    # Check if it's a valid source type
    if normalized in VALID_SOURCE_TYPES:
        return normalized

    # Special case for 'auto' (case-insensitive)
    if source_type.lower() == 'auto':
        return 'Auto'

    # Log warning and return default if invalid
    logger.warning(f"Invalid source type: {source_type}, defaulting to {DEFAULT_SEARCH_TYPE}")
    return DEFAULT_SEARCH_TYPE

def get_search_query(messages: List[Dict[str, Any]]) -> str:
    """
    Extract the search query from the messages.

    Args:
        messages: List of message dictionaries

    Returns:
        str: The content of the last user message, or empty string if none found
    """
    if not messages:
        return ""

    # Use the content of the last user message as the search query
    for message in reversed(messages):
        if message.get('role', message.get('from', '')) in ['user']:
            return message.get('content', '')

    return ""

async def parse_chat_request(body: Dict[str, Any], request: Request) -> Dict[str, Any]:
    """
    Parse a chat request and extract all relevant parameters.

    Args:
        body: The request body
        request: The FastAPI request object

    Returns:
        Dict[str, Any]: A dictionary of parsed parameters
    """
    # Check feature flags
    search_enabled = check_search_enabled(body)
    if search_enabled:
        logger.info("Search functionality enabled")

    deep_snipe_enabled = check_deep_snipe_enabled(body)
    if deep_snipe_enabled:
        logger.info("Deep snipe functionality enabled")

    # Get source type
    source_type = get_source_type(body, request)
    logger.info(f"Source type: {source_type}")

    # Extract parameters
    messages = body.get('messages', [])
    model = body.get('model', 'gpt-4o')
    temperature = body.get('temperature', 0.7)
    max_tokens = body.get('max_tokens', 1024)
    stream = body.get('stream', False)

    # Get the query from the last message
    query = get_search_query(messages)

    # Priority: If deep_snipe is enabled, use it regardless of web search setting
    # If only web search is enabled, use regular Gaia
    use_deep_snipe = deep_snipe_enabled
    use_search = search_enabled or deep_snipe_enabled

    # Tools support
    tools = body.get('tools', None)
    tool_choice = body.get('tool_choice', None)

    return {
        'messages': messages,
        'model': model,
        'temperature': temperature,
        'max_tokens': max_tokens,
        'stream': stream,
        'query': query,
        'use_deep_snipe': use_deep_snipe,
        'use_search': use_search,
        'source_type': source_type,
        'tools': tools,
        'tool_choice': tool_choice
    }
