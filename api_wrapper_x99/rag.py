import requests
import json
import os 
from gaia.llm.llm import LLM
import logging
from typing import Optional
import asyncio
os.environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"


def get_final_prompt(user_question, context):
    prompt = f"""
## ROLE:
You are a specialized AI assistant, acting as an official knowledge resource for the **Edge AI Foundation**. Your purpose is to synthesize information **exclusively** from the Foundation's internal context provided below to answer user questions accurately and clearly.
**Important Background:** Please be aware that the **Edge AI Foundation** recently (within the past year) rebranded from its previous name, the **'TinyML Foundation'**. The provided context documents may refer to the organization using either name; treat them as referring to the same entity.

## DEFINED SCOPE OF KNOWLEDGE (Reflected in Provided Context):
Your knowledge, derived *only* from the context below, primarily covers:
1.  **The Edge AI Foundation / TinyML Foundation**: Its mission, activities, structure, goals, members (e.g., Pete Bernard), news, events, or related organizational details under both its current and former name.
2.  **Edge AI / TinyML Technology**: Concepts, principles, hardware, software, algorithms, applications, challenges, research, or developments in edge artificial intelligence, including **TinyML**, as documented by the Foundation or its affiliates.
3.  **Related Scientific/Technical Fields**: Topics in AI, Machine Learning, Computer Science, embedded systems, IoT AI, distributed AI, etc., specifically as they relate to the Foundation's work or are discussed in its materials.
4.  **Entities/Concepts within the Context**: Factual information about people, projects, technologies, or concepts mentioned within the provided documents related to the Foundation.

You are *not* equipped to handle queries outside this scope (e.g., creative writing, general knowledge unrelated to these technical domains, personal advice, non-technical figures).

## TASK:
Generate a comprehensive and accurate answer to the user's specific question: "{user_question}", ensuring the answer is **strictly grounded** in the provided context and falls within the **Defined Scope of Knowledge**, recognizing the organization's potential naming variations in the context.

## CONTEXT:
The following context is provided from the **Edge AI Foundation's internal knowledge repositories** (represented via Vector Store and Graph Store), reflecting the defined scope. This context may contain references to both 'Edge AI Foundation' and its former name 'TinyML Foundation'. The Vector Store documents are numbered for citation purposes.

{context}
---
*End of Context*
---

## INSTRUCTIONS:

**1. Context Usage & Grounding:**
    *   **Strict Grounding:** Base your answer **SOLELY** on the information present in the provided CONTEXT section. Do **NOT** use any prior knowledge or external information, **even if the topic falls within the defined scope but is not mentioned in the context documents**. Recognize that 'TinyML Foundation' references pertain to the current Edge AI Foundation.
    *   **Scope Adherence:** Ensure your answer directly addresses the user's question *using only information relevant to the Defined Scope* as found in the context.
    *   **Completeness:** Utilize all relevant information from both Vector Store and Graph Store context snippets within the defined scope to formulate the answer.
    *   **No Assumptions:** Do not make assumptions or infer information not explicitly stated in the context. If the context doesn't provide an answer to an in-scope question, state that the information is not available in the provided Foundation documents.

**2. Source Prioritization & Citation:**
    *   **Conflict Resolution:** If information between the Vector Store and Graph Store context conflicts, prioritize the information from the **Vector Store**. Note significant discrepancies if applicable.
    *   **Vector Store Citation (Re-ordered & Clickable):**
        *   Identify the specific Vector Store document(s) (e.g., `Document N`) supporting each piece of information in your answer.
        *   Assign sequential citation numbers `[1]`, `[2]`, `[3]`, ... based on the *order in which you first cite each unique document* in your answer.
        *   Place the assigned sequential number in square brackets (e.g., `[1]`) immediately following the relevant sentence or phrase in the main body of your answer. If you cite the same document multiple times, use the *same* sequential number assigned on its first use.
        *   Do **not** use the original `Document N` number in the main text citations.
    *   **Graph Store Usage:** Integrate information derived from the Graph Store context (representing relationships/entities within the Foundation's scope) naturally into the answer without numerical citation.
    *   **Sources Section:** At the **very end** of your response, include a section titled "**Sources:**". List *only* the Vector Store documents you cited in your answer. Each entry should map the sequential citation number used in the text to the original Document ID and its clickable URL using Markdown link format. Follow this format precisely:
        ```
        **Sources:**
        [1] Document [Original_Doc_ID_1] ([URL_1])
        [2] Document [Original_Doc_ID_2] ([URL_2])
        [3] Document [Original_Doc_ID_3] ([URL_3])
        ...
        ```
        Replace `[Original_Doc_ID_X]` with the actual ID (e.g., `1`, `8`) from the context, and `[URL_X]` with the corresponding URL provided for that document in the context. Ensure the list follows the sequential order `[1], [2], [3], ...`.

**3. Answer Formatting & Style:**
    *   **Clarity & Conciseness:** Provide a clear, well-organized answer specific to the Edge AI Foundation (using its current name in your response unless quoting directly) and related topics within the scope. Define technical terms using the context if possible.
    *   **Length:** Be detailed enough to be helpful and specific, incorporating key details from the context related to the Foundation and its scope, but avoid unnecessary length or generic statements. Focus on answering "{user_question}" based *only* on the provided information.
    *   **Tone:** Maintain a neutral, objective, and informative tone, reflecting the official voice of the Edge AI Foundation's knowledge base.
    *   **Structure:** Use markdown (headings, lists, bolding) for logical structure.
    *   **Accuracy:** Ensure the answer accurately reflects the Foundation's context, correctly associating information regardless of whether the old or new name was used in the source document.

**4. What NOT to Include:**
    *   Internal mechanisms (e.g., "Based on Document 1 which mentioned the TinyML Foundation..."). Just provide the information and citation `[1]`.
    *   Information *not* found in the CONTEXT section, regardless of scope relevance.
    *   Apologies for missing information; state unavailability in the provided documents.
    *   Introductory/concluding remarks beyond the direct answer.
    *   A separate reference list.

## USER QUESTION (Regarding Edge AI Foundation Scope):
"{user_question}"

## ANSWER (Based strictly on the provided Edge AI Foundation context within the defined scope, recognizing the potential use of 'TinyML Foundation' name, and using numerical citations for Vector Store documents):
"""
    return prompt

def build_context(vector_context, graph_context):
    context_I = ""
    context_I+= f"**Vector Store Context**\n"
    context_I+= f"{vector_context}"
    context_I+= "\n\n"
    context_I+= f"**Graph Store Context**\n"
    context_I+= f"{graph_context}"
    context_I+= "\n\n"

    return context_I

# def match_filename_from_list(filename):
#     # Get the base filename without path
#     base_filename = os.path.basename(filename)
    
#     # Read the pdf links file
#     with open('5-inference/pdf_links.txt', 'r') as f:
#         pdf_links = f.readlines()
    
#     # Clean up links and remove empty lines
#     pdf_links = [link.strip() for link in pdf_links if link.strip()]
    
#     # For each link, check if the filename matches the end part
#     for link in pdf_links:
#         link_filename = link.split('/')[-1]
#         if link_filename.lower() == base_filename.lower():
#             return link
            
#     # If no match found, return the original filename
#     return filename

# def post_process(input, metadata):
#     # Replace document citations with metadata if they exist
#     print(input)
#     print()
#     print()
#     print(metadata)
#     for i, meta in enumerate(metadata):
#         meta = json.loads(meta)
#         if isinstance(meta.get('filename'), dict) and 'Source' in meta['filename']:
            
#             meta = meta['filename']['Source'] if meta['filename']['Source'] is not None else ""
#         else:
#             meta = meta.get('filename', '')
#             if meta.endswith('_cleaned.md'):
#                 meta = meta[:-11] + '.pdf'  # Remove '_cleaned.md' and add '.pdf'
#                 meta = match_filename_from_list(meta)
#         citation = f"**document {i+1}**" if f"**document {i+1}**" in input else f"**document {i+1}:**" if f"**document {i+1}:**" in input else f"*document {i+1}:*"
#         if citation in input:
#             rep = f"[{i+1}]({meta})" if meta else ""
#             input = input.replace(citation, rep)
#     return input



def query_flask_app(prompt, url):
    headers = {'Content-Type': 'application/json'}
    data = {'prompt': prompt}
    response = requests.post(url, headers=headers, data=json.dumps(data))
    if response.status_code == 200:
        response_data = response.json()
        return response_data.get('response'), response_data.get('relevant_docs', [])
    else:
        print(f"Error: {response.status_code}")
        return None, []




def get_context(prompt):

    context_vector = query_flask_app(prompt, 'http://78.47.100.16:5044/query')
    context_graph = query_flask_app(prompt, 'http://78.47.100.16:5013/query')
    # print(context_graph)
    # print('--------------------------------')


    context = build_context(context_vector[0], context_graph[0])
    # print(context)

    return context



    #build the prompt
    #stream response


    # stream = f'graph: \n graph: \n{context_graph} \n vector: \n {context_vector}'
    #stream = rag_chain.invoke({"context_I": context_I, "user_question":prompt})
    #stream = post_process(stream, context_vector['metadata'])
    #print(stream)
    #return jsonify({"answer": stream})

async def test_generation(
    provider: str, 
    model: str, 
    prompt: str,
    streaming: bool = False, 
    max_tokens: Optional[int] = 250,
):
    """Test LLM generation with the specified provider and model."""
    logging.info(f"Testing {provider} provider with model {model}")
    logging.info(f"Prompt: {prompt}")
    logging.info(f"Streaming: {streaming}")
    
    # Initialize LLM with the specified provider
    llm = LLM(provider=provider)
    
    # Prepare messages
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant."},
        {"role": "user", "content": prompt}
    ]
    
    # Generate response
    if streaming:
        logging.info("Streaming response:")
        print("\n--- Streaming Response ---")
        full_response = ""
        async for chunk in llm.stream_generate(
            messages=messages,
            model=model,
            # max_tokens=max_tokens
        ):
            print(chunk, end="", flush=True)
            full_response += chunk
        print("\n------------------------")
        return full_response
    else:
        logging.info("Generating complete response:")
        response = await llm.generate(
            messages=messages,
            model=model,
            # max_tokens=max_tokens
        )
        print("\n--- Complete Response ---")
        print(response)
        print("------------------------")
        return response
    


async def main():
    #print(match_filename_from_list('PankratiusVictor.pdf'))
    user_question = "Moshe Haiut is a principle senior engineer in the CTO team at DSP Group, Herzliya, Israel. His main areas of expertise are communication, video and audio digital signal processing, digital hardware architecture, and neural networks."
    context = get_context(user_question)
    # print(context)
    final_prompt = get_final_prompt(user_question, context)


    await test_generation(
        provider='openai',
        model='o3-mini',
        prompt=final_prompt,
        streaming=True,
        # max_tokens=8000
    )

if __name__ == '__main__':
    asyncio.run(main())



