# Custom OpenAI-Compatible Endpoint

This is a lightweight FastAPI server that acts as a proxy between the chat-ui application and OpenAI's API. It provides a compatible interface that mimics OpenAI's API structure, allowing the chat-ui to send requests through it.

## Features

- Proxies requests to OpenAI's API
- Supports streaming responses
- Handles chat completions and text completions
- Compatible with the chat-ui's expected format
- Basic error handling and logging
- CORS support for browser-based requests

## Setup Instructions

### 1. Install Requirements

```bash
pip install fastapi uvicorn openai pydantic
```

### 2. Set up your API key

Set your OpenAI API key as an environment variable:

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

Alternatively, you can modify the `custom_endpoint.py` file to use your API key directly.

### 3. Run the server

```bash
python custom_endpoint.py
```

The server will start on port 8000 by default. You can change the port by setting the `PORT` environment variable.

### 4. Configure chat-ui to use the custom endpoint

Update your `.env.local` file to point to your local endpoint:

```
MODELS=`[
  {
    "name": "gpt-4o",
    "id": "gpt-4o",
    "displayName": "GPT-4o",
    "tokenizer": "gpt2",
    "parameters": {
      "temperature": 0.7,
      "top_p": 0.95,
      "max_tokens": 1024,
      "frequency_penalty": 0,
      "presence_penalty": 0
    },
    "endpoints": [{
      "type": "openai",
      "baseURL": "http://localhost:8000/v1",
      "completion": "chat_completions",
      "defaultHeaders": {
        "Content-Type": "application/json",
        "Accept": "application/json"
      }
    }]
  }
]`
```

Note that we've changed the `baseURL` to point to your local server instead of the official OpenAI API.

## How It Works

1. The chat-ui sends requests to your local endpoint (`http://localhost:8000/v1/...`)
2. The custom endpoint formats and forwards these requests to OpenAI's API
3. Responses from OpenAI are streamed back to the chat-ui in real-time
4. The endpoint handles all necessary formatting and protocol requirements

## Implementation Notes

### Parameter Handling

The endpoint carefully handles request parameters to avoid conflicts. For streaming requests, it:
1. Creates a copy of the request body
2. Removes the `stream` parameter from the copy
3. Explicitly adds `stream=True` when calling the OpenAI API

This prevents the "multiple values for keyword argument 'stream'" error that would occur if the parameter was specified twice.

## Customization Options

### Change the port

```bash
export PORT=9000
python custom_endpoint.py
```

### Add custom processing

You can modify the `custom_endpoint.py` file to add custom processing logic before sending requests to OpenAI or before returning responses to the chat-ui.

### Mock responses

For testing purposes, you can modify the endpoint to return mock responses instead of calling OpenAI's API.

## Troubleshooting

### Enable debug logging

The server logs all requests and errors by default. Check the console output for debugging information.

### Check CORS settings

If you're experiencing CORS issues, update the CORS middleware configuration in `custom_endpoint.py` to match your chat-ui's domain.

### Test the endpoint directly

You can test the endpoint directly using curl:

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "gpt-4o", "messages": [{"role": "user", "content": "Hello"}], "stream": true}'
```

## Security Considerations

This is a basic implementation intended for local development. For production use, consider:

- Implementing proper authentication
- Using TLS/HTTPS
- Restricting CORS to specific domains
- Adding rate limiting
- Implementing proper error handling and logging 