#!/usr/bin/env python3
"""
Debug script to compare our requests with curl commands
"""
import requests
import time
import json

def test_service(name, url, params=None, timeout=10):
    """Test a service and compare with curl"""
    print(f"\n🔧 Testing {name}...")
    print(f"URL: {url}")
    print(f"Params: {params}")
    
    try:
        start_time = time.time()
        response = requests.get(url, params=params, timeout=timeout)
        end_time = time.time()
        
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Time: {end_time - start_time:.2f}s")
        print(f"📦 Content-Type: {response.headers.get('content-type', 'unknown')}")
        print(f"📏 Content-Length: {len(response.text)} chars")
        
        if response.headers.get('content-type', '').startswith('application/json'):
            try:
                data = response.json()
                print(f"🔍 JSON keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                return data
            except:
                print("❌ Failed to parse JSON")
                print(f"Raw response: {response.text[:200]}...")
        else:
            print(f"Raw response: {response.text[:200]}...")
            
        return response.text
        
    except requests.exceptions.Timeout:
        print(f"⏰ TIMEOUT after {timeout}s")
        return None
    except requests.exceptions.ConnectionError as e:
        print(f"🔌 CONNECTION ERROR: {e}")
        return None
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None

def main():
    """Test various services"""
    
    # Test root endpoints first
    print("=" * 60)
    print("TESTING ROOT ENDPOINTS")
    print("=" * 60)
    
    services = [
        ("Etsy", "http://78.47.100.16:9292/"),
        ("Nike", "http://78.47.100.16:9296/"),
        ("Amazon", "http://78.47.100.16:9280/"),
        ("Cars", "http://78.47.100.16:9290/"),
        ("Flights", "http://78.47.100.16:9282/"),
    ]
    
    for name, url in services:
        test_service(name, url, timeout=5)
    
    # Test search endpoints with short timeout
    print("\n" + "=" * 60)
    print("TESTING SEARCH ENDPOINTS (SHORT TIMEOUT)")
    print("=" * 60)
    
    search_tests = [
        ("Cars", "http://78.47.100.16:9290/search", None),  # POST method
        ("Flights", "http://78.47.100.16:9282/flights/search", {"from_airport": "JFK", "to_airport": "LAX", "date": "2025-07-01"}),
    ]
    
    for name, url, params in search_tests:
        if name == "Cars":
            # Cars uses POST
            print(f"\n🔧 Testing {name} (POST)...")
            try:
                response = requests.post(url, json={"query": "toyota"}, timeout=5)
                print(f"✅ Status: {response.status_code}")
                print(f"📦 Response: {response.text[:200]}...")
            except Exception as e:
                print(f"❌ ERROR: {e}")
        else:
            test_service(name, url, params, timeout=5)
    
    # Test Zyte services with very short timeout
    print("\n" + "=" * 60)
    print("TESTING ZYTE SERVICES (VERY SHORT TIMEOUT)")
    print("=" * 60)
    
    zyte_key = "54a018dfd57d4a50bfc8792da44c122a"
    zyte_tests = [
        ("Etsy", "http://78.47.100.16:9292/search", {"query": "test", "zyte_api_key": zyte_key}),
        ("Nike", "http://78.47.100.16:9296/search", {"query": "shoes", "zyte_api_key": zyte_key}),
        ("Amazon", "http://78.47.100.16:9280/search", {"query": "laptop", "zyte_api_key": zyte_key}),
    ]
    
    for name, url, params in zyte_tests:
        test_service(name, url, params, timeout=3)

if __name__ == "__main__":
    main()
