#!/usr/bin/env python3
"""
Test script for OpenRouter with Gemini models.
"""
import asyncio
import os
import logging
from llm import LLM

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def main():
    """Test OpenRouter with Gemini models."""
    # Create an LLM instance with OpenRouter provider
    llm = LLM(provider="openrouter")
    
    # Test message
    messages = [
        {"role": "user", "content": "What is the capital of France? Keep it brief."}
    ]
    
    # Test with Gemini model
    model = "google/gemini-2.5-pro-preview"
    
    print(f"\nTesting OpenRouter with {model}...")
    print("This should only pass the model and messages, without any additional parameters.")
    
    try:
        # Generate content
        response = await llm.generate(
            messages=messages,
            model=model
        )
        
        print("\nResponse:")
        print(response)
        
        # Test streaming
        print("\nTesting streaming with the same model...")
        print("Response (streaming):")
        
        async for chunk in llm.stream_generate(
            messages=messages,
            model=model
        ):
            print(chunk, end="", flush=True)
        
        print("\n\nStreaming complete.")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
