#!/usr/bin/env python
"""
Helper script to run the LLM tests.
Provides a simple command-line interface to test specific LLM providers.
"""
import os
import sys
import argparse
import subprocess

def run_test(test_type, provider=None, model=None, streaming=False):
    """Run the specified test with the given parameters."""
    if test_type == "all":
        cmd = [sys.executable, "test_all.py"]
    else:
        cmd = [sys.executable, "test_llm.py"]
        if provider:
            cmd.extend(["--provider", provider])
        if model:
            cmd.extend(["--model", model])
        if streaming:
            cmd.append("--stream")
    
    print(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description="Run LLM tests")
    parser.add_argument(
        "test_type",
        choices=["single", "all"],
        default="single",
        help="Run a single provider test or test all providers"
    )
    parser.add_argument(
        "--provider",
        choices=["openai", "deepseek", "minimax"],
        default="openai",
        help="LLM provider to test (for single test mode)"
    )
    parser.add_argument(
        "--model",
        help="Model to test (for single test mode)"
    )
    parser.add_argument(
        "--stream",
        action="store_true",
        help="Use streaming mode (for single test mode)"
    )
    
    args = parser.parse_args()
    
    # Change to the directory of this script
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    run_test(
        test_type=args.test_type,
        provider=args.provider,
        model=args.model,
        streaming=args.stream
    )

if __name__ == "__main__":
    main() 