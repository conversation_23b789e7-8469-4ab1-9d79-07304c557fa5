#!/usr/bin/env python3
"""
Test script for template variable resolution.

This script tests the enhanced reference resolution capabilities
for template variables in the format {{variable_name}}.
"""

import os
import sys
import json
import logging
import asyncio
from typing import Dict, Any, List, Set

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the necessary modules
from gaiav2.utils.reference_resolver import ReferenceResolver
from gaiav2.execution.tool_executor import ToolExecutor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_results() -> Dict[str, Any]:
    """Create sample results for testing."""
    return {
        "results_of_t1_select_cities": {
            "status": "success",
            "tool_name": "rank_data",
            "timestamp": "2025-05-10T12:34:56Z",
            "request_id": "rd-123456",
            "execution_time": 1.2,
            "data": {
                "ranked_items": [
                    {
                        "rank": 1,
                        "item": {
                            "name": "Bangkok",
                            "country": "Thailand",
                            "description": "Capital city of Thailand"
                        },
                        "score": 95,
                        "explanation": "Most popular tourist destination in Thailand"
                    },
                    {
                        "rank": 2,
                        "item": {
                            "name": "Chiang Mai",
                            "country": "Thailand",
                            "description": "Cultural city in northern Thailand"
                        },
                        "score": 90,
                        "explanation": "Rich cultural heritage and beautiful temples"
                    },
                    {
                        "rank": 3,
                        "item": {
                            "name": "Phuket",
                            "country": "Thailand",
                            "description": "Island in southern Thailand"
                        },
                        "score": 85,
                        "explanation": "Beautiful beaches and vibrant nightlife"
                    },
                    {
                        "rank": 4,
                        "item": {
                            "name": "Krabi",
                            "country": "Thailand",
                            "description": "Province in southern Thailand"
                        },
                        "score": 80,
                        "explanation": "Stunning limestone cliffs and clear waters"
                    },
                    {
                        "rank": 5,
                        "item": {
                            "name": "Ayutthaya",
                            "country": "Thailand",
                            "description": "Historical city in central Thailand"
                        },
                        "score": 75,
                        "explanation": "Ancient ruins and historical significance"
                    }
                ],
                "top_cities": [
                    "Bangkok",
                    "Chiang Mai",
                    "Phuket",
                    "Krabi",
                    "Ayutthaya"
                ],
                "city_1": "Bangkok",
                "city_2": "Chiang Mai",
                "city_3": "Phuket",
                "city_4": "Krabi",
                "city_5": "Ayutthaya"
            }
        }
    }

def create_sample_action_map() -> Dict[str, Dict[str, Any]]:
    """Create a sample action map for testing."""
    return {
        "t1_select_cities": {
            "action": "rank_data",
            "parameters": {
                "input_data": "Thailand cities",
                "criteria": "popularity, attractions, and cultural significance"
            },
            "name": "Select top cities in Thailand"
        },
        "t2_search_hotels_city5": {
            "action": "search_hotel",
            "parameters": {
                "destination": "{{top_city_5}}",
                "check_in": "2025-10-01",
                "check_out": "2025-10-30",
                "adults": 1,
                "currency": "USD"
            },
            "name": "Search hotels in the 5th top city"
        },
        "t3_search_hotels_city5_alt": {
            "action": "search_hotel",
            "parameters": {
                "destination": "results_of_t1_select_cities.data.city_5",
                "check_in": "2025-10-01",
                "check_out": "2025-10-30",
                "adults": 1,
                "currency": "USD"
            },
            "name": "Search hotels in the 5th top city (alternative reference)"
        },
        "t4_search_hotels_ranked_city5": {
            "action": "search_hotel",
            "parameters": {
                "destination": "results_of_t1_select_cities.data.ranked_items[4].item.name",
                "check_in": "2025-10-01",
                "check_out": "2025-10-30",
                "adults": 1,
                "currency": "USD"
            },
            "name": "Search hotels in the 5th ranked city"
        }
    }

def test_reference_resolver():
    """Test the reference resolver with template variables."""
    # Create sample results
    results = create_sample_results()
    
    # Create a reference resolver
    resolver = ReferenceResolver(results)
    
    # Test template variables
    test_templates = [
        "{{top_city_5}}",
        "{{city_5}}",
        "The 5th top city is {{city_5}}",
        "Visit {{city_1}}, {{city_2}}, and {{city_3}}",
        "{{non_existent_variable}}"
    ]
    
    print("Testing Template Variable Resolution\n")
    print("=" * 80)
    
    for template in test_templates:
        print(f"\nTemplate: {template}")
        result = resolver.resolve_template_variables(template)
        print(f"Result: {result}")
    
    print("\n" + "=" * 80)

async def test_tool_executor():
    """Test the tool executor with template variables."""
    # Create sample results
    results = create_sample_results()
    
    # Create a sample action map
    action_map = create_sample_action_map()
    
    # Create a sample execution schedule
    execution_schedule = [
        {"t1_select_cities"},
        {"t2_search_hotels_city5", "t3_search_hotels_city5_alt", "t4_search_hotels_ranked_city5"}
    ]
    
    # Create a tool executor
    tool_executor = ToolExecutor(execution_schedule, action_map)
    
    # Add the sample results to the tool executor
    tool_executor.results = results
    
    # Process parameters for each task
    print("\nTesting Parameter Processing in Tool Executor\n")
    print("=" * 80)
    
    for task_id, task_info in action_map.items():
        if task_id == "t1_select_cities":
            # Skip the first task since we already have its results
            continue
            
        print(f"\nTask: {task_id} - {task_info['name']}")
        print(f"Original parameters: {task_info['parameters']}")
        
        # Process the parameters
        processed_params = tool_executor._process_parameters(task_info['parameters'])
        
        print(f"Processed parameters: {processed_params}")
        
        # Check if the destination parameter was resolved correctly
        if "destination" in processed_params:
            print(f"Destination resolved to: {processed_params['destination']}")
            
            # Check if it matches the expected value
            expected = "Ayutthaya"
            if processed_params['destination'] == expected:
                print(f"✅ Correctly resolved to '{expected}'")
            else:
                print(f"❌ Failed to resolve correctly. Expected '{expected}', got '{processed_params['destination']}'")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    # Test the reference resolver
    test_reference_resolver()
    
    # Test the tool executor
    asyncio.run(test_tool_executor())
