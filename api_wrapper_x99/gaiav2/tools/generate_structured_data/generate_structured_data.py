"""
Generate Structured Data module.

This module provides a simple interface to the Generate Structured Data tool.
The tool generates structured data files (JSON, CSV, Markdown) from input data.

All LLM tools in this package use a standardized output format:
{
  "status": "success|error",
  "tool_name": "generate_structured_data",
  "timestamp": "ISO-8601 timestamp",
  "request_id": "unique_request_id",
  "execution_time": seconds,
  "metadata": {
    "output_type": "json|csv|markdown",
    "output_filename": "filename",
    "num_pages": number of pages (if applicable),
    "input_data_type": "type of input data",
    "model_used": "model name"
  },
  "data": {
    "file_path": "/path/to/output/file",
    "content_preview": "first few lines of the generated content",
    "structure_description": "description of the structure",
    "file_stats": {
      "size_bytes": file size in bytes,
      "line_count": number of lines in the file
    }
  }
}
"""

from typing import Dict, Any, Optional

from .generate_structured_data_tool import GenerateStructuredDataTool

# Create a default instance
_default_tool = GenerateStructuredDataTool()

async def generate_structured_data(input_data: Any,
                           output_type: str = "json",
                           output_filename: str = "output",
                           num_pages: Optional[int] = None) -> Dict[str, Any]:
    """
    Generate a structured data file from input data.

    Args:
        input_data: Any input data to structure
        output_type: Type of output file (json, csv, markdown)
        output_filename: Name of the output file (without extension)
        num_pages: Number of pages for paginated output (optional)

    Returns:
        Standardized output dictionary with the following structure:
        {
          "status": "success|error",
          "tool_name": "generate_structured_data",
          "timestamp": "ISO-8601 timestamp",
          "request_id": "unique_request_id",
          "execution_time": seconds,
          "metadata": {
            "output_type": "json|csv|markdown",
            "output_filename": "filename",
            "num_pages": number of pages (if applicable),
            "input_data_type": "type of input data",
            "model_used": "model name"
          },
          "data": {
            "file_path": "/path/to/output/file",
            "content_preview": "first few lines of the generated content",
            "structure_description": "description of the structure",
            "file_stats": {
              "size_bytes": file size in bytes,
              "line_count": number of lines in the file
            }
          }
        }
    """
    return await _default_tool.generate_structured_data(
        input_data=input_data,
        output_type=output_type,
        output_filename=output_filename,
        num_pages=num_pages
    )


__all__ = ['generate_structured_data']