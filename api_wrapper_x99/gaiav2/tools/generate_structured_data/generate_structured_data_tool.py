#!/usr/bin/env python3
"""
Generate Structured Data Tool.

This tool generates structured data files (e.g., JSON, CSV) from input data using LLM.
"""

import os
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from pathlib import Path

try:
    # When imported as a module
    from ..llm.base import <PERSON><PERSON>rovider
    from ..base_tool import BaseTool
    from ..llm_tool_base import LLMToolBase
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm.base import <PERSON><PERSON>rovider
    from base_tool import BaseTool
    from llm_tool_base import LLMToolBase


class GenerateStructuredDataTool(LLMToolBase):
    """
    Tool for generating structured data files from input data using LLM.
    """

    def __init__(self, output_dir: str = "output", env_file: str = None):
        """
        Initialize the Generate Structured Data tool.

        Args:
            output_dir: Directory to save generated files
            env_file: Path to .env file
        """
        super().__init__("generate_structured_data", env_file)
        # Set environment file
        self.env_file = env_file

        # Set output directory
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Placeholder for compatibility.
        Not used for this tool.
        """
        raise NotImplementedError("This tool doesn't support search operation")

    async def process(self,
             input_data: Any,
             output_type: str = "json",
             output_filename: str = "output",
             num_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        Process the input data and generate a structured data file.

        This implements the abstract method from LLMToolBase.

        Args:
            input_data: Any input data to structure
            output_type: Type of output file (json, csv, markdown)
            output_filename: Name of the output file (without extension)
            num_pages: Number of pages for paginated output (optional)

        Returns:
            Standardized output dictionary
        """
        return await self.execute_tool(
            self._generate_structured_data,
            input_data=input_data,
            output_type=output_type,
            output_filename=output_filename,
            num_pages=num_pages
        )

    async def generate_structured_data(self,
                                input_data: Any,
                                output_type: str = "json",
                                output_filename: str = "output",
                                num_pages: Optional[int] = None) -> Dict[str, Any]:
        """
        Generate a structured data file from input data.

        This is the public API method that maintains backward compatibility.

        Args:
            input_data: Any input data to structure
            output_type: Type of output file (json, csv, markdown)
            output_filename: Name of the output file (without extension)
            num_pages: Number of pages for paginated output (optional)

        Returns:
            Standardized output dictionary
        """
        return await self.process(input_data, output_type, output_filename, num_pages)

    async def _generate_structured_data(self,
                                 input_data: Any,
                                 output_type: str = "json",
                                 output_filename: str = "output",
                                 num_pages: Optional[int] = None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Internal implementation of generate_structured_data.

        Args:
            input_data: Any input data to structure
            output_type: Type of output file (json, csv, markdown)
            output_filename: Name of the output file (without extension)
            num_pages: Number of pages for paginated output (optional)

        Returns:
            Tuple of (data, metadata)
        """
        # Convert input_data to string if it's not already
        if isinstance(input_data, dict) or isinstance(input_data, list):
            data_str = json.dumps(input_data, indent=2)
        else:
            data_str = str(input_data)

        # Create the assistant prompt
        assistant_prompt = f"I am an AI assistant that helps generate structured {output_type} data from input information."

        # Create the user prompt
        user_prompt = f"""
Please analyze the following data and convert it into well-structured {output_type} format.
The output should be properly formatted and ready to be saved to a file.

DATA:
{data_str}

{f"Please structure the output into approximately {num_pages} pages or sections." if num_pages else ""}

Please provide ONLY the {output_type} content without any additional explanation or markdown code blocks.

Additionally, please provide a brief description of the structure you've created and any key insights or patterns you've identified in the data.
"""

        # Generate content using the LLM
        generated_content = await self.generate_content(assistant_prompt, user_prompt)

        # Extract the structured content and any description/insights
        # For most formats, the description will be in comments or at the beginning/end
        structured_content, description = self._extract_content_and_description(generated_content, output_type)

        # Ensure the output directory exists
        os.makedirs(self.output_dir, exist_ok=True)

        # Determine file extension
        extension = output_type.lower()
        if extension == "markdown":
            extension = "md"

        # Create full file path
        file_path = os.path.join(self.output_dir, f"{output_filename}.{extension}")

        # Save the content to a file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(structured_content)

        # Get file stats
        file_size = os.path.getsize(file_path)
        file_lines = len(structured_content.splitlines())

        # Create a preview of the content (first few lines)
        preview_lines = structured_content.splitlines()[:10]
        content_preview = "\n".join(preview_lines)
        if len(preview_lines) < len(structured_content.splitlines()):
            content_preview += "\n..."

        # Prepare the data output
        data = {
            "file_path": file_path,
            "content_preview": content_preview,
            "structure_description": description,
            "file_stats": {
                "size_bytes": file_size,
                "line_count": file_lines
            }
        }

        # Prepare metadata
        metadata = {
            "output_type": output_type,
            "output_filename": output_filename,
            "num_pages": num_pages,
            "input_data_type": type(input_data).__name__,
            "model_used": self.default_model
        }

        return data, metadata

    def _extract_content_and_description(self, generated_content: str, output_type: str) -> Tuple[str, str]:
        """
        Extract the structured content and description from the generated content.

        Args:
            generated_content: The content generated by the LLM
            output_type: The type of output (json, csv, markdown)

        Returns:
            Tuple of (structured_content, description)
        """
        # Default values
        structured_content = generated_content
        description = ""

        # Look for description based on output type
        if output_type.lower() == "json":
            # Check for JSON comments or text before/after JSON
            if "/*" in generated_content and "*/" in generated_content:
                # Extract comment
                comment_start = generated_content.find("/*")
                comment_end = generated_content.find("*/", comment_start) + 2
                description = generated_content[comment_start:comment_end].strip()
                structured_content = generated_content.replace(description, "").strip()
            elif "//" in generated_content:
                # Extract single-line comments
                lines = generated_content.splitlines()
                comment_lines = [line for line in lines if line.strip().startswith("//")]
                description = "\n".join(comment_lines)
                structured_content = "\n".join([line for line in lines if not line.strip().startswith("//")])

        elif output_type.lower() == "csv":
            # Check for description in comments or text before/after CSV
            lines = generated_content.splitlines()
            if any(line.startswith("#") for line in lines):
                # Extract comment lines
                comment_lines = [line[1:].strip() for line in lines if line.startswith("#")]
                description = "\n".join(comment_lines)
                structured_content = "\n".join([line for line in lines if not line.startswith("#")])

        elif output_type.lower() == "markdown":
            # Look for a section that might be a description
            if "## Description" in generated_content:
                # Extract description section
                desc_start = generated_content.find("## Description")
                next_section = generated_content.find("##", desc_start + 1)
                if next_section > 0:
                    description = generated_content[desc_start:next_section].strip()
                    structured_content = generated_content.replace(description, "").strip()
                else:
                    description = generated_content[desc_start:].strip()
                    structured_content = generated_content[:desc_start].strip()

        # If no description was found, use a generic one
        if not description:
            description = f"Structured {output_type.upper()} data generated from the input."

        return structured_content, description


# Example usage
if __name__ == "__main__":
    import asyncio

    async def main():
        # Create the Generate Structured Data tool
        tool = GenerateStructuredDataTool()

        # Example input data
        example_data = {
            "trip_details": {
                "destination": "Paris, France",
                "duration": "7 days",
                "travelers": 2
            },
            "flights": [
                {
                    "airline": "Air France",
                    "departure": "2023-12-01 08:00",
                    "arrival": "2023-12-01 15:30",
                    "price": "€450"
                },
                {
                    "airline": "Lufthansa",
                    "departure": "2023-12-01 10:00",
                    "arrival": "2023-12-01 17:45",
                    "price": "€420"
                }
            ],
            "accommodations": [
                {
                    "name": "Hotel de Paris",
                    "price": "€180/night",
                    "rating": 4.5,
                    "location": "City Center"
                },
                {
                    "name": "Montmartre Apartments",
                    "price": "€120/night",
                    "rating": 4.2,
                    "location": "Montmartre"
                }
            ]
        }

        try:
            # Generate JSON file
            json_result = await tool.generate_structured_data(
                input_data=example_data,
                output_type="json",
                output_filename="paris_trip"
            )

            print("Generated JSON File (Standardized Output Format):")
            print(f"Status: {json_result['status']}")
            print(f"Tool: {json_result['tool_name']}")
            print(f"Request ID: {json_result['request_id']}")
            print(f"Execution Time: {json_result['execution_time']:.2f} seconds")

            # Print file information
            if json_result["status"] == "success":
                print(f"\nFile Path: {json_result['data']['file_path']}")
                print(f"File Size: {json_result['data']['file_stats']['size_bytes']} bytes")
                print(f"Line Count: {json_result['data']['file_stats']['line_count']} lines")

                print(f"\nStructure Description: {json_result['data']['structure_description']}")

                print("\nContent Preview:")
                print(json_result['data']['content_preview'])

            # Generate CSV file
            csv_result = await tool.generate_structured_data(
                input_data=example_data,
                output_type="csv",
                output_filename="paris_trip"
            )
            print(f"\nGenerated CSV file: {csv_result['data']['file_path']}")

            # Generate Markdown file with pages
            md_result = await tool.generate_structured_data(
                input_data=example_data,
                output_type="markdown",
                output_filename="paris_trip",
                num_pages=3
            )
            print(f"\nGenerated Markdown file: {md_result['data']['file_path']}")

            # Save the full result to a file for inspection
            with open('structured_data_example.json', 'w') as f:
                json.dump(json_result, f, indent=2)
            print("\nFull result saved to 'structured_data_example.json'")

        except Exception as e:
            print(f"Error: {e}")

    # Run the async main function
    asyncio.run(main())
