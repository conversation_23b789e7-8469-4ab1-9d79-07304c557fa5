#!/usr/bin/env python3
"""
Maps search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class MapsTool(BaseTool):
    """
    Tool for searching places and getting directions using the Maps API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                google_maps_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Maps search tool.

        Args:
            base_url: Base URL for the Maps API
            timeout: Request timeout in seconds
            google_maps_api_key: Google Maps API key
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("MAPS_BASE_URL", "http://78.47.100.16:9284")
        
        # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.api_key = google_maps_api_key or os.environ.get("GOOGLE_MAPS_API_KEY", "")

    def search_places(self,
                     query: str,
                     lat: Optional[float] = None,
                     lng: Optional[float] = None,
                     radius: int = 5000,
                     place_type: Optional[str] = None,
                     min_price: Optional[int] = None,
                     max_price: Optional[int] = None,
                     open_now: Optional[bool] = None,
                     language: Optional[str] = None,
                     debug: bool = False,
                     **kwargs) -> Dict[str, Any]:
        """
        Search for places.

        Args:
            query: Search query (e.g., 'restaurants in San Francisco')
            lat: Latitude for the search location
            lng: Longitude for the search location
            radius: Search radius in meters (max 50000)
            place_type: Type of place to search for
            min_price: Minimum price level (0-4)
            max_price: Maximum price level (0-4)
            open_now: Return only places that are open now
            language: The language in which to return results
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Place search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "radius": radius,
            "debug": debug
        }

        # Add optional parameters if provided
        if lat is not None and lng is not None:
            params["lat"] = lat
            params["lng"] = lng

        if place_type:
            params["place_type"] = place_type

        if min_price is not None:
            params["min_price"] = min_price

        if max_price is not None:
            params["max_price"] = max_price

        if open_now is not None:
            params["open_now"] = open_now

        if language:
            params["language"] = language

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/places/search", params=params)

    def get_place_details(self,
                         place_id: str,
                         fields: Optional[str] = None,
                         language: Optional[str] = None,
                         debug: bool = False,
                         **kwargs) -> Dict[str, Any]:
        """
        Get detailed information about a specific place.

        Args:
            place_id: Place ID
            fields: Comma-separated list of fields to include
            language: The language in which to return results
            debug: Enable debug mode
            **kwargs: Additional parameters

        Returns:
            Place details
        """
        # Prepare request parameters
        params = {
            "debug": debug
        }

        # Add optional parameters if provided
        if fields:
            params["fields"] = fields

        if language:
            params["language"] = language

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", f"/places/details/{place_id}", params=params)

    def get_directions(self,
                      origin: str,
                      destination: str,
                      mode: str = "driving",
                      waypoints: Optional[str] = None,
                      alternatives: Optional[bool] = None,
                      avoid: Optional[str] = None,
                      language: Optional[str] = None,
                      units: Optional[str] = None,
                      arrival_time: Optional[int] = None,
                      departure_time: Optional[int] = None,
                      debug: bool = False,
                      **kwargs) -> Dict[str, Any]:
        """
        Get directions between two locations.

        Args:
            origin: Origin location (address, place ID, or coordinates)
            destination: Destination location (address, place ID, or coordinates)
            mode: Transportation mode (driving, walking, bicycling, transit)
            waypoints: Comma-separated list of waypoints
            alternatives: Whether to return alternative routes
            avoid: Features to avoid (tolls, highways, ferries, indoor)
            language: The language in which to return results
            units: Unit system (metric, imperial)
            arrival_time: Desired arrival time (Unix timestamp)
            departure_time: Desired departure time (Unix timestamp)
            debug: Enable debug mode
            **kwargs: Additional parameters

        Returns:
            Directions
        """
        # Prepare request parameters
        params = {
            "origin": origin,
            "destination": destination,
            "mode": mode,
            "debug": debug
        }

        # Add optional parameters if provided
        if waypoints:
            params["waypoints"] = waypoints

        if alternatives is not None:
            params["alternatives"] = alternatives

        if avoid:
            params["avoid"] = avoid

        if language:
            params["language"] = language

        if units:
            params["units"] = units

        if arrival_time is not None:
            params["arrival_time"] = arrival_time

        if departure_time is not None:
            params["departure_time"] = departure_time

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/directions", params=params)

    def get_place_types(self) -> Dict[str, Any]:
        """
        Get a list of all available place types.

        Returns:
            Available place types
        """
        # Prepare request parameters
        params = {}

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Make the request
        return self._make_request("GET", "/place-types", params=params)

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search using the tool.

        This method is required by the BaseTool abstract class.
        It delegates to search_places for Maps.

        Args:
            query: Search query
            **kwargs: Additional search parameters

        Returns:
            Search results
        """
        return self.search_places(query=query, **kwargs)


# Example usage
if __name__ == "__main__":
    # Create the Maps search tool
    maps_tool = MapsTool()

    try:
        # Check if the API is healthy
        health = maps_tool.check_health()
        print(f"API Health: {health}")

        # Get place types
        place_types = maps_tool.get_place_types()
        print(f"Available place types: {len(place_types.get('place_types', {}))}")

        # Perform a place search
        results = maps_tool.search_places(
            query="restaurants in San Francisco",
            radius=5000,
            open_now=True
        )

        # Print search results
        print(f"\nSearch results for 'restaurants in San Francisco':")

        # Print places
        places = results.get('places', [])
        print(f"Places found: {len(places)}")

        # Print first few places
        for i, place in enumerate(places[:3]):
            print(f"\nPlace {i+1}:")
            print(f"Name: {place.get('name')}")
            print(f"Address: {place.get('vicinity')}")
            print(f"Rating: {place.get('rating')}")

            # If we have a place ID, get details
            if place.get('place_id'):
                print("\nGetting place details...")
                details = maps_tool.get_place_details(
                    place_id=place.get('place_id')
                )
                print(f"Phone: {details.get('formatted_phone_number', 'N/A')}")
                print(f"Website: {details.get('website', 'N/A')}")

        # Get directions
        directions = maps_tool.get_directions(
            origin="San Francisco, CA",
            destination="Los Angeles, CA",
            mode="driving"
        )

        # Print directions summary
        routes = directions.get('routes', [])
        if routes:
            route = routes[0]
            print(f"\nDirections from San Francisco to Los Angeles:")
            print(f"Distance: {route.get('legs', [{}])[0].get('distance', {}).get('text', 'N/A')}")
            print(f"Duration: {route.get('legs', [{}])[0].get('duration', {}).get('text', 'N/A')}")

    except Exception as e:
        print(f"Error: {e}")
