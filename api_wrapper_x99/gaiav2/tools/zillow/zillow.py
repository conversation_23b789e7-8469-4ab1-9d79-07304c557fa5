#!/usr/bin/env python3
"""
Zillow functional interface.

This module provides functional interfaces for the Zillow tool.
"""

from typing import Dict, Any, Optional
from .zillow_tool import ZillowTool

# Global instance for functional interface
_zillow_tool = None

def get_tool(zyte_api_key: Optional[str] = None) -> ZillowTool:
    """
    Get or create a Zillow tool instance.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        ZillowTool: Zillow tool instance
    """
    global _zillow_tool
    if _zillow_tool is None:
        _zillow_tool = ZillowTool(zyte_api_key=zyte_api_key)
    return _zillow_tool

def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for properties on Zillow.
    
    Args:
        query (str): Search query for Zillow properties
        zyte_api_key: Optional Zyte API key
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Zillow
    """
    tool = get_tool(zyte_api_key)
    return tool.search(query, **kwargs)

def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the Zillow service is available.
    
    Args:
        zyte_api_key: Optional Zyte API key
        
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_tool(zyte_api_key)
    return tool.check_health()