from .mediamarkt_tool import MediaMarktTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on MediaMarkt.
    
    Args:
        query (str): Search query for MediaMarkt products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from MediaMarkt
    """
    tool = MediaMarktTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the MediaMarkt service is available.
    
    Returns:
        Dict[str, Any]: Health status of MediaMarkt service
    """
    tool = MediaMarktTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for MediaMarkt tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on MediaMarkt',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9308
    }