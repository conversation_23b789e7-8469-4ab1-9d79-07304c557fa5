#!/usr/bin/env python3
"""
Test script to verify that all tool modules in /tools/ directory work correctly.
Tests module imports, class instantiation, and basic functionality.
"""

import os
import sys
import importlib
import inspect
import traceback
import asyncio
import time
import json
import concurrent.futures
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Tools to skip during testing (marked with #skip)
SKIP_TOOLS = [
    # Add tool names here to skip them during testing
    # Example: 'problematic_tool',
]

# Test parameters for all tools (no categorization - all tools are equal)
TOOL_TEST_PARAMS = {
    'serp': {'query': 'python tutorial', 'max_results': 3},
    'amazon': {'query': 'laptop', 'max_results': 3},
    'flights': {'from_airport': 'JFK', 'to_airport': 'LAX', 'date': '2025-08-01'},
    'hotels': {'destination': 'New York', 'check_in': '2025-08-01', 'check_out': '2025-08-02'},
    'maps': {'query': 'restaurants in NYC', 'radius': 5000},
    'youtube': {'query': 'python tutorial', 'max_results': 3},
    'images': {'query': 'cats', 'max_results': 3},
    'scholar': {'query': 'machine learning', 'max_results': 3},
    'web': {'query': 'news', 'max_results': 3},
    'aldi': {'query': 'milk', 'country': 'UK'},
    'cars': {'query': 'toyota'},
    'costco': {'query': 'electronics'},
    'etsy': {'query': 'handmade', 'countries': 'USA'},
    'home_depot': {'query': 'hammer'},
    'immoweb': {'query': 'apartment'},
    'lidl': {'query': 'groceries', 'countries': 'UK'},
    'nike': {'query': 'shoes', 'countries': 'all'},
    'target': {'query': 'clothing'},
    'ubereats': {'query': 'pizza', 'address': 'New York, NY'},
    'walmart': {'query': 'groceries'},
    'zalando': {'query': 'dress', 'domain': 'DE'},
    'zara': {'query': 'shirt'},
    'hm': {'query': 'fashion', 'countries': 'United Kingdom'},
    'tjmaxx': {'query': 'fashion'},
    'ikea': {'query': 'chair'},
    'carrefour': {'query': 'bread'},
    'zillow': {'query': 'house'},
    'mediamarkt': {'query': 'phone', 'countries': 'all'},
    'manomano': {'query': 'tools', 'countries': 'all'},
    'leroymerlin': {'query': 'paint', 'countries': 'all'},
    'louisvuitton': {'query': 'bag', 'countries': 'all'},
    'generate_final_answers': { #skip
        'processed_data': {'test': 'data', 'results': ['item1', 'item2']},
        'original_question': 'What are the best options?',
        'format': 'markdown'
    },
    'generate_structured_data': { #skip
        'input_data': {'products': [{'name': 'Laptop', 'price': 999}]},
        'output_type': 'json',
        'output_filename': 'test_output'
    },
    'rank_data': {      #skip
        'prompt': 'Rank these items by quality',
        'input_data': [{'name': 'Item1', 'score': 85}, {'name': 'Item2', 'score': 92}],
        'criteria': {'quality': 'high', 'price': 'low'}
    },
    'evaluate_data': { #skip
        'input_data': {'options': ['A', 'B', 'C']},
        'criteria': {'quality': 'high', 'value': 'good'}
    },
    'select_data': { #skip
        'input_data': [{'name': 'Option1', 'score': 85}, {'name': 'Option2', 'score': 92}],
        'criteria': 'best_score'
    }
}

def discover_tool_modules() -> Tuple[List[str], List[str]]:
    """
    Discover all tool modules in the tools directory.

    Returns:
        Tuple of (active_modules, skipped_modules)
    """
    tools_dir = Path(__file__).parent
    tool_modules = []
    skipped_modules = []

    for item in tools_dir.iterdir():
        if item.is_dir() and not item.name.startswith('__'):
            # Check if it has an __init__.py file
            init_file = item / '__init__.py'
            if init_file.exists():
                if item.name in SKIP_TOOLS:
                    skipped_modules.append(item.name)
                else:
                    tool_modules.append(item.name)

    return sorted(tool_modules), sorted(skipped_modules)

def save_results_to_json(results: Dict[str, Any], output_file: str = None) -> str:
    """
    Save test results to a JSON file.

    Args:
        results: Test results dictionary
        output_file: Output file path (optional)

    Returns:
        Path to the saved file
    """
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"tool_test_results_{timestamp}.json"

    # Make results JSON serializable
    json_results = {
        'test_metadata': {
            'timestamp': datetime.now().isoformat(),
            'total_modules': results['total_modules'],
            'successful_modules': results['successful_modules'],
            'failed_modules': results['failed_modules']
        },
        'module_results': {}
    }

    # Process each module's results
    for module_name, module_result in results['detailed_results'].items():
        json_results['module_results'][module_name] = {
            'module_name': module_result.get('module_name', module_name),
            'import_success': module_result.get('import_success', False),
            'import_message': module_result.get('import_message', ''),
            'structure_success': module_result.get('structure_success', False),
            'structure_message': module_result.get('structure_message', ''),
            'structure_info': module_result.get('structure_info', {}),
            'instantiation_success': module_result.get('instantiation_success', False),
            'instantiation_message': module_result.get('instantiation_message', ''),
            'search_function_success': module_result.get('search_function_success', False),
            'search_function_message': module_result.get('search_function_message', ''),
            'endpoint_success': module_result.get('endpoint_success', False),
            'endpoint_message': module_result.get('endpoint_message', ''),
            'endpoint_result': str(module_result.get('endpoint_result', '')) if module_result.get('endpoint_result') is not None else None,
            'overall_success': module_result.get('overall_success', False),
            'error': module_result.get('error', '')
        }

    # Save to file
    with open(output_file, 'w') as f:
        json.dump(json_results, f, indent=2, default=str)

    return output_file

def test_module_import(module_name: str) -> Tuple[bool, str, Any]:
    """
    Test if a module can be imported successfully.
    
    Returns:
        Tuple of (success, message, module_object)
    """
    try:
        module = importlib.import_module(f'tools.{module_name}')
        return True, f"Successfully imported", module
    except ImportError as e:
        return False, f"Import error: {str(e)}", None
    except Exception as e:
        return False, f"Unexpected error: {str(e)}", None

def test_module_structure(module_name: str, module: Any) -> Tuple[bool, str, Dict[str, Any]]:
    """
    Test the structure of a module to see what it contains.
    
    Returns:
        Tuple of (success, message, structure_info)
    """
    try:
        structure = {
            'attributes': [],
            'functions': [],
            'classes': [],
            'has_search_function': False,
            'has_tool_class': False
        }
        
        for name in dir(module):
            if name.startswith('_'):
                continue
                
            attr = getattr(module, name)
            
            if inspect.isfunction(attr):
                structure['functions'].append(name)
                if name == 'search':
                    structure['has_search_function'] = True
            elif inspect.isclass(attr):
                structure['classes'].append(name)
                if 'Tool' in name:
                    structure['has_tool_class'] = True
            else:
                structure['attributes'].append(name)
        
        return True, "Structure analyzed", structure
    except Exception as e:
        return False, f"Error analyzing structure: {str(e)}", {}

def test_tool_class_instantiation(module_name: str, module: Any, structure: Dict[str, Any]) -> Tuple[bool, str, Any]:
    """
    Test if tool classes can be instantiated.
    
    Returns:
        Tuple of (success, message, tool_instance)
    """
    try:
        tool_classes = [cls for cls in structure['classes'] if 'Tool' in cls]
        
        if not tool_classes:
            return True, "No tool classes found (module-based tool)", None
        
        # Try to instantiate the first tool class
        tool_class_name = tool_classes[0]
        tool_class = getattr(module, tool_class_name)
        
        # Try instantiation without parameters first
        try:
            tool_instance = tool_class()
            return True, f"Successfully instantiated {tool_class_name}", tool_instance
        except TypeError:
            # Try with common parameters
            try:
                tool_instance = tool_class(api_key="test_key")
                return True, f"Successfully instantiated {tool_class_name} with api_key", tool_instance
            except:
                return True, f"Tool class {tool_class_name} exists but requires specific parameters", None
        
    except Exception as e:
        return False, f"Error instantiating tool class: {str(e)}", None

def test_search_function(module_name: str, module: Any, structure: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Test if the search function exists and can be called.

    Returns:
        Tuple of (success, message)
    """
    try:
        if not structure['has_search_function']:
            return True, "No search function found (may use tool class instead)"

        search_func = getattr(module, 'search')

        # Check if it's callable
        if not callable(search_func):
            return False, "search attribute exists but is not callable"

        # Check function signature
        sig = inspect.signature(search_func)
        params = list(sig.parameters.keys())

        return True, f"search function exists with parameters: {params}"

    except Exception as e:
        return False, f"Error testing search function: {str(e)}"

def test_endpoint_functionality(module_name: str, module: Any, structure: Dict[str, Any], dry_run: bool = True) -> Tuple[bool, str, Any]:
    """
    Test the actual endpoint functionality of a tool.

    Args:
        module_name: Name of the module
        module: The imported module
        structure: Module structure info
        dry_run: If True, only test that functions can be called without making real API calls

    Returns:
        Tuple of (success, message, result)
    """
    try:
        # Skip tools that don't have search endpoints
        if module_name in ['generate_final_answers', 'generate_structured_data', 'rank_data', 'evaluate_data', 'select_data', 'llm']:
            return True, "Infrastructure/processing tool - no search endpoint", None

        # Get test parameters for this module
        test_params = TOOL_TEST_PARAMS.get(module_name, {'query': 'test'})

        # Try to find the search function
        search_func = None

        if structure['has_search_function']:
            search_func = getattr(module, 'search')
        elif structure['has_tool_class']:
            # Try to get search function from tool class
            tool_classes = [cls for cls in structure['classes'] if 'Tool' in cls]
            if tool_classes:
                tool_class_name = tool_classes[0]
                tool_class = getattr(module, tool_class_name)

                # Try to instantiate and get search method
                try:
                    tool_instance = tool_class()
                    if hasattr(tool_instance, 'search'):
                        search_func = tool_instance.search
                except:
                    # Try with api_key parameter
                    try:
                        tool_instance = tool_class(api_key="test_key")
                        if hasattr(tool_instance, 'search'):
                            search_func = tool_instance.search
                    except:
                        pass

        # For processing tools, try the module.submodule.function pattern
        if not search_func and module_name in ['generate_final_answers', 'generate_structured_data', 'rank_data', 'evaluate_data', 'select_data']:
            try:
                submodule = getattr(module, module_name)
                if hasattr(submodule, module_name):
                    search_func = getattr(submodule, module_name)
            except:
                pass

        if not search_func:
            return False, "No callable search function found", None

        if dry_run:
            # In dry run mode, just check that the function exists and get its signature
            try:
                sig = inspect.signature(search_func)
                params = list(sig.parameters.keys())
                is_async = asyncio.iscoroutinefunction(search_func)
                func_type = "async" if is_async else "sync"
                return True, f"Endpoint ready ({func_type}) with params: {params}", None
            except Exception as e:
                return False, f"Error analyzing function signature: {str(e)}", None
        else:
            # Actually call the function with test parameters
            start_time = time.time()

            try:
                if asyncio.iscoroutinefunction(search_func):
                    # Async function
                    result = asyncio.run(search_func(**test_params))
                else:
                    # Sync function
                    result = search_func(**test_params)

                response_time = time.time() - start_time

                # Validate result
                if result is None:
                    return False, f"Function returned None ({response_time:.3f}s)", None
                elif isinstance(result, dict) and 'error' in result and result['error'] is not None:
                    return False, f"Function returned error: {result['error']} ({response_time:.3f}s)", result
                else:
                    result_type = type(result).__name__
                    if isinstance(result, (dict, list)):
                        size_info = f" with {len(result)} items"
                    else:
                        size_info = ""
                    return True, f"Endpoint working - returned {result_type}{size_info} ({response_time:.3f}s)", result

            except Exception as e:
                response_time = time.time() - start_time
                error_msg = str(e)

                # Categorize common errors
                if "API key" in error_msg or "api_key" in error_msg:
                    return False, f"API Key Error: {error_msg[:100]} ({response_time:.3f}s)", None
                elif "Connection" in error_msg or "timeout" in error_msg.lower():
                    return False, f"Connection Error: {error_msg[:100]} ({response_time:.3f}s)", None
                elif "404" in error_msg or "Not Found" in error_msg:
                    return False, f"Endpoint Error: {error_msg[:100]} ({response_time:.3f}s)", None
                else:
                    return False, f"Error: {error_msg[:100]} ({response_time:.3f}s)", None

    except Exception as e:
        return False, f"Unexpected error testing endpoint: {str(e)}", None

def test_module_comprehensive(module_name: str, test_endpoints: bool = True, dry_run: bool = True) -> Dict[str, Any]:
    """
    Run comprehensive tests on a single module.
    
    Returns:
        Dictionary with test results
    """
    results = {
        'module_name': module_name,
        'import_success': False,
        'import_message': '',
        'structure_success': False,
        'structure_message': '',
        'structure_info': {},
        'instantiation_success': False,
        'instantiation_message': '',
        'search_function_success': False,
        'search_function_message': '',
        'endpoint_success': False,
        'endpoint_message': '',
        'endpoint_result': None,
        'overall_success': False
    }
    
    # Test import
    import_success, import_msg, module_obj = test_module_import(module_name)
    results['import_success'] = import_success
    results['import_message'] = import_msg
    
    if not import_success:
        return results
    
    # Test structure
    struct_success, struct_msg, struct_info = test_module_structure(module_name, module_obj)
    results['structure_success'] = struct_success
    results['structure_message'] = struct_msg
    results['structure_info'] = struct_info
    
    if not struct_success:
        return results
    
    # Test instantiation
    inst_success, inst_msg, tool_instance = test_tool_class_instantiation(module_name, module_obj, struct_info)
    results['instantiation_success'] = inst_success
    results['instantiation_message'] = inst_msg
    
    # Test search function
    search_success, search_msg = test_search_function(module_name, module_obj, struct_info)
    results['search_function_success'] = search_success
    results['search_function_message'] = search_msg

    # Test endpoint functionality
    if test_endpoints:
        endpoint_success, endpoint_msg, endpoint_result = test_endpoint_functionality(
            module_name, module_obj, struct_info, dry_run
        )
        results['endpoint_success'] = endpoint_success
        results['endpoint_message'] = endpoint_msg
        results['endpoint_result'] = endpoint_result
    else:
        results['endpoint_success'] = True
        results['endpoint_message'] = "Endpoint testing skipped"

    # Overall success
    results['overall_success'] = (import_success and struct_success and
                                inst_success and search_success and results['endpoint_success'])
    
    return results

def test_module_parallel(args) -> Dict[str, Any]:
    """
    Wrapper function for parallel testing of a single module.

    Args:
        args: Tuple of (module_name, test_endpoints, dry_run)

    Returns:
        Dictionary with test results
    """
    module_name, test_endpoints, dry_run = args
    try:
        return test_module_comprehensive(module_name, test_endpoints, dry_run)
    except Exception as e:
        return {
            'module_name': module_name,
            'import_success': False,
            'import_message': f'Parallel test error: {str(e)}',
            'structure_success': False,
            'structure_message': '',
            'structure_info': {},
            'instantiation_success': False,
            'instantiation_message': '',
            'search_function_success': False,
            'search_function_message': '',
            'endpoint_success': False,
            'endpoint_message': '',
            'endpoint_result': None,
            'overall_success': False,
            'error': str(e)
        }

def run_all_tests(test_endpoints: bool = True, dry_run: bool = True, parallel: bool = True, max_workers: int = 8) -> Dict[str, Any]:
    """
    Run tests on all discovered tool modules.

    Args:
        test_endpoints: Whether to test endpoint functionality
        dry_run: Whether to run in dry-run mode (no actual API calls)
        parallel: Whether to run tests in parallel
        max_workers: Maximum number of parallel workers

    Returns:
        Dictionary with comprehensive test results
    """
    print("🔍 Discovering tool modules...")
    tool_modules, skipped_modules = discover_tool_modules()

    print(f"Found {len(tool_modules)} tool modules: {', '.join(tool_modules)}")
    if skipped_modules:
        print(f"Skipped {len(skipped_modules)} tool modules: {', '.join(skipped_modules)}")
    print()
    
    all_results = {}
    successful_modules = []
    failed_modules = []

    if parallel and len(tool_modules) > 1:
        print(f"🧪 Testing tool modules in parallel (max {max_workers} workers)...\n")

        # Prepare arguments for parallel execution
        test_args = [(module_name, test_endpoints, dry_run) for module_name in tool_modules]

        # Run tests in parallel
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_module = {executor.submit(test_module_parallel, args): args[0] for args in test_args}

            # Collect results as they complete
            for future in concurrent.futures.as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    results = future.result()
                    all_results[module_name] = results

                    if results['overall_success']:
                        successful_modules.append(module_name)
                        print(f"  ✅ {module_name}: All tests passed")
                    else:
                        failed_modules.append(module_name)
                        print(f"  ❌ {module_name}: Some tests failed")

                        # Print specific failures
                        if not results['import_success']:
                            print(f"    - Import: {results['import_message']}")
                        if not results['structure_success']:
                            print(f"    - Structure: {results['structure_message']}")
                        if not results['instantiation_success']:
                            print(f"    - Instantiation: {results['instantiation_message']}")
                        if not results['search_function_success']:
                            print(f"    - Search function: {results['search_function_message']}")
                        if not results['endpoint_success']:
                            print(f"    - Endpoint: {results['endpoint_message']}")

                except Exception as e:
                    print(f"  � {module_name}: Unexpected error - {str(e)}")
                    failed_modules.append(module_name)
                    all_results[module_name] = {
                        'module_name': module_name,
                        'overall_success': False,
                        'error': str(e)
                    }
    else:
        print("🧪 Testing tool modules sequentially...\n")

        for module_name in tool_modules:
            print(f"🔧 Testing {module_name}...")

            try:
                results = test_module_comprehensive(module_name, test_endpoints, dry_run)
                all_results[module_name] = results

                if results['overall_success']:
                    successful_modules.append(module_name)
                    print(f"  ✅ {module_name}: All tests passed")
                else:
                    failed_modules.append(module_name)
                    print(f"  ❌ {module_name}: Some tests failed")

                    # Print specific failures
                    if not results['import_success']:
                        print(f"    - Import: {results['import_message']}")
                    if not results['structure_success']:
                        print(f"    - Structure: {results['structure_message']}")
                    if not results['instantiation_success']:
                        print(f"    - Instantiation: {results['instantiation_message']}")
                    if not results['search_function_success']:
                        print(f"    - Search function: {results['search_function_message']}")
                    if not results['endpoint_success']:
                        print(f"    - Endpoint: {results['endpoint_message']}")

            except Exception as e:
                print(f"  💥 {module_name}: Unexpected error - {str(e)}")
                failed_modules.append(module_name)
                all_results[module_name] = {
                    'module_name': module_name,
                    'overall_success': False,
                    'error': str(e)
                }
    
    # Print summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    total_modules = len(tool_modules)
    successful_count = len(successful_modules)
    failed_count = len(failed_modules)
    
    print(f"Total modules tested: {total_modules}")
    print(f"Successful modules: {successful_count} ({successful_count/total_modules*100:.1f}%)")
    print(f"Failed modules: {failed_count} ({failed_count/total_modules*100:.1f}%)")
    
    if successful_modules:
        print(f"\n✅ WORKING MODULES ({len(successful_modules)}):")
        for module in successful_modules:
            structure = all_results[module]['structure_info']
            classes = len(structure.get('classes', []))
            functions = len(structure.get('functions', []))
            print(f"  - {module}: {classes} classes, {functions} functions")
    
    if failed_modules:
        print(f"\n❌ MODULES WITH ISSUES ({len(failed_modules)}):")
        for module in failed_modules:
            print(f"  - {module}")
    
    # Detailed analysis
    print(f"\n🔍 DETAILED ANALYSIS:")
    
    import_failures = [m for m, r in all_results.items() if not r.get('import_success', False)]
    structure_issues = [m for m, r in all_results.items() if r.get('import_success', False) and not r.get('structure_success', False)]
    instantiation_issues = [m for m, r in all_results.items() if r.get('structure_success', False) and not r.get('instantiation_success', False)]
    search_issues = [m for m, r in all_results.items() if r.get('instantiation_success', False) and not r.get('search_function_success', False)]
    endpoint_issues = [m for m, r in all_results.items() if r.get('search_function_success', False) and not r.get('endpoint_success', False)]
    
    if import_failures:
        print(f"  Import Failures ({len(import_failures)}): {', '.join(import_failures)}")
    if structure_issues:
        print(f"  Structure Issues ({len(structure_issues)}): {', '.join(structure_issues)}")
    if instantiation_issues:
        print(f"  Instantiation Issues ({len(instantiation_issues)}): {', '.join(instantiation_issues)}")
    if search_issues:
        print(f"  Search Function Issues ({len(search_issues)}): {', '.join(search_issues)}")
    if endpoint_issues:
        print(f"  Endpoint Issues ({len(endpoint_issues)}): {', '.join(endpoint_issues)}")
    
    print("\n" + "=" * 80)

    results = {
        'total_modules': total_modules,
        'successful_modules': successful_modules,
        'failed_modules': failed_modules,
        'detailed_results': all_results
    }

    # Save results to JSON file
    try:
        json_file = save_results_to_json(results)
        print(f"📄 Test results saved to: {json_file}")
    except Exception as e:
        print(f"⚠️  Failed to save results to JSON: {e}")

    return results

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Test all tool modules in GaiaV2")
    parser.add_argument("--no-endpoints", action="store_true",
                       help="Skip endpoint functionality testing")
    parser.add_argument("--full-test", action="store_true",
                       help="Run full endpoint tests (make actual API calls)")
    parser.add_argument("--dry-run", action="store_true", default=True,
                       help="Only test endpoint interfaces without making API calls (default)")
    parser.add_argument("--output", "-o", type=str,
                       help="Output JSON file path (default: auto-generated with timestamp)")
    parser.add_argument("--sequential", action="store_true",
                       help="Run tests sequentially instead of in parallel")
    parser.add_argument("--max-workers", type=int, default=8,
                       help="Maximum number of parallel workers (default: 8)")

    args = parser.parse_args()

    # Determine test mode
    test_endpoints = not args.no_endpoints
    dry_run = not args.full_test  # If full_test is True, dry_run is False
    parallel = not args.sequential
    max_workers = args.max_workers

    if args.full_test:
        print("⚠️  Running FULL endpoint tests - this will make actual API calls!")
        print("💡 Some tests may fail due to missing API keys or rate limits.")
    elif test_endpoints:
        print("🧪 Running DRY RUN endpoint tests - checking interfaces only.")
    else:
        print("📋 Running structure tests only - no endpoint testing.")

    if parallel:
        print(f"⚡ Running tests in PARALLEL mode with {max_workers} workers.")
    else:
        print("🔄 Running tests in SEQUENTIAL mode.")

    try:
        results = run_all_tests(test_endpoints=test_endpoints, dry_run=dry_run,
                              parallel=parallel, max_workers=max_workers)

        # Save results to JSON file with custom path if specified
        if args.output:
            try:
                json_file = save_results_to_json(results, args.output)
                print(f"📄 Test results also saved to: {json_file}")
            except Exception as e:
                print(f"⚠️  Failed to save results to custom path: {e}")

        if len(results['failed_modules']) == 0:
            print("🎉 ALL MODULES PASSED! All tool modules are working correctly.")
            sys.exit(0)
        else:
            print(f"⚠️  {len(results['failed_modules'])} modules have issues.")
            if test_endpoints and dry_run:
                print("💡 Try running with --full-test to test actual functionality.")
            elif test_endpoints and not dry_run:
                print("💡 Some failures may be due to missing API keys or external service issues.")
            print("💡 Check the detailed analysis above for specific problems.")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        traceback.print_exc()
        sys.exit(1)
