import os
import requests
from typing import Dict, Any, Optional
from ..base_tool import BaseTool


class NikeTool(BaseTool):
    """
    Tool for searching Nike products using Zyte API.
    """
    
    def __init__(self, zyte_api_key: Optional[str] = None):
        super().__init__("nike")
        self.base_url = os.environ.get("NIKE_BASE_URL", "http://78.47.100.16:9296")
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Nike.
        
        Args:
            query (str): Search query for Nike products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Nike
        """
        try:
            params = {
                'query': query,
                'countries': 'US',  # Limit to one country for faster response
                'k_limit': 2  # Limit to 2 detailed products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            response = self.make_request(
                method='GET',
                url=f"{self.base_url}/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Nike search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Nike service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self.make_request(
                method='GET',
                url=self.base_url
            )
            return {
                'status': 'healthy',
                'service': 'nike',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'nike',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = NikeTool()
    result = tool.search("running shoes")
    print(result)