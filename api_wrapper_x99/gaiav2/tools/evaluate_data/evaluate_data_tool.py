#!/usr/bin/env python3
"""
Evaluate Data Tool.

This tool evaluates data, compares options, and makes judgments using LLM.
"""

import json
from typing import Dict, Any, List, Union, Tuple

try:
    # When imported as a module
    from ..llm.base import LL<PERSON>rovider
    from ..base_tool import BaseTool
    from ..llm_tool_base import LLMToolBase
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm.base import <PERSON><PERSON>rovider
    from base_tool import BaseTool
    from llm_tool_base import LLMToolBase


class EvaluateDataTool(LLMToolBase):
    """
    Tool for evaluating data, comparing options, and making judgments using LLM.
    """

    def __init__(self, env_file: str = None):
        """
        Initialize the Evaluate Data tool.

        Args:
            env_file: Path to .env file
        """
        super().__init__("evaluate_data", env_file)
        # Set environment file
        self.env_file = env_file

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Placeholder for compatibility.
        Not used for this tool.
        """
        raise NotImplementedError("This tool doesn't support search operation")

    async def process(self, input_data: Any, criteria: Union[str, Dict]) -> Dict[str, Any]:
        """
        Process the input data and evaluate it based on criteria.

        This implements the abstract method from LLMToolBase.

        Args:
            input_data: Any input data to evaluate
            criteria: String description or dictionary of evaluation criteria

        Returns:
            Standardized output dictionary
        """
        return await self.execute_tool(
            self._evaluate_data,
            input_data=input_data,
            criteria=criteria
        )

    async def evaluate_data(self, input_data: Any, criteria: Union[str, Dict]) -> Dict[str, Any]:
        """
        Evaluate data, compare options, and make judgments.

        This is the public API method that maintains backward compatibility.

        Args:
            input_data: Any input data to evaluate
            criteria: String description or dictionary of evaluation criteria

        Returns:
            Standardized output dictionary
        """
        return await self.process(input_data, criteria)

    async def _evaluate_data(self, input_data: Any, criteria: Union[str, Dict]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Internal implementation of evaluate_data.

        Args:
            input_data: Any input data to evaluate
            criteria: String description or dictionary of evaluation criteria

        Returns:
            Tuple of (data, metadata)
        """
        # Convert input_data to string
        if isinstance(input_data, dict) or isinstance(input_data, list):
            data_str = json.dumps(input_data, indent=2)
        else:
            data_str = str(input_data)

        # Convert criteria to string if it's a dictionary
        if isinstance(criteria, dict):
            criteria_str = json.dumps(criteria, indent=2)
        else:
            criteria_str = str(criteria)

        # Create the assistant prompt
        assistant_prompt = "I am an AI assistant that helps evaluate data, compare options, and make judgments based on specific criteria."

        # Create the user prompt
        user_prompt = f"""
Please evaluate the following data based on the specified criteria.
Provide a comprehensive analysis including strengths, weaknesses, and recommendations.

DATA:
{data_str}

EVALUATION CRITERIA:
{criteria_str}

Please structure your evaluation as a JSON object with the following sections:
1. Summary - A brief overview of the evaluation (1-2 paragraphs)
2. Detailed Analysis - In-depth analysis of each aspect of the data
3. Strengths - Key positive aspects identified (at least 3 bullet points)
4. Weaknesses - Areas of concern or improvement (at least 3 bullet points)
5. Recommendations - Actionable suggestions based on the evaluation (at least 3 bullet points)
6. Overall Assessment - Final judgment including a numerical score (0-100) and qualitative rating

Return the results in this JSON format:
{{
  "summary": "...",
  "detailed_analysis": {{...}},
  "strengths": [...],
  "weaknesses": [...],
  "recommendations": [...],
  "overall_assessment": {{
    "score": 85,
    "rating": "Excellent/Good/Average/Poor",
    "conclusion": "Final conclusion statement"
  }}
}}

Ensure the detailed_analysis section breaks down each major component of the data.
"""

        # Generate content using the LLM
        generated_content = await self.generate_content(assistant_prompt, user_prompt)

        # Parse the generated content as JSON
        try:
            # Try to parse the entire response as JSON
            result = self.parse_json_response(generated_content)
        except Exception as e:
            # If parsing fails, raise the error to be handled by execute_tool
            raise ValueError(f"Failed to parse LLM response: {str(e)}")

        # Extract key components from the evaluation
        overall_assessment = result.get("overall_assessment", {})
        score = overall_assessment.get("score", 0)
        rating = overall_assessment.get("rating", "")

        # Count the number of strengths, weaknesses, and recommendations
        strengths_count = len(result.get("strengths", []))
        weaknesses_count = len(result.get("weaknesses", []))
        recommendations_count = len(result.get("recommendations", []))

        # Prepare the data output
        data = {
            "summary": result.get("summary", ""),
            "detailed_analysis": result.get("detailed_analysis", {}),
            "strengths": result.get("strengths", []),
            "weaknesses": result.get("weaknesses", []),
            "recommendations": result.get("recommendations", []),
            "overall_assessment": overall_assessment
        }

        # Prepare metadata
        metadata = {
            "criteria_description": criteria_str,
            "evaluation_score": score,
            "evaluation_rating": rating,
            "strengths_count": strengths_count,
            "weaknesses_count": weaknesses_count,
            "recommendations_count": recommendations_count,
            "model_used": self.default_model
        }

        return data, metadata


# Example usage
if __name__ == "__main__":
    import asyncio

    async def main():
        # Create the Evaluate Data tool
        tool = EvaluateDataTool()

        # Example input data (travel options)
        example_data = {
            "destination": "Tokyo, Japan",
            "travel_period": "April 5-15, 2024",
            "budget": "$5,000",
            "travelers": 2,
            "options": {
                "flights": [
                    {
                        "airline": "ANA",
                        "price": "$1,200 per person",
                        "duration": "14 hours",
                        "stops": 0,
                        "departure_time": "10:30 AM"
                    },
                    {
                        "airline": "Japan Airlines",
                        "price": "$950 per person",
                        "duration": "16 hours",
                        "stops": 1,
                        "departure_time": "1:15 PM"
                    },
                    {
                        "airline": "United",
                        "price": "$850 per person",
                        "duration": "17.5 hours",
                        "stops": 1,
                        "departure_time": "11:45 PM"
                    }
                ],
                "accommodations": [
                    {
                        "name": "Park Hyatt Tokyo",
                        "price": "$450 per night",
                        "rating": 5,
                        "location": "Shinjuku",
                        "amenities": ["luxury spa", "multiple restaurants", "city views"]
                    },
                    {
                        "name": "Citadines Shinjuku",
                        "price": "$180 per night",
                        "rating": 4,
                        "location": "Shinjuku",
                        "amenities": ["kitchenette", "laundry", "free wifi"]
                    },
                    {
                        "name": "Airbnb Apartment",
                        "price": "$120 per night",
                        "rating": 4.5,
                        "location": "Shibuya",
                        "amenities": ["kitchen", "washing machine", "local experience"]
                    }
                ]
            }
        }

        # Example criteria
        example_criteria = {
            "priorities": [
                "Maximize comfort and convenience",
                "Stay within budget of $5,000 total",
                "Prefer direct flights if possible",
                "Accommodation should be centrally located"
            ],
            "constraints": [
                "Must have private bathroom",
                "Flight arrival time should be during daylight hours",
                "Need accommodation with laundry facilities"
            ]
        }

        try:
            # Evaluate the data
            result = await tool.evaluate_data(example_data, example_criteria)

            print("Evaluation Results (Standardized Output Format):")
            print(f"Status: {result['status']}")
            print(f"Tool: {result['tool_name']}")
            print(f"Request ID: {result['request_id']}")
            print(f"Execution Time: {result['execution_time']:.2f} seconds")

            # Print evaluation summary and assessment
            if result["status"] == "success":
                print(f"\nSummary: {result['data']['summary']}")

                assessment = result['data']['overall_assessment']
                print(f"\nOverall Assessment:")
                print(f"Score: {assessment.get('score', 'N/A')}/100")
                print(f"Rating: {assessment.get('rating', 'N/A')}")
                print(f"Conclusion: {assessment.get('conclusion', 'N/A')}")

                print(f"\nStrengths ({result['metadata']['strengths_count']}):")
                for i, strength in enumerate(result['data']['strengths'][:3], 1):
                    print(f"{i}. {strength}")

                print(f"\nWeaknesses ({result['metadata']['weaknesses_count']}):")
                for i, weakness in enumerate(result['data']['weaknesses'][:3], 1):
                    print(f"{i}. {weakness}")

                print(f"\nTop Recommendations:")
                for i, rec in enumerate(result['data']['recommendations'][:3], 1):
                    print(f"{i}. {rec}")

            # Save the full result to a file for inspection
            with open('evaluation_example.json', 'w') as f:
                json.dump(result, f, indent=2)
            print("\nFull result saved to 'evaluation_example.json'")

        except Exception as e:
            print(f"Error: {e}")

    # Run the async main function
    asyncio.run(main())
