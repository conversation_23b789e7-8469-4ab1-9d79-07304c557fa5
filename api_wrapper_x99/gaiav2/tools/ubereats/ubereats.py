from .ubereats_tool import UberEatsTool
from typing import Dict, Any, Optional


def search(query: str, address: str, zyte_api_key: Optional[str] = None, maps_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for restaurants on UberEats.
    
    Args:
        query (str): Search query for restaurants/food
        address (str): Address for location-based search
        zyte_api_key (Optional[str]): Zyte API key for authentication
        maps_api_key (Optional[str]): Google Maps API key for geocoding
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from UberEats
    """
    tool = UberEatsTool(zyte_api_key=zyte_api_key, maps_api_key=maps_api_key)
    return tool.search(query, address, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the UberEats service is available.
    
    Returns:
        Dict[str, Any]: Health status of UberEats service
    """
    tool = UberEatsTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for UberEats tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for restaurants on UberEats',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API + Google Maps API',
        'method': 'GET',
        'port': 9298,
        'required_params': ['query', 'address']
    }