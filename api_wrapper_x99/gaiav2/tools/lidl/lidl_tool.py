import os
import requests
from typing import Dict, Any, Optional
from ..base_tool import BaseTool


class LidlTool(BaseTool):
    """
    Tool for searching Lidl products using Zyte API.
    """
    
    def __init__(self, zyte_api_key: Optional[str] = None):
        super().__init__("lidl")
        self.base_url = os.environ.get("LIDL_BASE_URL", "http://78.47.100.16:9295")
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Lidl.
        
        Args:
            query (str): Search query for Lidl products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Lidl
        """
        try:
            params = {
                'query': query,
                'countries': 'Germany',  # Default to Germany for faster response
                'k_limit': 2  # Limit to 2 detailed products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            response = self.make_request(
                method='GET',
                url=f"{self.base_url}/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Lidl search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Lidl service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self.make_request(
                method='GET',
                url=self.base_url
            )
            return {
                'status': 'healthy',
                'service': 'lidl',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'lidl',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = LidlTool()
    result = tool.search("groceries")
    print(result)