#!/usr/bin/env python3
"""
Select Data Tool.

This tool selects options based on evaluation or criteria using LLM.
"""

import json
from typing import Dict, Any, List, Union, Tuple

try:
    # When imported as a module
    from ..llm.base import <PERSON><PERSON><PERSON>ider
    from ..base_tool import BaseTool
    from ..llm_tool_base import LLMToolBase
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm.base import <PERSON><PERSON>rovider
    from base_tool import BaseTool
    from llm_tool_base import LLMToolBase


class SelectDataTool(LLMToolBase):
    """
    Tool for selecting options based on evaluation or criteria using LLM.
    """

    def __init__(self, env_file: str = None):
        """
        Initialize the Select Data tool.

        Args:
            env_file: Path to .env file
        """
        super().__init__("select_data", env_file)
        # Set environment file
        self.env_file = env_file

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Placeholder for compatibility.
        Not used for this tool.
        """
        raise NotImplementedError("This tool doesn't support search operation")

    async def process(self, options: Union[List, Dict], criteria: Union[str, Dict],
              prompt: str, topk: int = 1, explain: bool = True) -> Dict[str, Any]:
        """
        Process the input data and select the best options.

        This implements the abstract method from LLMToolBase.

        Args:
            options: List or dictionary of options to select from
            criteria: String description or dictionary of selection criteria
            prompt: Instructions for how to use options and criteria for selection.
            topk: Number of top options to select (default: 1)
            explain: Whether to include explanations for selections (default: True)

        Returns:
            Standardized output dictionary
        """
        return await self.execute_tool(
            self._select_data,
            options=options,
            criteria=criteria,
            prompt=prompt,
            topk=topk,
            explain=explain
        )

    async def select_data(self, options: Union[List, Dict], criteria: Union[str, Dict],
                   prompt: str, topk: int = 1, explain: bool = True) -> Dict[str, Any]:
        """
        Select options based on evaluation or criteria.

        This is the public API method that maintains backward compatibility.

        Args:
            options: List or dictionary of options to select from
            criteria: String description or dictionary of selection criteria
            prompt: Instructions for how to use options and criteria for selection.
            topk: Number of top options to select (default: 1)
            explain: Whether to include explanations for selections (default: True)

        Returns:
            Standardized output dictionary
        """
        return await self.process(options, criteria, prompt, topk, explain)

    async def _select_data(self, options: Union[List, Dict], criteria: Union[str, Dict],
                    prompt: str, topk: int = 1, explain: bool = True) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Internal implementation of select_data.

        Args:
            options: List or dictionary of options to select from
            criteria: String description or dictionary of selection criteria
            prompt: Instructions for how to use options and criteria for selection.
            topk: Number of top options to select (default: 1)
            explain: Whether to include explanations for selections (default: True)

        Returns:
            Tuple of (data, metadata)
        """
        # Convert options to string
        if isinstance(options, dict) or isinstance(options, list):
            options_str = json.dumps(options, indent=2)
        else:
            options_str = str(options)

        # Convert criteria to string if it's a dictionary
        if isinstance(criteria, dict):
            criteria_str = json.dumps(criteria, indent=2)
        else:
            criteria_str = str(criteria)

        # Create the assistant prompt
        assistant_prompt = "I am an AI assistant that helps select the best options based on specific criteria."

        # Create the user prompt
        user_prompt = f"""
Please select the {topk} best option(s) from the following choices based on the specified criteria.
{f"For each selection, provide a clear explanation of why it was chosen." if explain else ""}

USER PROMPT FOR SELECTION TASK:
{prompt}

OPTIONS (Available data to select from based on the USER PROMPT FOR SELECTION TASK):
{options_str}

SELECTION CRITERIA (Apply these criteria to the OPTIONS based on the USER PROMPT FOR SELECTION TASK):
{criteria_str}

Return ONLY a valid JSON object in the following structure, with no extra text or explanation or comments, only a json that can be parsed with python json library:

{{
  "selected_options": [
    {{
      "option": {{...}},  // The full option object
      "explanation": "Detailed explanation of why this option was selected",
      "confidence": 0.95  // A confidence score between 0 and 1
    }},
    ...
  ],
  "criteria_applied": "Detailed description of how the criteria were applied",
  "alternatives_considered": [
    {{
      "option": {{...}},  // An alternative that was considered but not selected
      "reason_not_selected": "Explanation of why this option wasn't selected"
    }},
    ...
  ]
}}

{f"Ensure each selection has a corresponding explanation." if explain else "Explanations can be brief if not needed."}
Limit your selection to exactly {topk} option(s).
Include a confidence score for each selection.
"""

        # Generate content using the LLM
        generated_content = await self.generate_content(assistant_prompt, user_prompt)

        # Parse the generated content as JSON
        try:
            # Try to parse the entire response as JSON
            result = self.parse_json_response(generated_content)
        except Exception as e:
            # If parsing fails, raise the error to be handled by execute_tool
            raise ValueError(f"Failed to parse LLM response: {str(e)}")

        # Extract the selected options and format the data
        selected_options = result.get("selected_options", [])

        # Prepare the data output
        data = {
            "selected_options": selected_options,
            "alternatives_considered": result.get("alternatives_considered", [])
        }

        # Prepare metadata
        metadata = {
            "criteria_applied": result.get("criteria_applied", ""),
            "criteria_description": criteria_str,
            "user_prompt_for_selection_task": prompt,
            "topk": topk,
            "options_count": len(options) if isinstance(options, list) else 1,
            "explain": explain,
            "model_used": self.default_model
        }

        return data, metadata


# Example usage
if __name__ == "__main__":
    # Create the Select Data tool
    tool = SelectDataTool()

    # Example options (list of restaurants)
    example_options = [
        {
            "name": "Sakura Japanese Restaurant",
            "cuisine": "Japanese",
            "price_range": "$$",
            "rating": 4.7,
            "distance": 2.5,
            "wait_time": "30-45 min",
            "special_diets": ["vegetarian", "gluten-free options"]
        },
        {
            "name": "Trattoria Italiana",
            "cuisine": "Italian",
            "price_range": "$$$",
            "rating": 4.5,
            "distance": 1.2,
            "wait_time": "45-60 min",
            "special_diets": ["vegetarian"]
        },
        {
            "name": "Burger Joint",
            "cuisine": "American",
            "price_range": "$",
            "rating": 4.2,
            "distance": 0.8,
            "wait_time": "15-20 min",
            "special_diets": []
        },
        {
            "name": "Thai Spice",
            "cuisine": "Thai",
            "price_range": "$$",
            "rating": 4.4,
            "distance": 3.0,
            "wait_time": "20-30 min",
            "special_diets": ["vegetarian", "vegan", "gluten-free options"]
        },
        {
            "name": "Mediterranean Delight",
            "cuisine": "Mediterranean",
            "price_range": "$$",
            "rating": 4.3,
            "distance": 1.5,
            "wait_time": "25-35 min",
            "special_diets": ["vegetarian", "vegan"]
        }
    ]

    # Example criteria
    example_criteria = {
        "preferences": [
            "Prefer restaurants with higher ratings",
            "Closer distance is better",
            "Need vegetarian options",
            "Prefer shorter wait times"
        ],
        "constraints": [
            "Price range should be $$ or less",
            "Maximum distance of 3 miles"
        ]
    }

    try:
        # Select the best option
        example_prompt_for_selection = "I want to find a good place for a quick and cheap vegetarian lunch."
        single_result = tool.select_data(example_options, example_criteria, example_prompt_for_selection)
        print("Selected Option (Standardized Output Format):")
        print(f"Status: {single_result['status']}")
        print(f"Tool: {single_result['tool_name']}")
        print(f"Request ID: {single_result['request_id']}")
        print(f"Execution Time: {single_result['execution_time']:.2f} seconds")

        # Print the top selection
        if single_result["status"] == "success" and single_result["data"]["selected_options"]:
            top_option = single_result["data"]["selected_options"][0]
            print("\nTop Selected Option:")
            print(f"Name: {top_option['option']['name']}")
            print(f"Cuisine: {top_option['option']['cuisine']}")
            print(f"Rating: {top_option['option']['rating']}")
            print(f"Confidence: {top_option.get('confidence', 'N/A')}")
            print(f"Explanation: {top_option.get('explanation', 'N/A')}")
            print(f"User Prompt: {single_result['metadata'].get('user_prompt_for_selection_task', 'N/A')}")

        # Select top 3 options
        multiple_result = tool.select_data(example_options, example_criteria, example_prompt_for_selection, topk=3)
        print("\nTop 3 Options (Names only):")
        if multiple_result["status"] == "success":
            for i, option in enumerate(multiple_result["data"]["selected_options"], 1):
                print(f"{i}. {option['option']['name']} - {option['option']['cuisine']}")

        print("\nFull response structure:")
        print(json.dumps(multiple_result, indent=2))

    except Exception as e:
        print(f"Error: {e}")