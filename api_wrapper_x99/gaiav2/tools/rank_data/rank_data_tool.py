#!/usr/bin/env python3
"""
Rank Data Tool.

This tool ranks data based on specific criteria using LLM.
The input_data is used as context, guided by a 'prompt', to identify items for ranking.
"""

import json
from typing import Dict, Any, List, Union, Tuple

try:
    # When imported as a module
    from ..llm.base import <PERSON><PERSON>rovider
    from ..base_tool import BaseTool
    from ..llm_tool_base import LLMToolBase
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from llm.base import LLMProvider
    from base_tool import BaseTool
    from llm_tool_base import LLMToolBase


class RankDataTool(LLMToolBase):
    """
    Tool for ranking data based on specific criteria using LLM.
    It first identifies items from the input_data (context) based on a prompt,
    then ranks those identified items.
    """

    def __init__(self, env_file: str = None):
        """
        Initialize the Rank Data tool.

        Args:
            env_file: Path to .env file
        """
        super().__init__("rank_data", env_file)
        # Set environment file
        self.env_file = env_file

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Placeholder for compatibility.
        Not used for this tool.
        """
        raise NotImplementedError("This tool doesn't support search operation")

    async def process(self, prompt: str, input_data: Union[List, Dict], criteria: Union[str, Dict]) -> Dict[str, Any]:
        """
        Process the input data to identify and rank items based on a prompt and criteria.

        This implements the abstract method from LLMToolBase.

        Args:
            prompt: Instructions for how to use input_data and what to rank.
            input_data: List or dictionary of items to use as context.
            criteria: String description or dictionary of ranking criteria for identified items.

        Returns:
            Standardized output dictionary
        """
        return await self.execute_tool(
            self._rank_data,
            prompt=prompt,
            input_data=input_data,
            criteria=criteria
        )

    async def rank_data(self, prompt: str, input_data: Union[List, Dict], criteria: Union[str, Dict]) -> Dict[str, Any]:
        """
        Identifies items from input_data based on a prompt and then ranks them based on criteria.

        This is the public API method.

        Args:
            prompt: Instructions for how to use input_data and what to rank.
            input_data: List or dictionary of items to use as context.
            criteria: String description or dictionary of ranking criteria for identified items.

        Returns:
            Standardized output dictionary
        """
        return await self.process(prompt, input_data, criteria)

    async def _rank_data(self, prompt: str, input_data: Union[List, Dict], criteria: Union[str, Dict]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Internal implementation of the ranking process.

        Args:
            prompt: Instructions for how to use input_data and what to rank.
            input_data: List or dictionary of items to use as context.
            criteria: String description or dictionary of ranking criteria for identified items.

        Returns:
            Tuple of (data, metadata)
        """
        # Convert input_data (context) to string
        if isinstance(input_data, dict) or isinstance(input_data, list):
            context_data_str = json.dumps(input_data, indent=2, ensure_ascii=False)
        else:
            context_data_str = str(input_data)

        # Convert criteria to string if it's a dictionary
        if isinstance(criteria, dict):
            criteria_str = json.dumps(criteria, indent=2, ensure_ascii=False)
        else:
            criteria_str = str(criteria)

        # Create the assistant prompt
        assistant_prompt = "I am an AI assistant that helps identify relevant items from a given context and then ranks them based on specific criteria."

        # Create the user prompt
        user_prompt = f"""
# TASK DESCRIPTION
You need to perform a two-step process:
1.  **Item Identification**: Based on the "USER PROMPT FOR TASK" and using the "SOURCE CONTEXT DATA" provided, identify the specific items that need to be ranked.
2.  **Ranking**: Rank these identified items according to the "RANKING CRITERIA".

# USER PROMPT FOR TASK
{prompt}

# SOURCE CONTEXT DATA
(Use this data to find the items to rank as per the USER PROMPT FOR TASK)
{context_data_str}

# RANKING CRITERIA
(Apply these criteria to the items you identified from the SOURCE CONTEXT DATA)
{criteria_str}

# REQUIRED JSON OUTPUT STRUCTURE
Please return ONLY a single, valid JSON object with the following structure. Do NOT include any text outside of this JSON object.
{{
  "items_identified_for_ranking": [
    // List of items you identified from the SOURCE CONTEXT DATA based on the USER PROMPT FOR TASK.
    // Example: ["Bangkok", "Chiang Mai", "Phuket"] if the task was to rank cities.
  ],
  "ranked_items": [
    {{
      "rank": 1, // Numerical rank, 1 being the highest.
      "item": <identified_item_object_or_string>, // The actual item that was identified and ranked.
      "score": <numerical_score_0_to_100>, // A score from 0 to 100.
      "explanation": <brief_explanation_for_this_item_s_rank_and_score>
    }},
    // ... more ranked items
  ],
  "criteria_applied_summary": <text_description_of_how_ranking_criteria_were_interpreted_and_applied_to_identified_items>,
  "ranking_method_summary": <text_description_of_the_method_or_logic_used_to_rank_the_identified_items>,
  "overall_ranking_summary": <brief_overall_summary_of_the_ranking_results_for_the_identified_items>
}}

# INSTRUCTIONS FOR JSON CONTENT
-   The "items_identified_for_ranking" list should contain the items extracted from the SOURCE CONTEXT DATA that you will then rank.
-   The "ranked_items" list must be sorted by "rank", with 1 being the highest.
-   Each item in "ranked_items" must have a "rank", "item" (the thing being ranked), "score" (0-100), and an "explanation".
-   Provide concise but clear summaries for "criteria_applied_summary", "ranking_method_summary", and "overall_ranking_summary".
-   Ensure the output is a single, valid JSON object. No markdown, no comments, no extra text.
"""

        # Generate content using the LLM
        generated_content = await self.generate_content(assistant_prompt, user_prompt)

        # Parse the generated content as JSON
        try:
            result = self.parse_json_response(generated_content)
        except Exception as e:
            raise ValueError(f"Failed to parse LLM response as JSON: {str(e)}. Response: {generated_content}")

        # Extract the ranked items and format the data
        ranked_items = result.get("ranked_items", [])
        items_identified = result.get("items_identified_for_ranking", [])

        scores = [item.get("score", 0) for item in ranked_items if isinstance(item, dict) and "score" in item and isinstance(item["score"], (int, float))]
        avg_score = sum(scores) / len(scores) if scores else 0

        # Prepare the data output
        data = {
            "items_identified_for_ranking": items_identified,
            "ranked_items": ranked_items,
            "overall_ranking_summary": result.get("overall_ranking_summary", ""),
            "top_item_details": ranked_items[0] if ranked_items else None,
        }

        # Prepare metadata
        metadata = {
            "user_prompt_for_task": prompt,
            "criteria_description_provided": criteria_str,
            "criteria_applied_summary_by_llm": result.get("criteria_applied_summary", ""),
            "ranking_method_summary_by_llm": result.get("ranking_method_summary", ""),
            "source_context_data_items_count": len(input_data) if isinstance(input_data, list) else (1 if isinstance(input_data, dict) else 0),
            "identified_items_count": len(items_identified),
            "ranked_items_count": len(ranked_items),
            "average_score_of_ranked_items": avg_score,
            "model_used": self.default_model
        }

        return data, metadata


# Example usage
if __name__ == "__main__":
    # Create the Rank Data tool
    tool = RankDataTool()

    # Example: User wants to find top cities in Thailand from a list of search snippets
    example_prompt_for_task = """
From the provided SOURCE CONTEXT DATA (which are snippets from web pages),
please identify all mentions of city names located in Thailand.
Once you have a list of unique Thai city names, rank them to determine the top 3 best cities for tourists.
If no explicit scoring is available in the source data for tourists, use your general knowledge to infer scores.
"""

    example_source_context_data = [
        {"snippet_id": 1, "content": "Discover Bangkok, a city of contrasts with vibrant street life and serene temples. Many tourists also love Chiang Mai for its mountains and culture."},
        {"snippet_id": 2, "content": "Phuket offers stunning beaches and is a top destination. Pattaya is known for its nightlife, but may not be for everyone. Krabi is great for nature lovers."},
        {"snippet_id": 3, "content": "Considering a trip to Thailand? Don't miss Bangkok's Grand Palace. For a quieter experience, Hua Hin is a popular beach resort town."},
        {"snippet_id": 4, "content": "The best Thai islands include Koh Samui and Koh Lanta. For cities, Chiang Rai offers a unique experience near the Golden Triangle."}
    ]

    example_ranking_criteria = {
        "factors_for_tourists": [
            {"factor": "cultural attractions", "weight": 0.4},
            {"factor": "natural beauty_and_beaches", "weight": 0.3},
            {"factor": "accessibility_and_amenities", "weight": 0.2},
            {"factor": "safety_and_friendliness", "weight": 0.1}
        ],
        "note": "Rank higher for cities with a good balance of these factors. Aim for a top 3 list."
    }

    try:
        # Rank the data
        result = tool.rank_data(
            prompt=example_prompt_for_task,
            input_data=example_source_context_data,
            criteria=example_ranking_criteria
        )

        print("Rank Data Tool (Standardized Output Format):")
        print(f"Status: {result['status']}")
        print(f"Tool: {result['tool_name']}")
        print(f"Request ID: {result['request_id']}")
        print(f"Execution Time: {result['execution_time']:.2f} seconds")

        if result["status"] == "success":
            print("\\n--- LLM Task Execution Details ---")
            print(f"User Prompt for Task: {result['metadata']['user_prompt_for_task'].strip()}")
            print(f"Criteria Applied by LLM: {result['metadata']['criteria_applied_summary_by_llm']}")
            print(f"Ranking Method by LLM: {result['metadata']['ranking_method_summary_by_llm']}")

            print("\\n--- Identified and Ranked Items ---")
            print(f"Items Identified for Ranking by LLM: {result['data']['items_identified_for_ranking']}")
            print(f"Overall Ranking Summary by LLM: {result['data']['overall_ranking_summary']}")

            print("\\nTop Ranked Items:")
            for i, item_detail in enumerate(result["data"]["ranked_items"], 1):
                print(f"{i}. Item: {item_detail.get('item')}")
                print(f"   Rank: {item_detail.get('rank')}, Score: {item_detail.get('score')}")
                print(f"   Explanation: {item_detail.get('explanation')}")
                if i >= 3 and result['metadata']['ranked_items_count'] > 3 : # Show top 3 if more
                    print(f"   ... and {result['metadata']['ranked_items_count'] - 3} more ranked items.")
                    break

        elif result["status"] == "error":
            print(f"\\nError Message: {result['error']['message']}")
            if 'details' in result['error']:
                 print(f"Error Details: {result['error']['details']}")


        # Save the full result to a file for inspection
        output_filename = 'ranked_data_tool_example_output.json'
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\\nFull result saved to '{output_filename}'")

    except Exception as e:
        print(f"An unexpected error occurred during the example run: {e}")
