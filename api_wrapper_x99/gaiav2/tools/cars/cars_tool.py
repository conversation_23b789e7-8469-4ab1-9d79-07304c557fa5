#!/usr/bin/env python3
"""
Cars search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class CarsTool(BaseTool):
    """
    Tool for searching cars using the Cars Search API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 120,  # Longer timeout as car search may take time
                env_file: str = None):
        """
        Initialize the Cars search tool.

        Args:
            base_url: Base URL for the Cars API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("CARS_BASE_URL", "http://78.47.100.16:9290")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

    def search(self,
              query: str,
              **kwargs) -> Dict[str, Any]:
        """
        Search for cars.

        Args:
            query: Search query for cars (e.g., "toyota", "honda civic")
            **kwargs: Additional search parameters

        Returns:
            Car search results
        """
        # Prepare request data
        data = {
            "query": query
        }

        # Add any additional parameters
        data.update(kwargs)

        # Use POST method with JSON body
        return self._make_request("POST", "/search", data=data)


# Example usage
if __name__ == "__main__":
    # Create the Cars search tool
    cars_tool = CarsTool()

    try:
        # Check if the API is healthy
        health = cars_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = cars_tool.search(
            query="toyota"
        )

        # Print search results
        print(f"\nSearch results for 'toyota':")
        print(f"Query: {results.get('query')}")
        
        # Print cars if available
        cars = results.get('cars', [])
        if cars:
            print(f"Cars found: {len(cars)}")
            
            # Print first few cars
            for i, car in enumerate(cars[:3]):
                print(f"\nCar {i+1}:")
                print(f"Title: {car.get('title')}")
                print(f"Price: {car.get('price')}")
                print(f"Year: {car.get('year')}")
                print(f"Mileage: {car.get('mileage')}")
                print(f"URL: {car.get('url')}")
        else:
            print("No cars found or different response structure")
            print(f"Full response: {results}")

    except Exception as e:
        print(f"Error: {e}")