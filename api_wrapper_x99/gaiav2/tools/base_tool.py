#!/usr/bin/env python3
"""
Base class for all search tool consumers.
"""

import os
import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dotenv import load_dotenv


class BaseTool(ABC):
    """
    Base class for all search tool consumers.

    This abstract class defines the common interface and functionality
    for all search tools that consume API endpoints.
    """

    def __init__(self, base_url: str = None, timeout: int = 300, env_file: str = None):
        """
        Initialize the base tool.

        Args:
            base_url: Base URL for the API endpoint
            timeout: Request timeout in seconds
            env_file: Path to .env file (default: looks for .env in current and parent directories)
        """
        # Load environment variables from .env file
        self._load_env_vars(env_file)

        self.base_url = base_url
        self.timeout = timeout

    def _load_env_vars(self, env_file: str = None):
        """
        Load environment variables from .env file.

        Args:
            env_file: Path to .env file
        """
        # Try to load from specified file
        if env_file and Path(env_file).exists():
            load_dotenv(env_file)
            return

        # Try to load from .env in current directory
        if Path('.env').exists():
            load_dotenv('.env')
            return

        # Try to load from .env in parent directory
        if Path('../.env').exists():
            load_dotenv('../.env')
            return

        # Try to load from .env in project root
        project_root = Path(__file__).parent.parent
        if (project_root / '.env').exists():
            load_dotenv(project_root / '.env')

    def _make_request(self,
                     method: str,
                     endpoint: str,
                     params: Optional[Dict[str, Any]] = None,
                     data: Optional[Dict[str, Any]] = None,
                     headers: Optional[Dict[str, str]] = None,
                     timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Make an HTTP request to the API endpoint.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint path
            params: URL parameters for GET requests
            data: JSON data for POST requests
            headers: HTTP headers

        Returns:
            JSON response as a dictionary

        Raises:
            Exception: If the request fails
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        default_headers = {"Content-Type": "application/json"}
        if headers:
            default_headers.update(headers)

        try:
            # Use custom timeout if provided, otherwise use default
            request_timeout = timeout if timeout is not None else self.timeout

            if method.upper() == "GET":
                response = requests.get(
                    url,
                    params=params,
                    headers=default_headers,
                    timeout=request_timeout
                )
            elif method.upper() == "POST":
                response = requests.post(
                    url,
                    params=params,
                    json=data,
                    headers=default_headers,
                    timeout=request_timeout
                )
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")

            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            error_msg = f"Request failed: {str(e)}"
            if hasattr(e, 'response') and e.response is not None:
                try:
                    error_data = e.response.json()
                    error_msg = f"{error_msg}. Response: {json.dumps(error_data)}"
                except:
                    error_msg = f"{error_msg}. Response: {e.response.text}"
            raise Exception(error_msg)

    def make_request(self,
                    method: str,
                    url: str,
                    params: Optional[Dict[str, Any]] = None,
                    data: Optional[Dict[str, Any]] = None,
                    headers: Optional[Dict[str, str]] = None,
                    timeout: Optional[int] = None) -> Dict[str, Any]:
        """
        Public method to make HTTP requests.

        Args:
            method: HTTP method (GET, POST, etc.)
            url: Full URL or endpoint path
            params: Query parameters
            data: Request body data
            headers: Additional headers
            timeout: Request timeout in seconds

        Returns:
            Response JSON data
        """
        # If url is a full URL, use it directly, otherwise treat as endpoint
        if url.startswith('http'):
            endpoint = url.replace(self.base_url, '')
        else:
            endpoint = url
        return self._make_request(method, endpoint, params, data, headers, timeout)

    def check_health(self) -> Dict[str, Any]:
        """
        Check if the API is healthy by calling the root endpoint.

        Returns:
            API response
        """
        return self._make_request("GET", "/")

    @abstractmethod
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search using the tool.

        Args:
            query: Search query
            **kwargs: Additional search parameters

        Returns:
            Search results
        """
        pass