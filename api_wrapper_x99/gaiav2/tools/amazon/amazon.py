"""
Amazon product search module.

This module provides a simple interface to the Amazon product search API.
"""

import os
from typing import Dict, Any, List, Optional

from .amazon_tool import AmazonTool

# Create a default instance with environment variables
_default_tool = AmazonTool()

def search(query: str, 
           max_results: int = 10,
           zyte_api_key: Optional[str] = None,
           stats_threshold: float = 1.0,
           **kwargs) -> Dict[str, Any]:
    """
    Search for products on Amazon.

    Args:
        query: Search query for Amazon products
        max_results: Maximum number of results to return
        zyte_api_key: API key for Zyte proxy service
        stats_threshold: Statistical threshold for filtering products
        **kwargs: Additional search parameters

    Returns:
        Amazon product search results
    """
    # Create a custom instance if API key is provided
    if zyte_api_key:
        tool = AmazonTool(zyte_api_key=zyte_api_key)
        return tool.search(query=query, max_results=max_results, stats_threshold=stats_threshold, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, max_results=max_results, stats_threshold=stats_threshold, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Amazon API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()

__all__ = ['search', 'check_health']
