"""
Hotel search module.

This module provides a simple interface to the Hotel search API.
"""

from typing import Dict, Any, Optional

from .hotels_tool import HotelsTool

# Create a default instance with environment variables
_default_tool = HotelsTool()

def search(destination: str,
           check_in: str,
           check_out: str,
           adults: int = 1,
           children: int = 0,
           currency: str = "USD",
           include_providers: bool = False,
           **kwargs) -> Dict[str, Any]:
    """
    Search for hotels.

    Args:
        destination: Destination (city, region, etc.)
        check_in: Check-in date (YYYY-MM-DD)
        check_out: Check-out date (YYYY-MM-DD)
        adults: Number of adults
        children: Number of children
        currency: Currency code (e.g., USD, EUR)
        include_providers: Include provider details for each hotel
        **kwargs: Additional search parameters

    Returns:
        Hotel search results
    """
    return _default_tool.search(
        destination=destination,
        check_in=check_in,
        check_out=check_out,
        adults=adults,
        children=children,
        currency=currency,
        include_providers=include_providers,
        **kwargs
    )

def get_hotel_details(hotel_id: str,
                     check_in: str,
                     check_out: str,
                     adults: int = 1,
                     children: int = 0,
                     currency: str = "USD",
                     **kwargs) -> Dict[str, Any]:
    """
    Get details for a specific hotel.

    Args:
        hotel_id: Hotel ID
        check_in: Check-in date (YYYY-MM-DD)
        check_out: Check-out date (YYYY-MM-DD)
        adults: Number of adults
        children: Number of children
        currency: Currency code (e.g., USD, EUR)
        **kwargs: Additional parameters

    Returns:
        Hotel details
    """
    return _default_tool.get_hotel_details(
        hotel_id=hotel_id,
        check_in=check_in,
        check_out=check_out,
        adults=adults,
        children=children,
        currency=currency,
        **kwargs
    )

def get_providers(booking_link: str, **kwargs) -> Dict[str, Any]:
    """
    Get booking providers for a hotel.

    Args:
        booking_link: Hotel booking link
        **kwargs: Additional parameters

    Returns:
        Booking provider details
    """
    return _default_tool.get_providers(booking_link=booking_link, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Hotels API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()


__all__ = ['search', 'check_health']