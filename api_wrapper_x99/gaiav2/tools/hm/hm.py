#!/usr/bin/env python3
"""
H&M search module.

This module provides a simple interface to the H&M search API.
"""

from typing import Dict, Any, Optional
from .hm_tool import HMTool

# Global tool instance
_hm_tool = None


def get_hm_tool(zyte_api_key: Optional[str] = None) -> HMTool:
    """
    Get or create a global H&M tool instance.
    
    Args:
        zyte_api_key: Zyte API key for authentication
    
    Returns:
        HMTool: The H&M tool instance
    """
    global _hm_tool
    if _hm_tool is None:
        _hm_tool = HMTool(zyte_api_key=zyte_api_key)
    return _hm_tool


def search(query: str, countries: Optional[str] = None, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on H&M.
    
    Args:
        query (str): Search query for H&M products
        countries (str, optional): Countries filter (e.g., "🇬🇧 United Kingdom")
        zyte_api_key (str, optional): Zyte API key for authentication
        **kwargs: Additional search parameters
        
    Returns:
        Dict[str, Any]: Search results from H&M
    """
    tool = get_hm_tool(zyte_api_key)
    return tool.search(query, countries=countries, **kwargs)


def check_health(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check if the H&M service is available.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health status
    """
    tool = get_hm_tool(zyte_api_key)
    return tool.check_health()


def get_health_endpoint(zyte_api_key: Optional[str] = None) -> Dict[str, Any]:
    """
    Check the dedicated health endpoint.
    
    Args:
        zyte_api_key (str, optional): Zyte API key for authentication
    
    Returns:
        Dict[str, Any]: Health endpoint response
    """
    tool = get_hm_tool(zyte_api_key)
    return tool.get_health_endpoint()


def get_available_endpoints() -> Dict[str, str]:
    """
    Get available H&M API endpoints.
    
    Returns:
        Dict[str, str]: Available endpoints
    """
    return {
        'root': 'GET /',
        'health': 'GET /health',
        'search': 'GET /search?query=<query>&countries=<countries>&zyte_api_key=<key>'
    }