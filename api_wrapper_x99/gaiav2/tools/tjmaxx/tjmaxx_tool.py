#!/usr/bin/env python3
"""
TJMaxx search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class TJMaxxTool(BaseTool):
    """
    Tool for searching TJMaxx products using Zyte API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                base_url: str = None,
                timeout: int = 60,
                env_file: str = None):
        """
        Initialize the TJMaxx search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            base_url: Base URL for the TJMaxx API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("TJMAXX_BASE_URL", "http://78.47.100.16:9303")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on TJMaxx.
        
        Args:
            query (str): Search query for TJMaxx products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from TJMaxx
        """
        try:
            params = {
                'query': query
            }
            
            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key
            
            # Add any additional parameters
            params.update(kwargs)
            
            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"TJMaxx search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the TJMaxx service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'tjmaxx',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'tjmaxx',
                'error': str(e)
            }
    
    def get_health_endpoint(self) -> str:
        """
        Get the health endpoint URL.
        
        Returns:
            str: Health endpoint URL
        """
        return f"{self.base_url}/health"


# Example usage
if __name__ == "__main__":
    tool = TJMaxxTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Show health endpoint
        health_endpoint = tool.get_health_endpoint()
        print(f"Health endpoint: {health_endpoint}")
        
        # Perform a search
        result = tool.search("fashion")
        print(f"\nSearch results for 'fashion':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")