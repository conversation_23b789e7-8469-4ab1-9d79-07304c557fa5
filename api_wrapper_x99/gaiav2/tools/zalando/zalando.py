from .zalando_tool import ZalandoTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Zalando.
    
    Args:
        query (str): Search query for Zalando products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Zalando
    """
    tool = ZalandoTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Zalando service is available.
    
    Returns:
        Dict[str, Any]: Health status of Zalando service
    """
    tool = ZalandoTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Zalando tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on Zalando',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9300
    }