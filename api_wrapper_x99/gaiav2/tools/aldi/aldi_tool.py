#!/usr/bin/env python3
"""
Aldi product search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class AldiTool(BaseTool):
    """
    Tool for searching Aldi products using the Aldi Product Scraper API.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 120,  # Longer timeout as Aldi scraping takes time
                zyte_api_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Aldi search tool.

        Args:
            base_url: Base URL for the Aldi API
            timeout: Request timeout in seconds
            zyte_api_key: API key for Zyte proxy service
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("ALDI_BASE_URL", "http://78.47.100.16:9289")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.zyte_api_key = zyte_api_key or os.environ.get("ZYTE_API_KEY", "")

    def search(self,
              query: str,
              zyte_api_key: Optional[str] = None,
              **kwargs) -> Dict[str, Any]:
        """
        Search for products on Aldi.

        Args:
            query: Search query for Aldi products
            zyte_api_key: API key for Zyte proxy service (overrides instance key)
            **kwargs: Additional search parameters

        Returns:
            Aldi product search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "country": "UK",  # Default to UK for faster response
            "limit": 5  # Limit to 5 products for faster response
        }

        # Use provided API key or instance key
        api_key = zyte_api_key or self.zyte_api_key
        if api_key:
            params["zyte_api_key"] = api_key

        # Add any additional parameters
        params.update(kwargs)

        # Use GET method with URL parameters
        return self._make_request("GET", "/search", params=params, timeout=120)


# Example usage
if __name__ == "__main__":
    # Create the Aldi search tool
    aldi_tool = AldiTool()

    try:
        # Check if the API is healthy
        health = aldi_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = aldi_tool.search(
            query="milk",
            zyte_api_key="54a018dfd57d4a50bfc8792da44c122a"
        )

        # Print search results
        print(f"\nSearch results for 'milk':")
        print(f"Query: {results.get('query')}")
        
        # Print products if available
        products = results.get('products', [])
        if products:
            print(f"Products found: {len(products)}")
            
            # Print first few products
            for i, product in enumerate(products[:3]):
                print(f"\nProduct {i+1}:")
                print(f"Title: {product.get('title')}")
                print(f"Price: {product.get('price')}")
                print(f"URL: {product.get('url')}")
        else:
            print("No products found or different response structure")
            print(f"Full response: {results}")

    except Exception as e:
        print(f"Error: {e}")