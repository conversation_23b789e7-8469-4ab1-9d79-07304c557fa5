"""Aldi product search module.

This module provides a simple interface to the Aldi product search API.
"""

import os
from typing import Dict, Any, List, Optional

from .aldi_tool import AldiTool

# Create a default instance with environment variables
_default_tool = AldiTool()

def search(query: str, 
           zyte_api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search for products on Aldi.

    Args:
        query: Search query for Aldi products
        zyte_api_key: API key for Zyte proxy service
        **kwargs: Additional search parameters

    Returns:
        Aldi product search results
    """
    # Create a custom instance if API key is provided
    if zyte_api_key:
        tool = AldiTool(zyte_api_key=zyte_api_key)
        return tool.search(query=query, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Aldi API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()

__all__ = ['search', 'check_health']