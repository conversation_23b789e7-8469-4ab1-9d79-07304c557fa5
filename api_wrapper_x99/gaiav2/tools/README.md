# Search Tools

This package provides Python client tools for consuming various search API endpoints.

## Available Tools

### Simple API (Recommended)

- **serp**: Search across multiple search engines (Google, Bing, Brave)
- **amazon**: Amazon product search
- **aldi**: Aldi product search (requires Zyte API key)
- **cars**: Car search
- **costco**: Costco product search (requires ScrapingBee API key)
- **flights**: Flight search
- **hotels**: Hotel search and hotel details
- **maps**: Maps and places search, place details, and directions
- **youtube**: YouTube video search
- **images**: Image search
- **scholar**: Google Scholar search

### Class-based API (Legacy)

- **SerpTool**: Search across multiple search engines (Google, Bing, Brave)
- **AmazonTool**: Search for products on Amazon
- **AldiTool**: Search for products on Aldi (requires Zyte API key)
- **CarsTool**: Search for cars
- **CostcoTool**: Search for products on Costco (requires ScrapingBee API key)
- **FlightsTool**: Search for flights
- **HotelsTool**: Search for hotels and get hotel details
- **MapsTool**: Search for places, get place details, and get directions
- **YouTubeTool**: Search for YouTube videos
- **ImagesTool**: Search for images
- **ScholarTool**: Search for academic papers

## Installation

No installation is required. Just make sure you have the required dependencies:

```bash
pip install requests python-dotenv
```

## Usage

### Simple API (Recommended)

The simple API provides a more straightforward way to use the search tools without having to create instances of the tool classes.

```python
# Import the tools you need
from tools import serp, amazon, aldi, cars, costco, flights, hotels, maps, youtube, images, scholar

# Use the tools directly
results = serp.search("python programming", engines=["Google"], max_results=5)
print(results)

# Get available search engines
engines = serp.get_available_engines()
print(f"Available engines: {engines}")

# Search for products on Amazon
products = amazon.search("python book", max_results=3)
print(products)

# Search for products on Aldi (requires Zyte API key)
aldi_products = aldi.search("milk", zyte_api_key="your_zyte_api_key")
print(aldi_products)

# Search for cars
cars_results = cars.search("toyota")
print(cars_results)

# Search for products on Costco (requires ScrapingBee API key)
costco_products = costco.search("electronics", scrapingbee_api_key="your_scrapingbee_api_key")
print(costco_products)

# Search for flights
flight_results = flights.search(
    from_airport="JFK",
    to_airport="LAX",
    date="2025-07-01",
    return_date="2025-07-10",
    trip_type="round-trip"
)
print(flight_results)

# Search for places
place_results = maps.search_places(
    query="coffee shops in San Francisco",
    api_key="YOUR_GOOGLE_MAPS_API_KEY"  # Required for Maps API
)
print(place_results)

# Search for YouTube videos
video_results = youtube.search("python tutorial", limit=5)
print(video_results)

# Search for images
image_results = images.search("mountain landscape", limit=10)
print(image_results)

# Search for academic papers
paper_results = scholar.search("machine learning survey", limit=5)
print(paper_results)
```

### Class-based API (Legacy)

#### SERP Search Tool

```python
from tools import SerpTool

# Create the SERP search tool
serp_tool = SerpTool(
    base_url="http://************:8080",  # Default
    timeout=60,  # Default
    brave_api_key="your_brave_api_key",  # Optional
    zyte_api_key="your_zyte_api_key",  # Optional
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = serp_tool.check_health()
print(f"API Health: {health}")

# Get available engines
engines = serp_tool.get_available_engines()
print(f"Available engines: {engines}")

# Perform a search
results = serp_tool.search(
    query="python web development",
    engines=["Google", "Bing"],
    max_results=5
)

# Process results
for result in results.get('results', []):
    print(f"Title: {result.get('title')}")
    print(f"Link: {result.get('link')}")
    print(f"Description: {result.get('description')}")
```

### Amazon Search Tool

```python
from tools import AmazonTool

# Create the Amazon search tool
amazon_tool = AmazonTool(
    base_url="http://************:8080",  # Default
    timeout=120,  # Default
    zyte_api_key="your_zyte_api_key",  # Optional
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = amazon_tool.check_health()
print(f"API Health: {health}")

# Perform a search using POST method
results = amazon_tool.search(
    query="samsung tv 4k 55 inch",
    max_results=5,
    method="POST"  # or "GET"
)

# Process results
for product in results.get('products', []):
    print(f"Title: {product.get('title')}")
    print(f"Price: {product.get('price')}")
    print(f"URL: {product.get('url')}")
```

### Aldi Search Tool

```python
from tools import AldiTool

# Create the Aldi search tool
aldi_tool = AldiTool(
    base_url="http://************:9289",  # Default
    timeout=60,  # Default
    zyte_api_key="your_zyte_api_key",  # Required
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = aldi_tool.check_health()
print(f"API Health: {health}")

# Perform a search
results = aldi_tool.search(
    query="milk",
    zyte_api_key="your_zyte_api_key"  # Can be passed here or set in environment
)

# Process results
for product in results.get('products', []):
    print(f"Title: {product.get('title')}")
    print(f"Price: {product.get('price')}")
    print(f"URL: {product.get('url')}")
```

### Cars Search Tool

```python
from tools import CarsTool

# Create the Cars search tool
cars_tool = CarsTool(
    base_url="http://************:9290",  # Default
    timeout=60,  # Default
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = cars_tool.check_health()
print(f"API Health: {health}")

# Perform a search
results = cars_tool.search(
    query="toyota"
)

# Process results
for car in results.get('cars', []):
    print(f"Make: {car.get('make')}")
    print(f"Model: {car.get('model')}")
    print(f"Price: {car.get('price')}")
    print(f"Year: {car.get('year')}")
```

### Costco Search Tool

```python
from tools import CostcoTool

# Create the Costco search tool
costco_tool = CostcoTool(
    base_url="http://************:9291",  # Default
    timeout=60,  # Default
    scrapingbee_api_key="your_scrapingbee_api_key",  # Required
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = costco_tool.check_health()
print(f"API Health: {health}")

# Perform a search
results = costco_tool.search(
    query="electronics",
    scrapingbee_api_key="your_scrapingbee_api_key"  # Can be passed here or set in environment
)

# Process results
for product in results.get('products', []):
    print(f"Title: {product.get('title')}")
    print(f"Price: {product.get('price')}")
    print(f"URL: {product.get('url')}")
```

### Flight Search Tool

```python
from tools import FlightsTool

# Create the Flight search tool
flights_tool = FlightsTool(
    base_url="http://************:8002",  # Default
    timeout=60,  # Default
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = flights_tool.check_health()
print(f"API Health: {health}")

# Perform a search
results = flights_tool.search(
    from_airport="JFK",
    to_airport="LAX",
    date="2023-12-01",
    return_date="2023-12-10",  # Optional for round trips
    trip="round-trip",  # or "one-way"
    adults=2,
    children=1
)

# Process results
for flight in results.get('flights', []):
    print(f"Airline: {flight.get('airline')}")
    print(f"Price: {flight.get('price')}")
    print(f"Duration: {flight.get('duration')}")
    print(f"Stops: {flight.get('stops')}")
```

### Hotel Search Tool

```python
from tools import HotelsTool
from datetime import datetime, timedelta

# Create the Hotel search tool
hotels_tool = HotelsTool(
    base_url="http://************:8001",  # Default
    timeout=60,  # Default
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = hotels_tool.check_health()
print(f"API Health: {health}")

# Get current date and future date for demo
today = datetime.now()
check_in = (today + timedelta(days=30)).strftime("%Y-%m-%d")
check_out = (today + timedelta(days=33)).strftime("%Y-%m-%d")

# Perform a search
results = hotels_tool.search(
    destination="New York",
    check_in=check_in,
    check_out=check_out,
    adults=2,
    children=0,
    currency="USD"
)

# Process results
for hotel in results.get('hotels', []):
    print(f"Name: {hotel.get('name')}")
    print(f"Price: {hotel.get('price')}")
    print(f"Rating: {hotel.get('rating')}")

    # Get hotel details
    if hotel.get('id'):
        details = hotels_tool.get_hotel_details(
            hotel_id=hotel.get('id'),
            check_in=check_in,
            check_out=check_out,
            adults=2
        )
        print(f"Description: {details.get('description')}")
```

### Maps Tool

```python
from tools import MapsTool

# Create the Maps search tool
maps_tool = MapsTool(
    base_url="http://************:8003",  # Default
    timeout=60,  # Default
    google_maps_api_key="your_google_maps_api_key",  # Optional
    env_file=".env"  # Optional, path to .env file
)

# Check if the API is healthy
health = maps_tool.check_health()
print(f"API Health: {health}")

# Search for places
places_results = maps_tool.search_places(
    query="restaurants in San Francisco",
    radius=5000,
    open_now=True
)

# Process place results
for place in places_results.get('places', []):
    print(f"Name: {place.get('name')}")
    print(f"Address: {place.get('vicinity')}")
    print(f"Rating: {place.get('rating')}")

    # Get place details
    if place.get('place_id'):
        details = maps_tool.get_place_details(
            place_id=place.get('place_id')
        )
        print(f"Phone: {details.get('formatted_phone_number')}")
        print(f"Website: {details.get('website')}")

# Get directions
directions_results = maps_tool.get_directions(
    origin="San Francisco, CA",
    destination="Los Angeles, CA",
    mode="driving"  # or "walking", "bicycling", "transit"
)

# Process directions results
routes = directions_results.get('routes', [])
if routes:
    route = routes[0]
    print(f"Distance: {route.get('legs', [{}])[0].get('distance', {}).get('text')}")
    print(f"Duration: {route.get('legs', [{}])[0].get('duration', {}).get('text')}")

    # Print steps
    for i, step in enumerate(route.get('legs', [{}])[0].get('steps', [])):
        print(f"{i+1}. {step.get('html_instructions')}")
```

## Example Script

An example script is provided to demonstrate how to use these tools. You can run it using the wrapper script from the project root:

```bash
# Use SERP search tool
python run_search_tool.py --tool serp --query "python web development" --engines Google Bing --max-results 5

# Use Amazon search tool
python run_search_tool.py --tool amazon --query "samsung tv 4k 55 inch" --max-results 5 --method POST

# Use Flights search tool
python run_search_tool.py --tool flights --from-airport JFK --to-airport LAX --date 2023-12-01 --return-date 2023-12-10 --trip round-trip --adults 2

# Use Hotels search tool
python run_search_tool.py --tool hotels --destination "New York" --check-in 2023-12-01 --check-out 2023-12-05 --adults 2 --currency USD

# Use Maps search tool (place search)
python run_search_tool.py --tool maps --place-query "restaurants in San Francisco"

# Use Maps search tool (directions)
python run_search_tool.py --tool maps --origin "San Francisco, CA" --destination "Los Angeles, CA" --mode driving

# Specify a .env file
python run_search_tool.py --tool serp --query "python web development" --env-file /path/to/.env
```

Alternatively, if you want to run the example script directly from the tools directory:

```bash
# Change to the tools directory
cd tools

# Run the example script
python example.py --tool serp --query "python web development" --engines Google Bing --max-results 5
```

## Environment Variables

The following environment variables can be used instead of passing API keys directly:

- `BRAVE_API_KEY`: API key for Brave search
- `ZYTE_API_KEY`: API key for Zyte proxy service (used by SERP, Amazon, and Aldi tools)
- `SCRAPINGBEE_API_KEY`: API key for ScrapingBee service (used by Costco tool)
- `GOOGLE_MAPS_API_KEY`: API key for Google Maps
- `YOUTUBE_API_KEY`: API key for YouTube
- `SERPAPI_KEY`: API key for SerpAPI (used by Images, Scholar, and Web tools)

You can set these variables in your environment or in a `.env` file. The tools will automatically look for a `.env` file in the following locations:

1. The path specified in the `env_file` parameter (for class-based API)
2. `.env` in the current directory
3. `.env` in the parent directory
4. `.env` in the project root directory

Example `.env` file:
```
BRAVE_API_KEY=your_brave_api_key
ZYTE_API_KEY=your_zyte_api_key
SCRAPINGBEE_API_KEY=your_scrapingbee_api_key
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
YOUTUBE_API_KEY=your_youtube_api_key
SERPAPI_KEY=your_serpapi_key
```

## API Endpoints

### SERP API

- Base URL: `http://************:9281`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /engines`: Get available search engines
  - `POST /search`: Search across multiple engines

### Amazon API

- Base URL: `http://************:9280`
- Endpoints:
  - `GET /`: Root endpoint
  - `POST /scrape`: Search for products (JSON body)
  - `GET /search`: Search for products (URL parameters)

### Flights API

- Base URL: `http://************:9282`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /flights/search`: Search for flights

### Hotels API

- Base URL: `http://************:9283`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /hotels/search`: Search for hotels
  - `GET /hotels/details/{hotel_id}`: Get hotel details
  - `GET /hotels/providers`: Get booking providers

### Maps API

- Base URL: `http://************:9284`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /places/search`: Search for places
  - `GET /places/details/{place_id}`: Get place details
  - `GET /directions`: Get directions between locations
  - `GET /place-types`: Get available place types

### YouTube API

- Base URL: `http://************:9285`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /videos/search`: Search for videos

### Images API

- Base URL: `http://************:9286`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /images/search`: Search for images

### Scholar API

- Base URL: `http://************:9287`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /scholar/search`: Search for academic papers

### Web API

- Base URL: `http://************:9288`
- Endpoints:
  - `GET /`: Root endpoint
  - `GET /web/search`: Search the web
