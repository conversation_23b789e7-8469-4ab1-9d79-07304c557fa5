#!/usr/bin/env python3
"""
Target search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class TargetTool(BaseTool):
    """
    Tool for searching Target products using Zyte API.
    """

    def __init__(self,
                zyte_api_key: Optional[str] = None,
                base_url: str = None,
                timeout: int = 300,
                env_file: str = None):
        """
        Initialize the Target search tool.

        Args:
            zyte_api_key: Zyte API key for authentication
            base_url: Base URL for the Target API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("TARGET_BASE_URL", "http://78.47.100.16:9297")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Target.
        
        Args:
            query (str): Search query for Target products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Target
        """
        try:
            params = {
                'query': query,
                'limit': 5  # Limit to 5 products for faster response
            }

            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key

            # Add any additional parameters
            params.update(kwargs)

            response = self._make_request(
                method="GET",
                endpoint="/search",
                params=params,
                timeout=120  # Increase timeout to 2 minutes
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Target search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Target service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'target',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'target',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = TargetTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Perform a search
        result = tool.search("clothing")
        print(f"\nSearch results for 'clothing':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")