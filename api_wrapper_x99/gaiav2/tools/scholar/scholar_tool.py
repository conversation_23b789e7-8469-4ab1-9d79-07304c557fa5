#!/usr/bin/env python3
"""
Scholar search tool consumer.
"""

import os
from typing import Dict, Any, List, Optional, Union

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class ScholarTool(BaseTool):
    """
    Tool for searching academic papers using Google Scholar.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                serpapi_key: Optional[str] = None,
                env_file: str = None):
        """
        Initialize the Scholar search tool.

        Args:
            base_url: Base URL for the Scholar API
            timeout: Request timeout in seconds
            serpapi_key: SerpAPI key
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("SCHOLAR_BASE_URL", "http://78.47.100.16:9287")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

        # Get API key from parameter or environment variable
        self.api_key = serpapi_key or os.environ.get("SERPAPI_KEY", "")

    def search(self,
              query: str,
              limit: int = 5,
              lang: str = "en",
              process_pdfs: bool = True,
              debug: bool = False,
              **kwargs) -> Dict[str, Any]:
        """
        Search for academic papers.

        Args:
            query: Search query
            limit: Maximum number of results to return (1-20)
            lang: Language for search results
            process_pdfs: Whether to process PDF links to extract content
            debug: Enable debug mode
            **kwargs: Additional search parameters

        Returns:
            Academic paper search results
        """
        # Prepare request parameters
        params = {
            "query": query,
            "limit": limit,
            "lang": lang,
            "process_pdfs": process_pdfs,
            "debug": debug
        }

        # Add API key if available
        if self.api_key:
            params["api_key"] = self.api_key

        # Add any additional parameters
        params.update(kwargs)

        # Make the request
        return self._make_request("GET", "/scholar/search", params=params)

    def format_paper(self, paper: Dict[str, Any]) -> str:
        """
        Format a paper for display.

        Args:
            paper: Paper data

        Returns:
            Formatted paper string
        """
        title = paper.get("title", "No title")
        authors = ", ".join(paper.get("authors", []))
        year = paper.get("year", "N/A")
        journal = paper.get("journal", "N/A")
        citations = paper.get("citation_count", 0)
        link = paper.get("link", "")

        formatted = f"Title: {title}\n"
        if authors:
            formatted += f"Authors: <AUTHORS>
        formatted += f"Year: {year}\n"
        formatted += f"Journal: {journal}\n"
        formatted += f"Citations: {citations}\n"
        formatted += f"Link: {link}\n"

        # Add PDF content if available
        if paper.get("pdf_content"):
            formatted += f"\nPDF Content Preview:\n{paper['pdf_content'][:500]}...\n"

        return formatted


# Example usage
if __name__ == "__main__":
    # Create the Scholar search tool
    scholar_tool = ScholarTool()

    try:
        # Check if the API is healthy
        health = scholar_tool.check_health()
        print(f"API Health: {health}")

        # Perform a search
        results = scholar_tool.search(
            query="machine learning survey",
            limit=3,
            process_pdfs=True
        )

        # Print search results
        print(f"\nSearch results for 'machine learning survey':")

        # Print papers
        papers = results.get('papers', [])
        print(f"Papers found: {len(papers)}")

        # Print each paper
        for i, paper in enumerate(papers):
            print(f"\nPaper {i+1}:")
            print(scholar_tool.format_paper(paper))

    except Exception as e:
        print(f"Error: {e}")
