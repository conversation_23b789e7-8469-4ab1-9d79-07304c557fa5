#!/usr/bin/env python3
"""
Walmart search tool consumer.
"""

import os
from typing import Dict, Any, Optional

try:
    # When imported as a module
    from ..base_tool import BaseTool
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from base_tool import BaseTool


class WalmartTool(BaseTool):
    """
    Tool for searching Walmart products using FastAPI.
    """

    def __init__(self,
                base_url: str = None,
                timeout: int = 60,
                env_file: str = None):
        """
        Initialize the Walmart search tool.

        Args:
            base_url: Base URL for the Walmart API
            timeout: Request timeout in seconds
            env_file: Path to .env file
        """
        # Get base URL from environment variable if not provided
        if base_url is None:
            base_url = os.environ.get("WALMART_BASE_URL", "http://78.47.100.16:9299")
        
                # Initialize base class (this will load environment variables)
        super().__init__(base_url, timeout, env_file)

    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Walmart.
        
        Args:
            query (str): Search query for Walmart products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Walmart
        """
        try:
            # Prepare request data
            data = {
                "query": query
            }
            
            # Add any additional parameters
            data.update(kwargs)
            
            # Use POST method with JSON body
            response = self._make_request(
                method="POST",
                endpoint="/search",
                data=data
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Walmart search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Walmart service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self._make_request(
                method="GET",
                endpoint="/"
            )
            return {
                'status': 'healthy',
                'service': 'walmart',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'walmart',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = WalmartTool()
    
    try:
        # Check if the API is healthy
        health = tool.check_health()
        print(f"API Health: {health}")
        
        # Perform a search
        result = tool.search("groceries")
        print(f"\nSearch results for 'groceries':")
        print(result)
        
    except Exception as e:
        print(f"Error: {e}")