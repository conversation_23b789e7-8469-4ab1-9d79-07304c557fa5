"""Costco product search module.

This module provides a simple interface to the Costco product search API.
"""

import os
from typing import Dict, Any, List, Optional

from .costco_tool import CostcoTool

# Create a default instance with environment variables
_default_tool = CostcoTool()

def search(query: str, 
           scrapingbee_api_key: Optional[str] = None,
           **kwargs) -> Dict[str, Any]:
    """
    Search for products on Costco.

    Args:
        query: Search query for Costco products
        scrapingbee_api_key: API key for ScrapingBee proxy service
        **kwargs: Additional search parameters

    Returns:
        Costco product search results
    """
    # Create a custom instance if API key is provided
    if scrapingbee_api_key:
        tool = CostcoTool(scrapingbee_api_key=scrapingbee_api_key)
        return tool.search(query=query, **kwargs)
    
    # Otherwise use the default instance
    return _default_tool.search(query=query, **kwargs)

def check_health() -> Dict[str, Any]:
    """
    Check if the Costco API is healthy.

    Returns:
        API health status
    """
    return _default_tool.check_health()

__all__ = ['search', 'check_health']