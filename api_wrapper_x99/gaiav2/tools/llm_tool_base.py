#!/usr/bin/env python3
"""
Base class for LLM-based tools.

This module provides a common base class for all tools that use LLM functionality.
It handles standardized output formatting, error handling, and LLM integration.
"""

import json
import time
import uuid
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Union, Tuple, Optional, Callable
from pathlib import Path

try:
    # When imported as a module
    from .llm.llm import LLM
except ImportError:
    # When run directly
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from llm.llm import LLM


class LLMToolBase(ABC):
    """
    Abstract base class for LLM-based tools.
    
    This class provides common functionality for tools that use LLM capabilities,
    including standardized output formatting, error handling, and LLM integration.
    """
    
    def __init__(self, tool_name: str, env_file: str = None):
        """
        Initialize the LLM tool base.
        
        Args:
            tool_name: Name of the tool
            env_file: Path to .env file
        """
        self.tool_name = tool_name
        self.env_file = env_file
        self.logger = logging.getLogger(f'tools.{tool_name}')
        
        # Default LLM configuration
        self.default_provider = "openai"
        self.default_model = "gpt-4o-mini"
        self.default_temperature = 0.0
        self.default_max_tokens = 4000
        
        # Initialize LLM
        self._llm = None
    
    @property
    def llm(self) -> LLM:
        """Get or create LLM instance."""
        if self._llm is None:
            self._llm = LLM(provider=self.default_provider)
        return self._llm
    
    @abstractmethod
    async def process(self, *args, **kwargs) -> Dict[str, Any]:
        """
        Process the input data using the tool.
        
        This method must be implemented by subclasses.
        
        Returns:
            Standardized output dictionary
        """
        pass
    
    async def execute_tool(self, tool_function: Callable, **kwargs) -> Dict[str, Any]:
        """
        Execute a tool function with standardized error handling and output formatting.
        
        Args:
            tool_function: The function to execute
            **kwargs: Arguments to pass to the tool function
            
        Returns:
            Standardized output dictionary
        """
        request_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            self.logger.info(f"{self.tool_name}: Starting execution with request_id: {request_id}")
            
            # Execute the tool function
            result = await tool_function(**kwargs)
            
            # Handle different return types
            if isinstance(result, tuple) and len(result) == 2:
                data, metadata = result
            else:
                data = result
                metadata = {}
            
            execution_time = time.time() - start_time
            
            # Create standardized output
            output = {
                "status": "success",
                "tool_name": self.tool_name,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "request_id": request_id,
                "execution_time": execution_time,
                "metadata": metadata,
                "data": data
            }
            
            self.logger.info(f"{self.tool_name}: Execution completed successfully in {execution_time:.2f}s")
            return output
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = str(e)
            
            self.logger.error(f"{self.tool_name}: Error executing tool: {error_msg}")
            
            # Create error output
            output = {
                "status": "error",
                "tool_name": self.tool_name,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime()),
                "request_id": request_id,
                "execution_time": execution_time,
                "metadata": {},
                "data": {},
                "error": {
                    "code": type(e).__name__,
                    "message": error_msg,
                    "details": str(e)
                }
            }
            
            return output
    
    async def generate_content(self, assistant_prompt: str, user_prompt: str, 
                             model: str = None, temperature: float = None, 
                             max_tokens: int = None) -> str:
        """
        Generate content using the LLM.
        
        Args:
            assistant_prompt: System/assistant prompt
            user_prompt: User prompt
            model: Model to use (defaults to default_model)
            temperature: Temperature setting (defaults to default_temperature)
            max_tokens: Max tokens (defaults to default_max_tokens)
            
        Returns:
            Generated content
        """
        try:
            messages = [
                {"role": "system", "content": assistant_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Use defaults if not specified
            model = model or self.default_model
            temperature = temperature if temperature is not None else self.default_temperature
            max_tokens = max_tokens or self.default_max_tokens
            
            self.logger.debug(f"{self.tool_name}: Generating content with model: {model}")
            
            response = await self.llm.generate(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"{self.tool_name}: Error generating content: {e}")
            raise
    
    def parse_json_response(self, response: str) -> Dict[str, Any]:
        """
        Parse JSON response from LLM.
        
        Args:
            response: Raw response string
            
        Returns:
            Parsed JSON data
        """
        try:
            # Try to parse the entire response as JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # Try to extract JSON from markdown code blocks
            import re
            json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(1))
            
            # Try to find JSON-like content
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group(0))
            
            raise ValueError(f"Could not parse JSON from response: {response[:200]}...")
    
    def save_output_file(self, content: str, filename: str, output_dir: str = "output") -> str:
        """
        Save content to an output file.
        
        Args:
            content: Content to save
            filename: Name of the file
            output_dir: Output directory
            
        Returns:
            Path to the saved file
        """
        try:
            # Create output directory if it doesn't exist
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # Save the file
            file_path = output_path / filename
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"{self.tool_name}: Saved output to {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"{self.tool_name}: Error saving output file: {e}")
            raise


__all__ = ['LLMToolBase']
