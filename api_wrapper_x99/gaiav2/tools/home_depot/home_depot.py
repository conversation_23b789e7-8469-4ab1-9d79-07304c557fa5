from .home_depot_tool import HomeDepotTool
from typing import Dict, Any, Optional


def search(query: str, zyte_api_key: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """
    Search for products on Home Depot.
    
    Args:
        query (str): Search query for Home Depot products
        zyte_api_key (Optional[str]): Zyte API key for authentication
        **kwargs: Additional parameters
        
    Returns:
        Dict[str, Any]: Search results from Home Depot
    """
    tool = HomeDepotTool(zyte_api_key=zyte_api_key)
    return tool.search(query, **kwargs)


def check_health() -> Dict[str, Any]:
    """
    Check if the Home Depot service is available.
    
    Returns:
        Dict[str, Any]: Health status of Home Depot service
    """
    tool = HomeDepotTool()
    return tool.check_health()


def get_available_features() -> Dict[str, Any]:
    """
    Get available features for Home Depot tool.
    
    Returns:
        Dict[str, Any]: Available features
    """
    return {
        'search': 'Search for products on Home Depot',
        'health_check': 'Check service availability',
        'api_type': 'Zyte API',
        'method': 'GET',
        'port': 9293
    }