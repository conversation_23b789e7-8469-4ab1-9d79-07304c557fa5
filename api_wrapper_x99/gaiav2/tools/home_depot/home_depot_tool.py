import os
import requests
from typing import Dict, Any, Optional
from ..base_tool import BaseTool


class HomeDepotTool(BaseTool):
    """
    Tool for searching Home Depot products using Zyte API.
    """
    
    def __init__(self, zyte_api_key: Optional[str] = None):
        base_url = os.environ.get("HOME_DEPOT_BASE_URL", "http://78.47.100.16:9293")
        super().__init__(base_url)
        self.zyte_api_key = zyte_api_key or os.getenv('ZYTE_API_KEY')
    
    def search(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search for products on Home Depot.
        
        Args:
            query (str): Search query for Home Depot products
            **kwargs: Additional parameters
            
        Returns:
            Dict[str, Any]: Search results from Home Depot
        """
        try:
            params = {
                'query': query
            }
            
            if self.zyte_api_key:
                params['zyte_api_key'] = self.zyte_api_key
            
            response = self.make_request(
                method='GET',
                url=f"{self.base_url}/search",
                params=params
            )
            
            return response
            
        except Exception as e:
            return {
                'error': f"Home Depot search failed: {str(e)}",
                'query': query
            }
    
    def check_health(self) -> Dict[str, Any]:
        """
        Check if the Home Depot service is available.
        
        Returns:
            Dict[str, Any]: Health status
        """
        try:
            response = self.make_request(
                method='GET',
                url=self.base_url
            )
            return {
                'status': 'healthy',
                'service': 'home_depot',
                'response': response
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'service': 'home_depot',
                'error': str(e)
            }


# Example usage
if __name__ == "__main__":
    tool = HomeDepotTool()
    result = tool.search("hammer")
    print(result)