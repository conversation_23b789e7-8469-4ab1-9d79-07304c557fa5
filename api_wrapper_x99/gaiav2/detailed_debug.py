#!/usr/bin/env python3
"""
Detailed debugging to compare exact request format
"""
import requests
import urllib.parse
import json

def debug_request_details(url, params=None, timeout=5):
    """Debug the exact request being made"""
    print(f"\n🔍 DEBUGGING REQUEST")
    print(f"Base URL: {url}")
    print(f"Params: {params}")
    
    # Show the exact URL that will be constructed
    if params:
        query_string = urllib.parse.urlencode(params)
        full_url = f"{url}?{query_string}"
        print(f"Full URL: {full_url}")
        print(f"Query string: {query_string}")
        
        # Show individual param encoding
        for key, value in params.items():
            encoded_value = urllib.parse.quote_plus(str(value))
            print(f"  {key} = {value} -> {encoded_value}")
    
    # Show headers that will be sent
    session = requests.Session()
    req = requests.Request('GET', url, params=params)
    prepared = session.prepare_request(req)
    
    print(f"Prepared URL: {prepared.url}")
    print(f"Headers: {dict(prepared.headers)}")
    
    return prepared.url

def test_etsy_detailed():
    """Test Etsy with detailed debugging"""
    print("=" * 60)
    print("DETAILED ETSY DEBUGGING")
    print("=" * 60)
    
    base_url = "http://78.47.100.16:9292/search"
    params = {
        'query': 'handmade',
        'zyte_api_key': '54a018dfd57d4a50bfc8792da44c122a'
    }
    
    # Debug the request construction
    final_url = debug_request_details(base_url, params)
    
    # Try with different approaches
    print(f"\n🧪 TESTING DIFFERENT APPROACHES")
    
    # Approach 1: Using params
    print(f"\n1️⃣ Using params parameter:")
    try:
        response = requests.get(base_url, params=params, timeout=2)
        print(f"✅ Success: {response.status_code}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Approach 2: Manual URL construction
    print(f"\n2️⃣ Using manual URL construction:")
    manual_url = f"{base_url}?query=handmade&zyte_api_key=54a018dfd57d4a50bfc8792da44c122a"
    print(f"Manual URL: {manual_url}")
    try:
        response = requests.get(manual_url, timeout=2)
        print(f"✅ Success: {response.status_code}")
    except Exception as e:
        print(f"❌ Failed: {e}")
    
    # Approach 3: Different headers
    print(f"\n3️⃣ Using different headers:")
    headers = {
        'User-Agent': 'curl/7.68.0',
        'Accept': '*/*'
    }
    try:
        response = requests.get(base_url, params=params, headers=headers, timeout=2)
        print(f"✅ Success: {response.status_code}")
    except Exception as e:
        print(f"❌ Failed: {e}")

def test_working_service():
    """Test a working service for comparison"""
    print("\n" + "=" * 60)
    print("TESTING WORKING SERVICE (CARS)")
    print("=" * 60)
    
    url = "http://78.47.100.16:9290/search"
    data = {"query": "toyota"}
    
    print(f"URL: {url}")
    print(f"Data: {data}")
    
    try:
        response = requests.post(url, json=data, timeout=5)
        print(f"✅ Status: {response.status_code}")
        print(f"⏱️  Response time: Fast")
        result = response.json()
        print(f"🔍 Result keys: {list(result.keys())}")
        print(f"📊 Cars found: {result.get('total_found', 'unknown')}")
    except Exception as e:
        print(f"❌ Failed: {e}")

def main():
    test_working_service()
    test_etsy_detailed()

if __name__ == "__main__":
    main()
