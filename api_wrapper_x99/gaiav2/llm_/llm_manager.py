import os
import logging
import openai
from dotenv import load_dotenv

class LLMManager:
    """Manages interactions with different LLM APIs."""

    def __init__(self):
        """Initializes the manager and loads API keys."""
        load_dotenv()  # Load .env config
        self.openai_api_key = os.getenv("OPENAI_API_KEY")

        if not self.openai_api_key:
            logging.warning("OPENAI_API_KEY not found; OpenAI models unavailable.")
        else:
            openai.api_key = self.openai_api_key

    def generate_content_03(self, o3_assistant_prompt: str, o3_user_prompt: str) -> str:
        """Uses the reasoning-enabled 'o3-mini' model for completions."""
        messages = [
            {"role": "assistant", "content": o3_assistant_prompt},
            {"role": "user", "content": o3_user_prompt}
        ]
        from openai import OpenAI
        client = OpenAI()
        resp = client.responses.create(
            model="o3",
            input=messages,

        )
        return resp.output_text

# End of llm_manager module 