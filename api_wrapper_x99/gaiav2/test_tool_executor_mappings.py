#!/usr/bin/env python3
"""
Test script to verify that all tool mappings in ToolExecutor work correctly.
"""

import sys
import os
sys.path.insert(0, '.')

from execution.tool_executor import ToolExecutor

def test_tool_mappings():
    """Test that all tool actions can be mapped to functions."""
    
    # Test cases for different tool actions
    test_cases = [
        # Core search actions
        {"action": "search_flight", "params": {"from_airport": "JFK", "to_airport": "LAX", "date": "2025-08-01"}},
        {"action": "search_hotel", "params": {"destination": "New York", "check_in": "2025-08-01", "check_out": "2025-08-02"}},
        {"action": "search_routes", "params": {"origin": "New York", "destination": "Boston"}},
        {"action": "search_restaurant", "params": {"query": "restaurants in NYC"}},
        {"action": "search_activity", "params": {"query": "activities in NYC"}},
        {"action": "search_shops", "params": {"query": "shops in NYC"}},
        {"action": "search_products", "params": {"query": "laptop"}},
        {"action": "global_search", "params": {"query": "python tutorial"}},
        {"action": "search_scientific", "params": {"query": "machine learning"}},
        
        # E-commerce specific actions
        {"action": "search_amazon", "params": {"query": "laptop"}},
        {"action": "search_walmart", "params": {"query": "groceries"}},
        {"action": "search_target", "params": {"query": "clothing"}},
        {"action": "search_costco", "params": {"query": "electronics"}},
        {"action": "search_etsy", "params": {"query": "handmade"}},
        {"action": "search_home_depot", "params": {"query": "hammer"}},
        {"action": "search_ikea", "params": {"query": "chair"}},
        {"action": "search_tjmaxx", "params": {"query": "fashion"}},
        
        # Fashion specific actions
        {"action": "search_nike", "params": {"query": "shoes"}},
        {"action": "search_zara", "params": {"query": "shirt"}},
        {"action": "search_hm", "params": {"query": "fashion"}},
        {"action": "search_zalando", "params": {"query": "dress"}},
        {"action": "search_louisvuitton", "params": {"query": "bag"}},
        
        # Grocery specific actions
        {"action": "search_aldi", "params": {"query": "milk"}},
        {"action": "search_lidl", "params": {"query": "groceries"}},
        {"action": "search_carrefour", "params": {"query": "bread"}},
        {"action": "search_ubereats", "params": {"query": "pizza", "address": "New York, NY"}},
        
        # Real estate actions
        {"action": "search_immoweb", "params": {"query": "apartment"}},
        {"action": "search_zillow", "params": {"query": "house"}},
        
        # Electronics actions
        {"action": "search_mediamarkt", "params": {"query": "phone"}},
        
        # DIY actions
        {"action": "search_manomano", "params": {"query": "tools"}},
        {"action": "search_leroymerlin", "params": {"query": "paint"}},
        
        # Automotive actions
        {"action": "search_cars", "params": {"query": "toyota"}},
        
        # Media actions
        {"action": "search_youtube", "params": {"query": "python tutorial"}},
        {"action": "search_images", "params": {"query": "cats"}},
        {"action": "search_web", "params": {"query": "news"}},
        
        # Tool selector names
        {"action": "Google Maps", "params": {"query": "restaurants in NYC"}},
        {"action": "Amazon", "params": {"query": "laptop"}},
        {"action": "Walmart", "params": {"query": "groceries"}},
        {"action": "Google Flights", "params": {"from_airport": "JFK", "to_airport": "LAX", "date": "2025-08-01"}},
        {"action": "YouTube", "params": {"query": "python tutorial"}},
        
        # Level 2 processing actions
        {"action": "generate_final_answers", "params": {"processed_data": {"test": "data"}}},
        {"action": "generate_structured_data", "params": {"input_data": {"test": "data"}, "output_type": "json", "output_filename": "test"}},
        {"action": "rank_data", "params": {"prompt": "Rank these items", "input_data": [1, 2, 3], "criteria": "quality"}},
        {"action": "evaluate_data", "params": {"input_data": {"test": "data"}, "criteria": "quality"}},
        {"action": "select_data", "params": {"input_data": [1, 2, 3], "criteria": "best"}},
    ]
    
    print(f"🧪 Testing {len(test_cases)} tool action mappings...")
    print("=" * 80)
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        action = test_case["action"]
        params = test_case["params"]
        
        try:
            # Create a test plan with this action
            test_plan = {
                'tasks': [
                    {
                        'id': f'test_task_{i}',
                        'type': 'primitive',
                        'name': f'Test {action}',
                        'orchestration_action': action,
                        'parameters': params,
                        'preconditions': []
                    }
                ]
            }
            
            # Create executor
            executor = ToolExecutor(test_plan, f'test query {i}')
            
            # Test the tool function mapping
            tool_function_map = {
                # Core search actions
                "search_flight": "flights.search",
                "search_hotel": "hotels.search",
                "search_routes": "maps.get_directions",
                "search_restaurant": "maps.search_places",
                "search_activity": "maps.search_places",
                "search_shops": "maps.search_places",
                "search_products": "amazon.search",
                "global_search": "serp.search",
                "search_scientific": "scholar.search",
                
                # E-commerce and retail search actions
                "search_amazon": "amazon.search",
                "search_walmart": "walmart.search",
                "search_target": "target.search",
                "search_costco": "costco.search",
                "search_etsy": "etsy.search",
                "search_home_depot": "home_depot.search",
                "search_ikea": "ikea.search",
                "search_tjmaxx": "tjmaxx.search",
                
                # Fashion and clothing search actions
                "search_nike": "nike.search",
                "search_zara": "zara.search",
                "search_hm": "hm.search",
                "search_zalando": "zalando.search",
                "search_louisvuitton": "louisvuitton.search",
                
                # Grocery and food search actions
                "search_aldi": "aldi.search",
                "search_lidl": "lidl.search",
                "search_carrefour": "carrefour.search",
                "search_ubereats": "ubereats.search",
                
                # Real estate and property search actions
                "search_immoweb": "immoweb.search",
                "search_zillow": "zillow.search",
                
                # Electronics and technology search actions
                "search_mediamarkt": "mediamarkt.search",
                
                # DIY and home improvement search actions
                "search_manomano": "manomano.search",
                "search_leroymerlin": "leroymerlin.search",
                
                # Automotive search actions
                "search_cars": "cars.search",
                
                # Media and content search actions
                "search_youtube": "youtube.search",
                "search_images": "images.search",
                "search_web": "web.search",
                
                # Level 2 processing actions
                "generate_final_answers": "generate_final_answers.generate_final_answers",
                "generate_structured_data": "generate_structured_data.generate_structured_data",
                "rank_data": "rank_data.rank_data",
                "evaluate_data": "evaluate_data.evaluate_data",
                "select_data": "select_data.select_data",
            }
            
            # Test parameter transformation
            transformed_params = executor._transform_parameters_for_tool(action, params)
            
            print(f"✅ {i:2d}. {action:<25} -> Parameters transformed successfully")
            passed += 1
            
        except Exception as e:
            print(f"❌ {i:2d}. {action:<25} -> Error: {str(e)[:50]}...")
            failed += 1
    
    print("=" * 80)
    print(f"📊 RESULTS: {passed} passed, {failed} failed out of {len(test_cases)} tests")
    
    if failed == 0:
        print("🎉 All tool mappings are working correctly!")
        return True
    else:
        print(f"⚠️  {failed} tool mappings need attention.")
        return False

if __name__ == "__main__":
    success = test_tool_mappings()
    sys.exit(0 if success else 1)
