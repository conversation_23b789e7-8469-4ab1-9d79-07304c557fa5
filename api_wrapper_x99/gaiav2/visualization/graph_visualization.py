import logging
import networkx as nx
import plotly.graph_objects as go
from typing import Optional, Dict, Any, List
from collections import deque # Added for BFS
import matplotlib.pyplot as plt

logger = logging.getLogger(__name__)

class GraphVisualizer:
    """Handles the visualization of the HTN plan graph using Plotly."""

    def _calculate_node_layers(self, graph: nx.DiGraph) -> Dict[Any, int]:
        """Calculates the layer/depth of each node based on longest path from roots."""
        layers = {}
        roots = {node for node, degree in graph.in_degree() if degree == 0}

        if not roots:
            # Handle cases with no clear root (e.g., cycles or disconnected graph)
            # Fallback: Assign layer 0 to all nodes or use another strategy
            logging.warning("No root nodes found. Assigning layer 0 to all nodes.")
            return {node: 0 for node in graph.nodes()}

        queue = deque([(root, 0) for root in roots])
        visited_layers = {root: 0 for root in roots} # Store max layer found so far

        # Initialize layers for all nodes
        for node in graph.nodes():
            if node not in visited_layers:
                visited_layers[node] = -1 # Indicate not reached yet

        processed_nodes = set() # To handle DAGs correctly

        while queue:
            node, current_layer = queue.popleft()

            # Update the layer for the current node (it might be reached via a longer path)
            visited_layers[node] = max(visited_layers[node], current_layer)
            processed_nodes.add(node)


            for neighbor in graph.successors(node):
                 # Only proceed if we found a potentially longer path to the neighbor
                if visited_layers[neighbor] < visited_layers[node] + 1:
                    visited_layers[neighbor] = visited_layers[node] + 1
                    # Add neighbor to queue even if visited before,
                    # to propagate the longer path distance
                    queue.append((neighbor, visited_layers[neighbor]))

        # Assign layers, handle nodes unreachable from roots if any
        final_layers = {}
        max_layer = 0
        for node, layer in visited_layers.items():
            if layer == -1:
                logging.warning(f"Node {node} is unreachable from any root. Assigning layer 0.")
                final_layers[node] = 0
            else:
                final_layers[node] = layer
                max_layer = max(max_layer, layer)

        # Invert layers so root is at top (optional, depends on desired orientation)
        # for node in final_layers:
        #     final_layers[node] = max_layer - final_layers[node]

        return final_layers


    def plot_plan_interactive(self, graph: nx.DiGraph):
        """Generates an interactive plot of the plan graph using Plotly, styled as a workflow."""
        if not graph or not graph.nodes:
            logging.warning("Graph is empty, cannot plot.")
            return

        # --- Calculate Node Layers for Hierarchical Layout ---
        try:
            node_layers = self._calculate_node_layers(graph)
            if not node_layers: # Check if calculation failed or graph was unsuitable
                 raise ValueError("Failed to calculate node layers.")

            # Add layers as node attributes for multipartite_layout
            for node_id, layer in node_layers.items():
                graph.nodes[node_id]['layer'] = layer

            # Use multipartite layout
            # align='vertical' places layers along the y-axis (top-down typical tree)
            # Adjust scale and center the layout
            pos = nx.multipartite_layout(graph, subset_key='layer', align='vertical', scale=4.0, center=(0,0))
            # Flip y-coordinates so layer 0 is at the top
            for node in pos:
                 pos[node] = (pos[node][0], -pos[node][1])

            # --- Stretch horizontally to use space better ---
            x_coords = [p[0] for p in pos.values()]
            y_coords = [p[1] for p in pos.values()]
            min_x, max_x = min(x_coords), max(x_coords)
            if max_x > min_x: # Avoid division by zero if all x are same
                horizontal_stretch_factor = 1.8 # Increase this factor to stretch more
                for node in pos:
                    x, y = pos[node]
                    # Scale x relative to the center of the x-range
                    center_x = (min_x + max_x) / 2
                    scaled_x = center_x + (x - center_x) * horizontal_stretch_factor
                    pos[node] = (scaled_x, y)
            # ----------------------------------------------

            # --- Nudge primitive children of layer 1 compounds ---
            try:
                nudge_offset = 0.2 # Adjust this value to control spacing
                layer1_compounds = [n for n in graph.nodes() if graph.nodes[n].get('layer') == 1 and graph.nodes[n].get('task_type') == 'compound']
                for parent_id in layer1_compounds:
                    primitive_children = []
                    for successor in graph.successors(parent_id):
                        # Check if successor is primitive and in the next layer
                        if (graph.nodes[successor].get('layer') == 2 and
                            graph.nodes[successor].get('task_type') == 'primitive'):
                             primitive_children.append(successor)

                    # Sort children by current x-position before nudging
                    primitive_children.sort(key=lambda child_id: pos[child_id][0])

                    num_children = len(primitive_children)
                    for i, child_id in enumerate(primitive_children):
                        # Apply alternating offset
                        current_x, current_y = pos[child_id]
                        direction = 1 if i % 2 == 0 else -1 # Alternate +/-
                        # Maybe scale offset based on number of children? Simpler for now:
                        new_x = current_x + direction * nudge_offset
                        pos[child_id] = (new_x, current_y)
            except Exception as e_nudge:
                 logging.warning(f"Failed to apply horizontal nudge to children: {e_nudge}")
            # --------------------------------------------------

            logging.info("Using multipartite layout based on node depth, stretched horizontally.")

        except Exception as e:
            logging.warning(f"Multipartite layout failed: {e}. Falling back to spring layout.")
            try:
                pos = nx.spring_layout(graph, k=1.5, iterations=75)
            except Exception as e_spring:
                logging.warning(f"Spring layout fallback failed: {e_spring}. Using default spring layout.")
                pos = nx.spring_layout(graph)


        # --- Edge Traces (Distinguish Hierarchical vs. Dependency) ---
        hierarchical_edge_x, hierarchical_edge_y = [], [] # Hierarchical edges (parent-child)
        dependency_edge_x, dependency_edge_y = [], [] # Dependency edges (based on preconditions/input_refs)
        edge_annotations = []
        arrow_annotations = [] # For arrows on edges

        for edge in graph.edges(data=True):
            u, v, data = edge
            x0, y0 = pos[u]
            x1, y1 = pos[v]
            method_id = data.get('method_id')
            edge_type = data.get('edge_type', 'unknown')

            # Add method ID annotation for method-based edges
            if method_id:
                mid_x, mid_y = (x0 + x1) / 2, (y0 + y1) / 2
                edge_annotations.append(dict(
                    x=mid_x, y=mid_y, text=method_id,
                    showarrow=False, font=dict(color='purple', size=9),
                    bgcolor="rgba(255,255,255,0.7)", ax=0, ay=0,
                    yshift=8
                ))

            # --- Categorize edges based on edge_type ---
            if edge_type == 'dependency':  # Dependency edge
                dependency_edge_x.extend([x0, x1, None])
                dependency_edge_y.extend([y0, y1, None])
                # Add arrow annotation for dependency edges
                arrow_annotations.append(dict(
                    x=x1, y=y1,
                    ax=x0, ay=y0,
                    xref='x', yref='y',
                    axref='x', ayref='y',
                    showarrow=True, arrowhead=2, arrowsize=1, arrowwidth=1,
                    arrowcolor='blue'  # Blue for dependency arrows
                ))
            elif edge_type == 'hierarchical':  # Hierarchical edge
                hierarchical_edge_x.extend([x0, x1, None])
                hierarchical_edge_y.extend([y0, y1, None])
                # No arrow annotation for hierarchical edges to reduce clutter
            else:  # Fallback for unknown edge types
                logging.debug(f"Edge ({u}, {v}) has unknown edge_type: {edge_type}")
                # Default to hierarchical style
                hierarchical_edge_x.extend([x0, x1, None])
                hierarchical_edge_y.extend([y0, y1, None])
            # -----------------------------------------------------

        # Define the edge traces with appropriate styles
        hierarchical_trace = go.Scatter(
            x=hierarchical_edge_x, y=hierarchical_edge_y,
            line=dict(width=1, color='#2ca02c', dash='dot'),  # Green, dotted
            hoverinfo='none',
            mode='lines',
            name='Hierarchical (Parent-Child)'
        )

        dependency_trace = go.Scatter(
            x=dependency_edge_x, y=dependency_edge_y,
            line=dict(width=2, color='blue'),  # Blue
            hoverinfo='none',
            mode='lines',
            name='Dependency (Input/Precondition)'
        )

        # --- Node Trace (Style based on Task Type and Orchestration Action) ---
        node_x = []
        node_y = []
        node_text = []
        node_color = []
        node_size = []
        node_symbol = []

        # Node status is reflected in hover text, not in node color

        # Find root nodes (nodes with in-degree 0)
        root_nodes = {node for node, degree in graph.in_degree() if degree == 0}

        for node_id in graph.nodes(): # Assuming node_id is the unique identifier from the plan
            x, y = pos[node_id]
            node_x.append(x)
            node_y.append(y)
            node_info = graph.nodes[node_id] # Get the dictionary of node attributes

            # Node attributes
            task_type = node_info.get('task_type', 'unknown') # compound | primitive
            orch_action = node_info.get('action', 'unknown') # SEARCH, TRANSFORM, etc.
            params = node_info.get('parameters', {})
            preconds = node_info.get('input_refs', [])  # HTN preconditions
            output = node_info.get('output_ref', 'N/A')
            desc = node_info.get('description', 'No description')
            status = node_info.get('status', 'pending')

            # Hover text
            text = f"<b>ID:</b> {node_info.get('original_id', node_id)}<br>" # Use original_id if available
            text += f"<b>Type:</b> {task_type}<br>"
            if task_type == 'primitive':
                text += f"<b>Action:</b> {orch_action}<br>"
            elif task_type == 'compound':
                # Add direct subtasks to hover text for compound nodes
                subtasks = [n for n in graph.successors(node_id) if graph.edges[node_id, n].get('ordered') is not True]
                subtask_ids = [graph.nodes[st].get('original_id', st) for st in subtasks]
                if subtask_ids:
                    text += f"<b>Subtasks:</b> {', '.join(map(str, subtask_ids))}<br>"
            text += f"<b>Status:</b> {status}<br>"
            text += f"<b>Description:</b> {desc}<br>"
            text += f"<b>Parameters:</b> {params}<br>"
            text += f"<b>Inputs/Preconditions:</b> {preconds}<br>"
            text += f"<b>Output:</b> {output}"

            # Add justification if irrelevant
            if status == 'irrelevant':
                text += f"<br><b>Justification:</b> {node_info.get('justification', 'N/A')}"

            node_text.append(text)

            # Node Styling based on type and action
            # --- COLOR ---
            # Check if it's a root node FIRST
            if node_id in root_nodes:
                color = 'red' # Root node color
            # Color by task type / action if not root
            elif task_type == 'compound':
                color = 'orange'
            else: # Primitive tasks
                # Search Actions
                if orch_action.startswith('search_'):
                    color = '#1f77b4' # Blue
                # Generate/Transform Actions
                elif (orch_action.startswith('generate_') or
                      orch_action in ['clean_data', 'normalize_data']):
                    color = '#ff7f0e' # Orange (different shade)
                # Summarize Action
                elif orch_action == 'summarize_data':
                    color = '#2ca02c' # Green
                # Analyze Action
                elif orch_action == 'analyze_data':
                    color = '#d62728' # Red (different shade)
                # Rank Action
                elif orch_action == 'rank_data':
                    color = '#e377c2' # Pink
                # Evaluate Action
                elif orch_action == 'evaluate_data':
                    color = '#9467bd' # Purple
                # Select Action
                elif orch_action == 'select_data':
                    color = '#8c564b' # Brown
                # Fallback
                else:
                    color = 'lightblue' # Default primitive color
            node_color.append(color)

            # --- SYMBOL & SIZE ---
            if task_type == 'compound':
                symbol = 'circle' # Compound tasks are circles
                size = 18
            else: # Primitive tasks
                size = 15
                # Search Actions
                if orch_action.startswith('search_'): symbol = 'diamond'
                # Generate/Transform Actions
                elif (orch_action.startswith('generate_') or orch_action in ['clean_data', 'normalize_data']): symbol = 'cross'
                # Summarize Action
                elif orch_action == 'summarize_data': symbol = 'triangle-up'
                # Analyze Action
                elif orch_action == 'analyze_data': symbol = 'star'
                # Rank Action
                elif orch_action == 'rank_data': symbol = 'hexagram'
                # Evaluate Action
                elif orch_action == 'evaluate_data': symbol = 'hourglass'
                # Select Action
                elif orch_action == 'select_data': symbol = 'pentagon'
                # Fallback
                else: symbol = 'square' # Default primitive symbol
            node_size.append(size)
            node_symbol.append(symbol)

        node_trace = go.Scatter(
            x=node_x, y=node_y,
            mode='markers',
            hoverinfo='text',
            text=node_text,
            marker=dict(
                showscale=False,
                color=node_color,
                symbol=node_symbol,
                size=node_size,
                line_width=2))

        # Combine all annotations
        all_annotations = edge_annotations + arrow_annotations

        fig = go.Figure(
            data=[hierarchical_trace, dependency_trace, node_trace],
            layout=go.Layout(
                title='<br>HTN Orchestration Workflow',
                title_font_size=16,
                showlegend=False,
                hovermode='closest',
                margin=dict(b=30,l=5,r=5,t=60),
                annotations=all_annotations, # Use combined annotations
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False))
        )

        # --- Legend --- (Update for new edge types)
        legend_items = [
            # Nodes
            ('Root Node', 'red', 'circle', None),
            ('Compound Task', 'orange', 'circle', None),
            ('Primitive: Search (*)', '#1f77b4', 'diamond', None),
            ('Primitive: Generate/Transform (*)', '#ff7f0e', 'cross', None),
            ('Primitive: Summarize', '#2ca02c', 'triangle-up', None),
            ('Primitive: Analyze', '#d62728', 'star', None),
            ('Primitive: Rank', '#e377c2', 'hexagram', None),
            ('Primitive: Evaluate', '#9467bd', 'hourglass', None),
            ('Primitive: Select', '#8c564b', 'pentagon', None),
            ('Primitive: Other', 'lightblue', 'square', None),
            # Edges
            ('Hierarchical Edge', '#2ca02c', None, 'dot'),           # Parent-Child relationship
            ('Dependency Edge', 'blue', None, 'solid')               # Input/Precondition dependency
        ]

        for name, color, symbol, dash in legend_items:
            if symbol:
                fig.add_trace(go.Scatter(
                    x=[None], y=[None], mode='markers',
                    marker=dict(size=10, color=color, symbol=symbol),
                    name=name, showlegend=True
                ))
            else: # For lines
                fig.add_trace(go.Scatter(
                    x=[None], y=[None], mode='lines',
                    line=dict(color=color, width=2, dash=dash),
                    name=name, showlegend=True
                ))

        fig.update_layout(showlegend=True)
        fig.show()

    def plot_primitive_dag(self, tasks: List[Dict[str, Any]], methods: List[Dict[str, Any]], filename: str = "primitive_dag.png"):
        """
        Plots the execution DAG for primitive tasks based on HTN tasks and methods.
        """
        # Build primitive-only graph
        G = nx.DiGraph()
        # Add primitive nodes
        for task in tasks:
            if task.get("type") == "primitive":
                G.add_node(task["id"])
        # Add edges based on preconditions
        for task in tasks:
            if task.get("type") == "primitive":
                for pre in task.get("preconditions", []):
                    pre_id = pre.split()[0]
                    if pre_id in G:
                        G.add_edge(pre_id, task["id"], label=pre)
        # Propagate compound-level preconditions to primitive subtasks
        tasks_by_id = {t["id"]: t for t in tasks}
        for method in methods:
            comp_id = method.get("task_id")
            comp_task = tasks_by_id.get(comp_id)
            if comp_task and comp_task.get("type") == "compound":
                # Determine first primitive subtask based on ordering
                ordered_subs = sorted(method.get("subtasks", []), key=lambda s: s.get("order", 0))
                first_prim = None
                for sub in ordered_subs:
                    sid = sub.get("id")
                    sub_task = tasks_by_id.get(sid)
                    if sub_task and sub_task.get("type") == "primitive":
                        first_prim = sid
                        break
                if not first_prim:
                    continue
                # Add edges from each compound precondition to the first primitive
                for pre in comp_task.get("preconditions", []):
                    pre_id = pre.split()[0]
                    if pre_id in G:
                        G.add_edge(pre_id, first_prim, label=pre)
        # Add sequential edges from ordered methods
        for method in methods:
            if method.get("ordering") == "ordered":
                subtasks = sorted(method.get("subtasks", []), key=lambda s: s.get("order", 0))
                for prev, nxt in zip(subtasks, subtasks[1:]):
                    u, v = prev["id"], nxt["id"]
                    if u in G and v in G:
                        G.add_edge(u, v, label="sequential")
        # Add cross-component ordering for top-level method
        # Identify the top-level HTN method (not a subtask of any other method)
        all_subtask_ids = {sub['id'] for m in methods for sub in m.get('subtasks', [])}
        top_methods = [m for m in methods if m.get('task_id') not in all_subtask_ids]
        if top_methods:
            root_method = top_methods[0]
            # Ordered component tasks
            ordered_comps = sorted(root_method.get('subtasks', []), key=lambda s: s.get('order', 0))
            # Map task_id to its method
            methods_by_task = {m.get('task_id'): m for m in methods}
            for comp_prev, comp_next in zip(ordered_comps, ordered_comps[1:]):
                prev_method = methods_by_task.get(comp_prev.get('id'))
                next_method = methods_by_task.get(comp_next.get('id'))
                if not prev_method or not next_method:
                    continue
                # Find last primitive in previous component
                last_prev = None
                for sub in sorted(prev_method.get('subtasks', []), key=lambda s: s.get('order', 0), reverse=True):
                    if tasks_by_id.get(sub.get('id'), {}).get('type') == 'primitive':
                        last_prev = sub.get('id')
                        break
                # Find first primitive in next component
                first_next = None
                for sub in sorted(next_method.get('subtasks', []), key=lambda s: s.get('order', 0)):
                    if tasks_by_id.get(sub.get('id'), {}).get('type') == 'primitive':
                        first_next = sub.get('id')
                        break
                if last_prev and first_next:
                    G.add_edge(last_prev, first_next, label="component-seq")
        # Compute execution plan if DAG
        if nx.is_directed_acyclic_graph(G):
            plan = list(nx.topological_sort(G))
            logging.info(f"Primitive execution plan: {plan}")
        else:
            logging.warning("Primitive DAG contains cycles; cannot generate execution plan.")
            plan = []
        # Plot with Plotly
        try:
             # Try a layout potentially better for DAGs/flow
             pos = nx.kamada_kawai_layout(G)
             logging.info("Using Kamada-Kawai layout for primitive DAG.")
        except Exception as e_layout:
             logging.warning(f"Kamada-Kawai layout failed for primitive DAG: {e_layout}. Falling back to spring layout.")
             pos = nx.spring_layout(G, seed=42, k=0.8) # Increase k slightly for spring fallback

        edge_x, edge_y = [], []
        for u, v in G.edges():
            x0, y0 = pos[u]
            x1, y1 = pos[v]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
        edge_trace = go.Scatter(
            x=edge_x, y=edge_y, mode="lines",
            line=dict(color="black", width=1), hoverinfo="none"
        )
        node_x, node_y, node_text = [], [], []
        for node in G.nodes():
            x, y = pos[node]
            node_x.append(x)
            node_y.append(y)
            node_text.append(node)
        node_trace = go.Scatter(
            x=node_x, y=node_y, mode="markers+text",
            text=node_text, textposition="top center",
            marker=dict(size=20, color="lightblue", line_width=2),
            hoverinfo="text", hovertext=node_text
        )
        # Build figure
        fig = go.Figure(data=[edge_trace, node_trace])
        fig.update_layout(
            title="Primitive Task Execution DAG",
            showlegend=False,
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            margin=dict(l=20, r=20, t=40, b=20)
        )
        # Add arrows and labels for edges
        annotations = []
        for u, v, data in G.edges(data=True):
            x0, y0 = pos[u]
            x1, y1 = pos[v]
            # Arrow annotation
            annotations.append(dict(
                x=x1, y=y1, ax=x0, ay=y0,
                xref='x', yref='y', axref='x', ayref='y',
                showarrow=True, arrowhead=2, arrowsize=1, arrowwidth=1.5,
                arrowcolor='gray'
            ))
            # Edge label
            label = data.get('label')
            if label and label not in ['sequential', 'component-seq']:
                annotations.append(dict(
                    x=(x0 + x1) / 2, y=(y0 + y1) / 2,
                    xref='x', yref='y',
                    text=label, showarrow=False,
                    font=dict(color='black', size=10),
                    bgcolor='rgba(255,255,255,0.7)'
                ))
        fig.update_layout(annotations=annotations)
        if filename:
            try:
                fig.write_image(filename)
                logging.info(f"Primitive DAG saved to {filename}")
            except Exception as e:
                logging.warning(f"Failed to save Primitive DAG to {filename}: {e}")
        fig.show()

def visualize_plan_graph(graph: nx.DiGraph, title="Execution Plan Graph"):
    """
    Visualizes the execution plan graph (now expected to be primitives-only).

    Args:
        graph (nx.DiGraph): The graph to visualize, from WebPlanner.
        title (str): The title of the plot.
    """
    if not isinstance(graph, nx.DiGraph):
        logger.error("Visualization Error: Input is not a valid NetworkX DiGraph.")
        # Optionally raise an error or return a specific value indicating failure
        # raise TypeError("Input must be a NetworkX DiGraph.")
        return

    if not graph.nodes:
        logger.info("Graph is empty. Nothing to visualize.")
        # Create a simple plot indicating the graph is empty
        plt.figure(figsize=(10, 2)) # Adjusted size for better text display
        plt.text(0.5, 0.5, "Plan graph is empty.", ha='center', va='center', fontsize=12, color='gray')
        plt.title(title, fontsize=14)
        plt.gca().set_axis_off() # Use gca() to get current axes for turning off
        plt.tight_layout()
        try:
            plt.show()
        except Exception as e:
            logger.error(f"Error displaying empty graph plot: {e}. This might happen in non-GUI environments.")
        return

    plt.figure(figsize=(18, 14)) # Slightly larger for potentially complex graphs
    
    # Layout Algorithm Selection
    pos = None
    try:
        # For directed acyclic graphs (DAGs), a hierarchical layout is often best.
        # `nx.nx_agraph.graphviz_layout` with `prog='dot'` is excellent if graphviz is installed.
        # It typically requires `pygraphviz` or `pydot`.
        pos = nx.nx_agraph.graphviz_layout(graph, prog='dot', args='-Grankdir=TB') # TB for Top-to-Bottom, LR for Left-to-Right
        logger.info("Using graphviz 'dot' layout for visualization.")
    except ImportError:
        logger.warning("Graphviz (and pygraphviz/pydot) not found. Falling back to spring_layout. For better DAG layout, consider installing graphviz.")
        # Spring layout is a general-purpose fallback
        pos = nx.spring_layout(graph, k=0.8, iterations=70, seed=42) # Adjusted k and iterations
    except Exception as e_gv:
        logger.warning(f"Graphviz layout failed ('{e_gv}'). Falling back to spring_layout.")
        pos = nx.spring_layout(graph, k=0.8, iterations=70, seed=42)
    
    if pos is None: # Should not happen if fallbacks work, but as a safeguard
        logger.error("Failed to compute any layout. Using random layout.")
        pos = nx.random_layout(graph, seed=42)

    node_labels = {}
    node_colors_map = {
        'completed': '#90EE90',  # lightgreen
        'failed': '#FA8072',     # salmon
        'error': '#FA8072',      # salmon
        'running': '#ADD8E6',    # lightskyblue
        'processing': '#ADD8E6', # lightskyblue
        'pending': '#D3D3D3',    # lightgrey
        'irrelevant': '#F5F5F5', # whitesmoke (very light grey)
        'unknown': '#FFA500'     # orange
    }
    node_color_list = []
    node_size_list = [] # Changed from node_sizes to node_size_list for clarity

    for node, data in graph.nodes(data=True):
        node_id_str = str(data.get('original_id', f"Node {node}"))
        action_str = str(data.get('action', 'N/A'))
        desc_str = str(data.get('description', ''))
        # Improved label formatting for clarity
        label = f"ID: {node_id_str}\nAction: {action_str}\nDesc: {desc_str[:25]}{'...' if len(desc_str) > 25 else ''}"
        node_labels[node] = label

        status = str(data.get('status', 'pending')).lower()
        node_color_list.append(node_colors_map.get(status, node_colors_map['unknown']))
        
        # Example: Vary node size by some attribute if needed, otherwise constant
        node_size_list.append(3500) 

    # Node Drawing
    nx.draw_networkx_nodes(graph, pos, 
                           node_color=node_color_list, 
                           node_size=node_size_list, 
                           alpha=0.95, 
                           edgecolors='grey') # Add edgecolors to nodes for better definition
    
    # Edge Drawing (all edges are dependencies between primitives)
    nx.draw_networkx_edges(graph, pos, 
                           edgelist=list(graph.edges()), # Ensure edgelist is a list
                           arrowstyle='-|>', # More pronounced arrow head
                           arrowsize=25, 
                           edge_color='#888888', # Darker grey for edges
                           width=1.8, 
                           alpha=0.8,
                           node_size=node_size_list) # Important for correct arrow placement with varying node sizes
    
    # Label Drawing
    nx.draw_networkx_labels(graph, pos, 
                            labels=node_labels, 
                            font_size=7, # Adjusted font size
                            font_weight='normal', # Normal weight can be more readable for dense info
                            font_family='sans-serif')

    plt.title(title, fontsize=18, fontweight='bold')
    plt.axis('off')
    plt.tight_layout(pad=1.0) # Add some padding
    
    try:
        plt.show()
    except Exception as e:
        logger.error(f"Error displaying plot: {e}. This can occur in non-GUI backend environments for Matplotlib.")

# Example Usage (commented out, for testing purposes if this file is run directly)
# if __name__ == '__main__':
#     # Configure basic logging for the example
#     logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

#     # Test with an empty graph
#     logger.info("Visualizing empty graph...")
#     empty_g = nx.DiGraph()
#     visualize_plan_graph(empty_g, title="Test: Empty Execution Plan")

#     # Create a sample graph (primitives only)
#     logger.info("Visualizing sample primitives-only plan graph...")
#     sample_graph = nx.DiGraph()
#     sample_graph.add_node(0, original_id='T1_Fetch', action='search_data', description='Fetch initial dataset from source XYZ and preprocess for quality.', status='completed', task_type='primitive')
#     sample_graph.add_node(1, original_id='T2_Process', action='process_data', description='Clean, transform, and normalize the fetched data.', status='running', task_type='primitive')
#     sample_graph.add_node(2, original_id='T3_Analyze_A', action='analyze_A', description='Perform statistical analysis on subset A.', status='pending', task_type='primitive')
#     sample_graph.add_node(3, original_id='T4_Analyze_B', action='analyze_B', description='Run machine learning model on subset B.', status='failed', task_type='primitive')
#     sample_graph.add_node(4, original_id='T5_Report', action='generate_report', description='Compile findings into a comprehensive final report.', status='irrelevant', task_type='primitive')

#     sample_graph.add_edge(0, 1, edge_type='dependency') 
#     sample_graph.add_edge(1, 2, edge_type='dependency') 
#     sample_graph.add_edge(1, 3, edge_type='dependency') 
#     sample_graph.add_edge(2, 4, edge_type='dependency') 
#     # sample_graph.add_edge(3, 4, edge_type='dependency') # Example: T4 (failed) might not lead to report
    
#     visualize_plan_graph(sample_graph, title="Sample Primitives-Only Execution Plan")