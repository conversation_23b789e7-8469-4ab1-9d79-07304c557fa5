2025-07-31 15:20:12,770 - root - INFO - Attempting to load plan from: ../test_plan/plan_20250511_193326.json
2025-07-31 15:20:12,770 - root - INFO - Successfully loaded plan from ../test_plan/plan_20250511_193326.json
2025-07-31 15:20:12,771 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-07-31 15:20:12,771 - root - INFO - Step 2 complete: Graph constructed
2025-07-31 15:20:12,771 - root - INFO - Step 3: Plotting the plan graph
2025-07-31 15:20:12,772 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-07-31 15:20:13,213 - root - INFO - Step 3 complete: Plan graph plotted
2025-07-31 15:20:13,213 - root - INFO - Step 4: Generating primitive execution schedule
2025-07-31 15:20:13,213 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-07-31 15:20:13,213 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-07-31 15:20:13,215 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-07-31 15:20:13,215 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't4_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't5_search_activity_city1' depends on: ['t4_select_hotel_city1']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_activity_city1' depends on: ['t5_search_activity_city1']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_hotels_city2' depends on: ['t2_rank_cities']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't8_select_hotel_city2' depends on: ['t7_search_hotels_city2']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't9_search_activity_city2' depends on: ['t8_select_hotel_city2']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t9_search_activity_city2']
2025-07-31 15:20:13,216 - gaiav2.execution.tool_executor - INFO -   Task 't11_search_hotels_city3' depends on: ['t2_rank_cities']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't12_select_hotel_city3' depends on: ['t11_search_hotels_city3']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't13_search_activity_city3' depends on: ['t12_select_hotel_city3']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't14_select_activity_city3' depends on: ['t13_search_activity_city3']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't15_search_hotels_city4' depends on: ['t2_rank_cities']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't16_select_hotel_city4' depends on: ['t15_search_hotels_city4']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't17_search_activity_city4' depends on: ['t16_select_hotel_city4']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't18_select_activity_city4' depends on: ['t17_search_activity_city4']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't19_search_hotels_city5' depends on: ['t2_rank_cities']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't20_select_hotel_city5' depends on: ['t19_search_hotels_city5']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't21_search_activity_city5' depends on: ['t20_select_hotel_city5']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't22_select_activity_city5' depends on: ['t21_search_activity_city5']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO -   Task 't23_generate_final_answers' depends on: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't18_select_activity_city4', 't22_select_activity_city5', 't2_rank_cities', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3', 't16_select_hotel_city4', 't20_select_hotel_city5']
2025-07-31 15:20:13,217 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-07-31 15:20:13,218 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-07-31 15:20:13,218 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-07-31 15:20:13,218 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-07-31 15:20:13,218 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-07-31 15:20:13,219 - root - INFO - Step 6.1 complete: Task templates registered
2025-07-31 15:20:13,220 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-07-31 15:20:13,222 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_cities' to current execution batch.
2025-07-31 15:20:13,222 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_search_cities']
2025-07-31 15:20:13,223 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_cities': Global search for best cities to visit in Thailand (Action: global_search)
2025-07-31 15:20:13,224 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-07-31 15:20:46,874 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' (Global search for best cities to visit in Thailand) raw result: Type <class 'dict'>
2025-07-31 15:20:46,875 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' completed successfully.
2025-07-31 15:20:46,879 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't1_search_cities' to context/tasks/t1_search_cities_result.json
2025-07-31 15:20:46,880 - gaiav2.execution.tool_executor - INFO - Adding task 't2_rank_cities' to current execution batch.
2025-07-31 15:20:46,880 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t2_rank_cities']
2025-07-31 15:20:46,880 - gaiav2.execution.tool_executor - INFO - Executing task 't2_rank_cities': Rank the top 5 Thai cities for the trip (Action: rank_data)
2025-07-31 15:20:46,886 - tools.rank_data - INFO - rank_data: Starting execution with request_id: 0ad251d4-cea2-422f-b1f8-0294eac056cb
2025-07-31 15:20:46,897 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:03,818 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:03,826 - tools.rank_data - INFO - rank_data: Execution completed successfully in 16.94s
2025-07-31 15:21:03,826 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' (Rank the top 5 Thai cities for the trip) raw result: Type <class 'dict'>
2025-07-31 15:21:03,827 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' completed successfully.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't2_rank_cities' to context/tasks/t2_rank_cities_result.json
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Adding task 't19_search_hotels_city5' to current execution batch.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Adding task 't11_search_hotels_city3' to current execution batch.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Adding task 't3_search_hotels_city1' to current execution batch.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Adding task 't15_search_hotels_city4' to current execution batch.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Adding task 't7_search_hotels_city2' to current execution batch.
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Executing batch of 5 tasks: ['t19_search_hotels_city5', 't11_search_hotels_city3', 't3_search_hotels_city1', 't15_search_hotels_city4', 't7_search_hotels_city2']
2025-07-31 15:21:03,834 - gaiav2.execution.tool_executor - INFO - Executing task 't19_search_hotels_city5': Search hotels in top city #5 (Action: search_hotel)
2025-07-31 15:21:03,835 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-07-31 15:21:03,835 - gaiav2.execution.tool_executor - INFO - Executing task 't11_search_hotels_city3': Search hotels in top city #3 (Action: search_hotel)
2025-07-31 15:21:03,835 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-07-31 15:21:03,836 - gaiav2.execution.tool_executor - INFO - Executing task 't3_search_hotels_city1': Search hotels in top city #1 (Action: search_hotel)
2025-07-31 15:21:03,836 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-07-31 15:21:03,836 - gaiav2.execution.tool_executor - INFO - Executing task 't15_search_hotels_city4': Search hotels in top city #4 (Action: search_hotel)
2025-07-31 15:21:03,836 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-07-31 15:21:03,837 - gaiav2.execution.tool_executor - INFO - Executing task 't7_search_hotels_city2': Search hotels in top city #2 (Action: search_hotel)
2025-07-31 15:21:03,837 - gaiav2.execution.tool_executor - WARNING - Tool function search_hotel (search) is synchronous. Running in executor.
2025-07-31 15:21:06,556 - gaiav2.execution.tool_executor - INFO - Task 't3_search_hotels_city1' (Search hotels in top city #1) raw result: Type <class 'dict'>
2025-07-31 15:21:08,287 - gaiav2.execution.tool_executor - INFO - Task 't19_search_hotels_city5' (Search hotels in top city #5) raw result: Type <class 'dict'>
2025-07-31 15:21:09,876 - gaiav2.execution.tool_executor - INFO - Task 't11_search_hotels_city3' (Search hotels in top city #3) raw result: Type <class 'dict'>
2025-07-31 15:21:12,053 - gaiav2.execution.tool_executor - INFO - Task 't7_search_hotels_city2' (Search hotels in top city #2) raw result: Type <class 'dict'>
2025-07-31 15:21:14,182 - gaiav2.execution.tool_executor - INFO - Task 't15_search_hotels_city4' (Search hotels in top city #4) raw result: Type <class 'dict'>
2025-07-31 15:21:14,182 - gaiav2.execution.tool_executor - INFO - Task 't19_search_hotels_city5' completed successfully.
2025-07-31 15:21:14,183 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't19_search_hotels_city5' to context/tasks/t19_search_hotels_city5_result.json
2025-07-31 15:21:14,183 - gaiav2.execution.tool_executor - INFO - Task 't11_search_hotels_city3' completed successfully.
2025-07-31 15:21:14,183 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't11_search_hotels_city3' to context/tasks/t11_search_hotels_city3_result.json
2025-07-31 15:21:14,183 - gaiav2.execution.tool_executor - INFO - Task 't3_search_hotels_city1' completed successfully.
2025-07-31 15:21:14,184 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't3_search_hotels_city1' to context/tasks/t3_search_hotels_city1_result.json
2025-07-31 15:21:14,184 - gaiav2.execution.tool_executor - INFO - Task 't15_search_hotels_city4' completed successfully.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't15_search_hotels_city4' to context/tasks/t15_search_hotels_city4_result.json
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Task 't7_search_hotels_city2' completed successfully.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't7_search_hotels_city2' to context/tasks/t7_search_hotels_city2_result.json
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Adding task 't8_select_hotel_city2' to current execution batch.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Adding task 't20_select_hotel_city5' to current execution batch.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Adding task 't4_select_hotel_city1' to current execution batch.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Adding task 't16_select_hotel_city4' to current execution batch.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Adding task 't12_select_hotel_city3' to current execution batch.
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Executing batch of 5 tasks: ['t8_select_hotel_city2', 't20_select_hotel_city5', 't4_select_hotel_city1', 't16_select_hotel_city4', 't12_select_hotel_city3']
2025-07-31 15:21:14,185 - gaiav2.execution.tool_executor - INFO - Executing task 't8_select_hotel_city2': Select best hotel in city #2 (Action: select_data)
2025-07-31 15:21:14,186 - tools.select_data - INFO - select_data: Starting execution with request_id: 2060123f-a008-4893-a506-50604b21a6ae
2025-07-31 15:21:14,194 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:14,194 - gaiav2.execution.tool_executor - INFO - Executing task 't20_select_hotel_city5': Select best hotel in city #5 (Action: select_data)
2025-07-31 15:21:14,195 - tools.select_data - INFO - select_data: Starting execution with request_id: 05aac1bf-749c-4594-876c-8ace97506e1a
2025-07-31 15:21:14,195 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:14,195 - gaiav2.execution.tool_executor - INFO - Executing task 't4_select_hotel_city1': Select best hotel in city #1 (Action: select_data)
2025-07-31 15:21:14,196 - tools.select_data - INFO - select_data: Starting execution with request_id: 3a01ac11-7063-4c05-8866-434c60a8f577
2025-07-31 15:21:14,196 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:14,196 - gaiav2.execution.tool_executor - INFO - Executing task 't16_select_hotel_city4': Select best hotel in city #4 (Action: select_data)
2025-07-31 15:21:14,197 - tools.select_data - INFO - select_data: Starting execution with request_id: 40459a0a-bd12-4078-ad46-79cfd9b0049a
2025-07-31 15:21:14,197 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:14,197 - gaiav2.execution.tool_executor - INFO - Executing task 't12_select_hotel_city3': Select best hotel in city #3 (Action: select_data)
2025-07-31 15:21:14,197 - tools.select_data - INFO - select_data: Starting execution with request_id: a2aa28e6-b19c-415b-a0a3-7160d5ac0262
2025-07-31 15:21:14,198 - root - INFO - Using max_tokens=4000 for model gpt-4o-mini
2025-07-31 15:21:28,948 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:28,954 - tools.select_data - INFO - select_data: Execution completed successfully in 14.76s
2025-07-31 15:21:28,954 - gaiav2.execution.tool_executor - INFO - Task 't4_select_hotel_city1' (Select best hotel in city #1) raw result: Type <class 'dict'>
2025-07-31 15:21:37,983 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:37,985 - tools.select_data - INFO - select_data: Execution completed successfully in 23.79s
2025-07-31 15:21:37,985 - gaiav2.execution.tool_executor - INFO - Task 't12_select_hotel_city3' (Select best hotel in city #3) raw result: Type <class 'dict'>
2025-07-31 15:21:37,987 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:37,988 - tools.select_data - INFO - select_data: Execution completed successfully in 23.79s
2025-07-31 15:21:37,988 - gaiav2.execution.tool_executor - INFO - Task 't16_select_hotel_city4' (Select best hotel in city #4) raw result: Type <class 'dict'>
2025-07-31 15:21:38,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:38,388 - tools.select_data - INFO - select_data: Execution completed successfully in 24.19s
2025-07-31 15:21:38,388 - gaiav2.execution.tool_executor - INFO - Task 't20_select_hotel_city5' (Select best hotel in city #5) raw result: Type <class 'dict'>
2025-07-31 15:21:39,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-31 15:21:39,958 - tools.select_data - INFO - select_data: Execution completed successfully in 25.77s
2025-07-31 15:21:39,959 - gaiav2.execution.tool_executor - INFO - Task 't8_select_hotel_city2' (Select best hotel in city #2) raw result: Type <class 'dict'>
2025-07-31 15:21:39,959 - gaiav2.execution.tool_executor - INFO - Task 't8_select_hotel_city2' completed successfully.
2025-07-31 15:21:39,960 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't8_select_hotel_city2' to context/tasks/t8_select_hotel_city2_result.json
2025-07-31 15:21:39,960 - gaiav2.execution.tool_executor - INFO - Task 't20_select_hotel_city5' completed successfully.
2025-07-31 15:21:39,961 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't20_select_hotel_city5' to context/tasks/t20_select_hotel_city5_result.json
2025-07-31 15:21:39,961 - gaiav2.execution.tool_executor - INFO - Task 't4_select_hotel_city1' completed successfully.
2025-07-31 15:21:39,963 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't4_select_hotel_city1' to context/tasks/t4_select_hotel_city1_result.json
2025-07-31 15:21:39,963 - gaiav2.execution.tool_executor - INFO - Task 't16_select_hotel_city4' completed successfully.
2025-07-31 15:21:39,965 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't16_select_hotel_city4' to context/tasks/t16_select_hotel_city4_result.json
2025-07-31 15:21:39,965 - gaiav2.execution.tool_executor - INFO - Task 't12_select_hotel_city3' completed successfully.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't12_select_hotel_city3' to context/tasks/t12_select_hotel_city3_result.json
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Adding task 't17_search_activity_city4' to current execution batch.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Adding task 't21_search_activity_city5' to current execution batch.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Adding task 't13_search_activity_city3' to current execution batch.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Adding task 't5_search_activity_city1' to current execution batch.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Adding task 't9_search_activity_city2' to current execution batch.
2025-07-31 15:21:39,966 - gaiav2.execution.tool_executor - INFO - Executing batch of 5 tasks: ['t17_search_activity_city4', 't21_search_activity_city5', 't13_search_activity_city3', 't5_search_activity_city1', 't9_search_activity_city2']
2025-07-31 15:21:39,967 - gaiav2.execution.tool_executor - INFO - Executing task 't17_search_activity_city4': Search activities near selected hotel in city #4 (Action: search_activity)
2025-07-31 15:21:39,967 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-07-31 15:21:39,967 - gaiav2.execution.tool_executor - INFO - Executing task 't21_search_activity_city5': Search activities near selected hotel in city #5 (Action: search_activity)
2025-07-31 15:21:39,967 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-07-31 15:21:39,968 - gaiav2.execution.tool_executor - INFO - Executing task 't13_search_activity_city3': Search activities near selected hotel in city #3 (Action: search_activity)
2025-07-31 15:21:39,968 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-07-31 15:21:39,968 - gaiav2.execution.tool_executor - INFO - Executing task 't5_search_activity_city1': Search activities near selected hotel in city #1 (Action: search_activity)
2025-07-31 15:21:39,969 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-07-31 15:21:39,972 - gaiav2.execution.tool_executor - INFO - Executing task 't9_search_activity_city2': Search activities near selected hotel in city #2 (Action: search_activity)
2025-07-31 15:21:39,972 - gaiav2.execution.tool_executor - WARNING - Tool function search_activity (search_places) is synchronous. Running in executor.
2025-07-31 15:21:41,151 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't13_search_activity_city3' (Action: search_activity): Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Novotel+Phuket+City+Phokeethra&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/lib/python3.13/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Novotel+Phuket+City+Phokeethra&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        query=query,
        ^^^^^^^^^^^^
    ...<7 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Novotel+Phuket+City+Phokeethra&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
2025-07-31 15:21:41,361 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't21_search_activity_city5' (Action: search_activity): Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Hua+Hin+Marriott+Resort+%26+Spa&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/lib/python3.13/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Hua+Hin+Marriott+Resort+%26+Spa&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        query=query,
        ^^^^^^^^^^^^
    ...<7 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Hua+Hin+Marriott+Resort+%26+Spa&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
2025-07-31 15:21:41,487 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't17_search_activity_city4' (Action: search_activity): Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Dusit+Thani+Krabi+Beach+Resort&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/lib/python3.13/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Dusit+Thani+Krabi+Beach+Resort&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        query=query,
        ^^^^^^^^^^^^
    ...<7 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Dusit+Thani+Krabi+Beach+Resort&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
2025-07-31 15:21:41,542 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't5_search_activity_city1' (Action: search_activity): Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Zayn+Hotel+Bangkok&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/lib/python3.13/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Zayn+Hotel+Bangkok&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        query=query,
        ^^^^^^^^^^^^
    ...<7 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Zayn+Hotel+Bangkok&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
2025-07-31 15:21:41,601 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't9_search_activity_city2' (Action: search_activity): Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Chiang+Mai+Hill+Hotel&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 116, in _make_request
    response.raise_for_status()
    ~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/lib/python3.13/site-packages/requests/models.py", line 1024, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Chiang+Mai+Hill+Hotel&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 341, in execute_task
    result = await self._execute_tool_function(action, processed_params)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 941, in _execute_tool_function
    return await loop.run_in_executor(None, partial_func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/concurrent/futures/thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps.py", line 59, in search_places
    return _default_tool.search_places(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        query=query,
        ^^^^^^^^^^^^
    ...<7 lines>...
        **kwargs
        ^^^^^^^^
    )
    ^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/maps/maps_tool.py", line 115, in search_places
    return self._make_request("GET", "/places/search", params=params)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/code/snipesearch/gaiav2/tools/base_tool.py", line 127, in _make_request
    raise Exception(error_msg)
Exception: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Chiang+Mai+Hill+Hotel&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - ERROR - Task 't17_search_activity_city4' execution failed: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Dusit+Thani+Krabi+Beach+Resort&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
NoneType: None
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - ERROR - Task 't21_search_activity_city5' execution failed: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Hua+Hin+Marriott+Resort+%26+Spa&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
NoneType: None
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - ERROR - Task 't13_search_activity_city3' execution failed: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Novotel+Phuket+City+Phokeethra&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
NoneType: None
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - ERROR - Task 't5_search_activity_city1' execution failed: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Zayn+Hotel+Bangkok&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
NoneType: None
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - ERROR - Task 't9_search_activity_city2' execution failed: Request failed: 500 Server Error: Internal Server Error for url: http://localhost:9284/places/search?query=tourist+attractions+near+Chiang+Mai+Hill+Hotel&radius=5000&debug=False&place_type=tourist_attraction&language=en&api_key=AIzaSyDzdccjUeTJWzz_iB9aQLml0Mm9GC9xK2U. Response: {"detail": "REQUEST_DENIED (You must enable Billing on the Google Cloud Project at https://console.cloud.google.com/project/_/billing/enable Learn more at https://developers.google.com/maps/gmp-get-started)"}
NoneType: None
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t18_select_activity_city4 as failed (blocked by failed dependency t17_search_activity_city4).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t6_select_activity_city1 as failed (blocked by failed dependency t5_search_activity_city1).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t23_generate_final_answers as failed (blocked by failed dependency t6_select_activity_city1).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t10_select_activity_city2 as failed (blocked by failed dependency t9_search_activity_city2).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t14_select_activity_city3 as failed (blocked by failed dependency t13_search_activity_city3).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Marking task t22_select_activity_city5 as failed (blocked by failed dependency t21_search_activity_city5).
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - WARNING - Execution halted. All 6 remaining tasks are blocked by upstream failures.
2025-07-31 15:21:41,603 - gaiav2.execution.tool_executor - INFO - All 23 tasks accounted for. Completed: 12, Failed: 11.
2025-07-31 15:21:41,606 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250731_152141.json
2025-07-31 15:21:41,606 - root - INFO - Step 6 complete: All tasks executed and result files generated.
