2025-05-18 17:07:46,668 - root - INFO - Attempting to load plan from: ../test_plan/plan_20250511_193326.json
2025-05-18 17:07:46,669 - root - INFO - Successfully loaded plan from ../test_plan/plan_20250511_193326.json
2025-05-18 17:07:46,670 - root - INFO - Step 2: Initializing WebPlanner and constructing graph
2025-05-18 17:07:46,670 - root - INFO - Step 2 complete: Graph constructed
2025-05-18 17:07:46,670 - root - INFO - Step 3: Plotting the plan graph
2025-05-18 17:07:46,671 - root - INFO - Using multipartite layout based on node depth, stretched horizontally.
2025-05-18 17:07:47,707 - root - INFO - Step 3 complete: Plan graph plotted
2025-05-18 17:07:47,708 - root - INFO - Step 4: Generating primitive execution schedule
2025-05-18 17:07:47,709 - root - INFO - Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.
2025-05-18 17:07:47,709 - root - INFO - Step 6: Executing all levels of tasks using the tools library
2025-05-18 17:07:47,713 - gaiav2.execution.tool_executor - INFO - Dependency graph built:
2025-05-18 17:07:47,715 - gaiav2.execution.tool_executor - INFO -   Task 't1_search_cities' has no dependencies.
2025-05-18 17:07:47,716 - gaiav2.execution.tool_executor - INFO -   Task 't2_rank_cities' depends on: ['t1_search_cities']
2025-05-18 17:07:47,718 - gaiav2.execution.tool_executor - INFO -   Task 't3_search_hotels_city1' depends on: ['t2_rank_cities']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't4_select_hotel_city1' depends on: ['t3_search_hotels_city1']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't5_search_activity_city1' depends on: ['t4_select_hotel_city1']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't6_select_activity_city1' depends on: ['t5_search_activity_city1']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't7_search_hotels_city2' depends on: ['t2_rank_cities']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't8_select_hotel_city2' depends on: ['t7_search_hotels_city2']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't9_search_activity_city2' depends on: ['t8_select_hotel_city2']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't10_select_activity_city2' depends on: ['t9_search_activity_city2']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't11_search_hotels_city3' depends on: ['t2_rank_cities']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't12_select_hotel_city3' depends on: ['t11_search_hotels_city3']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't13_search_activity_city3' depends on: ['t12_select_hotel_city3']
2025-05-18 17:07:47,720 - gaiav2.execution.tool_executor - INFO -   Task 't14_select_activity_city3' depends on: ['t13_search_activity_city3']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't15_search_hotels_city4' depends on: ['t2_rank_cities']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't16_select_hotel_city4' depends on: ['t15_search_hotels_city4']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't17_search_activity_city4' depends on: ['t16_select_hotel_city4']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't18_select_activity_city4' depends on: ['t17_search_activity_city4']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't19_search_hotels_city5' depends on: ['t2_rank_cities']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't20_select_hotel_city5' depends on: ['t19_search_hotels_city5']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't21_search_activity_city5' depends on: ['t20_select_hotel_city5']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't22_select_activity_city5' depends on: ['t21_search_activity_city5']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO -   Task 't23_generate_final_answers' depends on: ['t6_select_activity_city1', 't10_select_activity_city2', 't14_select_activity_city3', 't18_select_activity_city4', 't22_select_activity_city5', 't2_rank_cities', 't4_select_hotel_city1', 't8_select_hotel_city2', 't12_select_hotel_city3', 't16_select_hotel_city4', 't20_select_hotel_city5']
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO - Dependency graph validated: No cycles found.
2025-05-18 17:07:47,721 - root - INFO - Step 6.1: Registering task templates for dynamic task generation
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO - Registering task template: process_ranked_items
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO - Registering task template: process_selected_options
2025-05-18 17:07:47,721 - gaiav2.execution.tool_executor - INFO - Registering task template: conditional_high_score
2025-05-18 17:07:47,721 - root - INFO - Step 6.1 complete: Task templates registered
2025-05-18 17:09:48,759 - gaiav2.execution.tool_executor - INFO - Starting plan execution...
2025-05-18 17:09:48,760 - gaiav2.execution.tool_executor - INFO - Adding task 't1_search_cities' to current execution batch.
2025-05-18 17:09:48,761 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t1_search_cities']
2025-05-18 17:09:48,762 - gaiav2.execution.tool_executor - INFO - Executing task 't1_search_cities': Global search for best cities to visit in Thailand (Action: global_search)
2025-05-18 17:09:48,767 - gaiav2.execution.tool_executor - WARNING - Tool function global_search (search) is synchronous. Running in executor.
2025-05-18 17:10:12,534 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' (Global search for best cities to visit in Thailand) raw result: Type <class 'dict'>
2025-05-18 17:10:12,534 - gaiav2.execution.tool_executor - INFO - Task 't1_search_cities' completed successfully.
2025-05-18 17:10:12,538 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't1_search_cities' to context/tasks/t1_search_cities_result.json
2025-05-18 17:10:12,538 - gaiav2.execution.tool_executor - INFO - Adding task 't2_rank_cities' to current execution batch.
2025-05-18 17:10:12,538 - gaiav2.execution.tool_executor - INFO - Executing batch of 1 tasks: ['t2_rank_cities']
2025-05-18 17:10:12,538 - gaiav2.execution.tool_executor - INFO - Executing task 't2_rank_cities': Rank the top 5 Thai cities for the trip (Action: rank_data)
2025-05-18 17:10:12,553 - root - INFO - Using max_tokens=4000 for model o4-mini
2025-05-18 17:10:14,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-05-18 17:10:14,124 - tools.llm_tool_base - ERROR - rank_data: Error generating content: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-05-18 17:10:14,124 - tools.llm_tool_base - ERROR - rank_data: Error executing tool: Error code: 400 - {'error': {'message': "Unsupported parameter: 'max_tokens' is not supported with this model. Use 'max_completion_tokens' instead.", 'type': 'invalid_request_error', 'param': 'max_tokens', 'code': 'unsupported_parameter'}}
2025-05-18 17:10:14,124 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' (Rank the top 5 Thai cities for the trip) raw result: Type <class 'dict'>
2025-05-18 17:10:14,124 - gaiav2.execution.tool_executor - INFO - Task 't2_rank_cities' completed successfully.
2025-05-18 17:10:14,126 - gaiav2.execution.tool_executor - INFO - Saved result and input parameters for task 't2_rank_cities' to context/tasks/t2_rank_cities_result.json
2025-05-18 17:10:14,126 - gaiav2.execution.tool_executor - INFO - Adding task 't11_search_hotels_city3' to current execution batch.
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Adding task 't7_search_hotels_city2' to current execution batch.
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Adding task 't15_search_hotels_city4' to current execution batch.
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Adding task 't19_search_hotels_city5' to current execution batch.
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Adding task 't3_search_hotels_city1' to current execution batch.
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Executing batch of 5 tasks: ['t11_search_hotels_city3', 't7_search_hotels_city2', 't15_search_hotels_city4', 't19_search_hotels_city5', 't3_search_hotels_city1']
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - INFO - Executing task 't11_search_hotels_city3': Search hotels in top city #3 (Action: search_hotel)
2025-05-18 17:10:14,127 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.ranked_items[2].item': 'data'. Current object type: <class 'dict'>.
2025-05-18 17:10:14,128 - gaiav2.execution.tool_executor - ERROR - Action 'search_hotel' is missing required 'destination' parameter or it resolved to None/empty. Processed parameters: {'destination': None, 'check_in': '2025-10-01', 'check_out': '2025-10-30', 'adults': 1, 'children': 0, 'currency': 'USD'}
2025-05-18 17:10:14,128 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't11_search_hotels_city3' (Action: search_hotel): Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 336, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 605, in _execute_tool_function
    raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")
ValueError: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
2025-05-18 17:10:14,135 - gaiav2.execution.tool_executor - INFO - Executing task 't7_search_hotels_city2': Search hotels in top city #2 (Action: search_hotel)
2025-05-18 17:10:14,135 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.ranked_items[1].item': 'data'. Current object type: <class 'dict'>.
2025-05-18 17:10:14,135 - gaiav2.execution.tool_executor - ERROR - Action 'search_hotel' is missing required 'destination' parameter or it resolved to None/empty. Processed parameters: {'destination': None, 'check_in': '2025-10-01', 'check_out': '2025-10-30', 'adults': 1, 'children': 0, 'currency': 'USD'}
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't7_search_hotels_city2' (Action: search_hotel): Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 336, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 605, in _execute_tool_function
    raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")
ValueError: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - INFO - Executing task 't15_search_hotels_city4': Search hotels in top city #4 (Action: search_hotel)
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.ranked_items[3].item': 'data'. Current object type: <class 'dict'>.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Action 'search_hotel' is missing required 'destination' parameter or it resolved to None/empty. Processed parameters: {'destination': None, 'check_in': '2025-10-01', 'check_out': '2025-10-30', 'adults': 1, 'children': 0, 'currency': 'USD'}
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't15_search_hotels_city4' (Action: search_hotel): Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 336, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 605, in _execute_tool_function
    raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")
ValueError: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - INFO - Executing task 't19_search_hotels_city5': Search hotels in top city #5 (Action: search_hotel)
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.ranked_items[4].item': 'data'. Current object type: <class 'dict'>.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Action 'search_hotel' is missing required 'destination' parameter or it resolved to None/empty. Processed parameters: {'destination': None, 'check_in': '2025-10-01', 'check_out': '2025-10-30', 'adults': 1, 'children': 0, 'currency': 'USD'}
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't19_search_hotels_city5' (Action: search_hotel): Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 336, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 605, in _execute_tool_function
    raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")
ValueError: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - INFO - Executing task 't3_search_hotels_city1': Search hotels in top city #1 (Action: search_hotel)
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - WARNING - Error accessing 'data' in path 'data.ranked_items[0].item': 'data'. Current object type: <class 'dict'>.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Action 'search_hotel' is missing required 'destination' parameter or it resolved to None/empty. Processed parameters: {'destination': None, 'check_in': '2025-10-01', 'check_out': '2025-10-30', 'adults': 1, 'children': 0, 'currency': 'USD'}
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Error during execution of task 't3_search_hotels_city1' (Action: search_hotel): Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 336, in execute_task
    result = await self._execute_tool_function(action, processed_params)
  File "/Users/<USER>/code/snipesearch/gaiav2/execution/tool_executor.py", line 605, in _execute_tool_function
    raise ValueError(f"Action '{action}' missing or has invalid 'destination' parameter after processing.")
ValueError: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Task 't11_search_hotels_city3' execution failed: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
NoneType: None
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Task 't7_search_hotels_city2' execution failed: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
NoneType: None
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Task 't15_search_hotels_city4' execution failed: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
NoneType: None
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Task 't19_search_hotels_city5' execution failed: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
NoneType: None
2025-05-18 17:10:14,136 - gaiav2.execution.tool_executor - ERROR - Task 't3_search_hotels_city1' execution failed: Action 'search_hotel' missing or has invalid 'destination' parameter after processing.
NoneType: None
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR - Deadlock: No tasks ready, none running, but 16 tasks pending and not directly blocked by failed dependencies.
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t10_select_activity_city2 (deadlocked) unmet non-failed dependencies: ['t9_search_activity_city2']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t12_select_hotel_city3 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t17_search_activity_city4 (deadlocked) unmet non-failed dependencies: ['t16_select_hotel_city4']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t14_select_activity_city3 (deadlocked) unmet non-failed dependencies: ['t13_search_activity_city3']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t9_search_activity_city2 (deadlocked) unmet non-failed dependencies: ['t8_select_hotel_city2']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t5_search_activity_city1 (deadlocked) unmet non-failed dependencies: ['t4_select_hotel_city1']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t22_select_activity_city5 (deadlocked) unmet non-failed dependencies: ['t21_search_activity_city5']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t20_select_hotel_city5 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t23_generate_final_answers (deadlocked) unmet non-failed dependencies: ['t6_select_activity_city1', 't18_select_activity_city4', 't4_select_hotel_city1', 't8_select_hotel_city2', 't16_select_hotel_city4']
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t18_select_activity_city4 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t21_search_activity_city5 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t6_select_activity_city1 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t16_select_hotel_city4 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t8_select_hotel_city2 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t4_select_hotel_city1 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - ERROR -   Task t13_search_activity_city3 (deadlocked) unmet non-failed dependencies: []
2025-05-18 17:10:14,137 - gaiav2.execution.tool_executor - INFO - All 23 tasks accounted for. Completed: 2, Failed: 21.
2025-05-18 17:10:14,139 - gaiav2.execution.tool_executor - INFO - Execution summary saved to context/execution_summary_20250518_171014.json
2025-05-18 17:10:14,139 - root - INFO - Step 6 complete: All tasks executed and result files generated.
