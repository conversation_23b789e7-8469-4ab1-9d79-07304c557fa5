2025-05-18 19:10:49,108 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-05-18 19:10:49,108 - root - INFO - DECOMPOSE | Sending planner prompt to LLM...
2025-05-18 19:10:49,108 - root - INFO - Setting up parameters for thinking model: o3-mini
2025-05-18 19:10:49,108 - root - INFO - Using max_completion_tokens=4000 for thinking model: o3-mini
2025-05-18 19:10:49,108 - root - INFO - Setting reasoning_effort=high for thinking model: o3-mini
2025-05-18 19:10:49,108 - root - INFO - Using thinking model: o3-mini
2025-05-18 19:10:49,108 - root - INFO - Setting reasoning_effort=high for model o3-mini
2025-05-18 19:10:49,108 - root - INFO - Using max_completion_tokens=4000 for model o3-mini
2025-05-18 19:11:22,580 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-18 19:11:22,602 - root - ERROR - DECOMPOSE | Empty or failed response from LLM. Returning query as question.
2025-05-18 19:11:22,603 - root - INFO - Step 1 (Iteration 1): Decomposition resulted in a question.
