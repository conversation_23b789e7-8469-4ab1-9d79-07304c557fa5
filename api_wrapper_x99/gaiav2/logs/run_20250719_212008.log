2025-07-19 21:20:15,524 - root - INFO - OpenRouter provider initialized
2025-07-19 21:20:15,524 - root - INFO - Step 1 (Iteration 1/5): Decomposing query...
2025-07-19 21:20:21,691 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-19 21:20:22,423 - root - ERROR - DECOMPOSE | Unexpected error during query decomposition: name 'tools_names' is not defined
2025-07-19 21:20:22,430 - root - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/code/snipesearch/gaiav2/planners/QueryDecomposer.py", line 35, in decompose_query
    prompt = get_decomposition_prompt(main_query, user_context, tools)
  File "/Users/<USER>/code/snipesearch/gaiav2/prompts/decomposition_prompts.py", line 308, in get_decomposition_prompt
    """+tools_names+"""
NameError: name 'tools_names' is not defined

2025-07-19 21:20:22,430 - root - INFO - Step 1 (Iteration 1): Decomposition resulted in a question.
