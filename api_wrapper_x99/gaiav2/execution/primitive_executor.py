import networkx as nx
import logging
from typing import List, Dict, Any, Set, Tuple, Optional

class PrimitiveExecutor:
    """
    Parses an HTN plan and generates an execution schedule for primitive tasks,
    respecting dependencies and maximizing parallelism.
    """
    def __init__(self, tasks: List[Dict[str, Any]], methods: List[Dict[str, Any]]):
        if not tasks:
            raise ValueError("Tasks list cannot be empty.")
        self.tasks = tasks
        self.methods = methods
        self.tasks_by_id = {t["id"]: t for t in tasks}
        self.methods_by_task = {m.get('task_id'): m for m in methods}
        self.primitive_graph = self._build_primitive_graph()
        self.schedule: List[Set[str]] = []

    def _build_primitive_graph(self) -> nx.DiGraph:
        """
        Constructs the dependency graph for primitive tasks based on HTN structure.
        """
        G = nx.DiGraph()
        primitive_ids = set()

        # Add primitive nodes
        for task in self.tasks:
            if task.get("type") == "primitive":
                prim_id = task["id"]
                G.add_node(prim_id, name=task.get('name', prim_id)) # Add name for clarity
                primitive_ids.add(prim_id)

        if not primitive_ids:
            logging.warning("No primitive tasks found in the plan.")
            return G

        # Add edges based on primitive preconditions
        for task_id in primitive_ids:
            task = self.tasks_by_id[task_id]
            for pre in task.get("preconditions", []):
                # Assuming precondition format like "task_id completed"
                pre_task_id = pre.split()[0]
                if pre_task_id in primitive_ids:
                    G.add_edge(pre_task_id, task_id)
                elif pre_task_id in self.tasks_by_id and self.tasks_by_id[pre_task_id].get("type") == "compound":
                     # If precond is a compound task, link from its *last* primitive
                    last_prim_in_pre = self._find_last_primitive(pre_task_id)
                    if last_prim_in_pre and last_prim_in_pre in primitive_ids:
                         G.add_edge(last_prim_in_pre, task_id)
                    else:
                         logging.warning(f"Could not find last primitive for compound precondition '{pre_task_id}' of task '{task_id}'")

        # Add sequential edges within ordered methods
        for method in self.methods:
            if method.get("ordering") == "ordered":
                subtasks = sorted(method.get("subtasks", []), key=lambda s: s.get("order", 0))
                for i in range(len(subtasks) - 1):
                    prev_sub_info = subtasks[i]
                    next_sub_info = subtasks[i+1]

                    # Only add edge if orders differ
                    if prev_sub_info.get("order") != next_sub_info.get("order"):
                        last_prims_prev = self._find_all_primitives_of_task(prev_sub_info["id"])
                        first_prims_next = self._find_all_primitives_of_task(next_sub_info["id"])

                        for p_prev in last_prims_prev:
                             if p_prev not in primitive_ids: continue
                             for p_next in first_prims_next:
                                 if p_next not in primitive_ids: continue
                                 # Add edge from *all* last primitives of prev order group
                                 # to *all* first primitives of next order group
                                 G.add_edge(p_prev, p_next)

        # Add cross-component ordering for the top-level ordered method
        top_methods = self._find_top_methods()
        if top_methods:
             # Assuming only one top method for simplicity, could be extended
            root_method = top_methods[0]
            if root_method.get("ordering") == "ordered":
                 ordered_comps = sorted(root_method.get('subtasks', []), key=lambda s: s.get('order', 0))
                 for i in range(len(ordered_comps) - 1):
                     comp_prev_info = ordered_comps[i]
                     comp_next_info = ordered_comps[i+1]

                     # Only add edge if orders differ
                     if comp_prev_info.get("order") != comp_next_info.get("order"):
                         last_prims_prev_comp = self._find_all_primitives_of_task(comp_prev_info["id"])
                         first_prims_next_comp = self._find_all_primitives_of_task(comp_next_info["id"])

                         for p_prev in last_prims_prev_comp:
                             if p_prev not in primitive_ids: continue
                             for p_next in first_prims_next_comp:
                                 if p_next not in primitive_ids: continue
                                 G.add_edge(p_prev, p_next)

        # Ensure it's a DAG
        if not nx.is_directed_acyclic_graph(G):
            cycles = list(nx.simple_cycles(G))
            logging.error(f"Primitive graph contains cycles! Cycles found: {cycles}")
            # Handle cycle error, maybe raise exception or return empty graph
            raise ValueError(f"Cycle detected in primitive graph: {cycles}")

        return G

    def _find_all_primitives_of_task(self, task_id: str) -> Set[str]:
        """ Recursively find all primitive task IDs under a given task_id. """
        primitives = set()
        task = self.tasks_by_id.get(task_id)
        if not task: return primitives

        if task.get('type') == 'primitive':
            primitives.add(task_id)
        elif task.get('type') == 'compound':
            method = self.methods_by_task.get(task_id)
            if method:
                for sub_info in method.get('subtasks', []):
                    primitives.update(self._find_all_primitives_of_task(sub_info["id"]))
        return primitives

    def _find_last_primitive(self, task_id: str) -> Optional[str]:
         """ Find the ID of the chronologically last primitive task under a (potentially compound) task. """
         task = self.tasks_by_id.get(task_id)
         if not task: return None

         if task.get('type') == 'primitive':
             return task_id
         elif task.get('type') == 'compound':
             method = self.methods_by_task.get(task_id)
             if not method: return None

             subtasks = sorted(method.get('subtasks', []), key=lambda s: s.get("order", 0), reverse=True)
             if not subtasks: return None

             # Find the last primitive from the last subtask(s)
             # Need to handle potential parallelism in the last step
             last_order = subtasks[0].get('order')
             last_prims = set()
             for sub_info in subtasks:
                 if sub_info.get('order') == last_order:
                     # Recursively find the last primitive within this subtask
                     # This might return None if the subtask itself is compound with no primitives
                     prim = self._find_last_primitive(sub_info["id"])
                     if prim:
                         last_prims.add(prim)
                 else:
                     break # Already processed all subtasks of the last order

             # If multiple primitives could be last (parallel), this logic might need refinement.
             # For now, return one if found.
             return next(iter(last_prims), None)
         return None

    def _find_top_methods(self) -> List[Dict[str, Any]]:
         """ Find methods that are not subtasks of any other method. """
         all_subtask_ids = {sub['id'] for m in self.methods for sub in m.get('subtasks', [])}
         top_task_ids = set(self.methods_by_task.keys()) - all_subtask_ids
         return [m for tid, m in self.methods_by_task.items() if tid in top_task_ids]

    def generate_execution_schedule(self) -> List[Set[str]]:
        """
        Generates a parallel execution schedule based on the primitive graph.
        Returns a list of sets, where each set contains tasks for a parallel step.
        """
        if not self.primitive_graph:
            logging.warning("Primitive graph is empty, cannot generate schedule.")
            return []

        # Use a copy to modify during processing
        graph_copy = self.primitive_graph.copy()
        schedule = []
        processed_nodes = set()

        while graph_copy.nodes:
            # Find nodes with in-degree 0 in the current graph
            ready_nodes = {node for node, degree in graph_copy.in_degree() if degree == 0}

            if not ready_nodes:
                # This should not happen in a DAG unless there's an issue
                remaining_nodes = list(graph_copy.nodes())
                logging.error(f"Error generating schedule: No nodes with in-degree 0 found, but graph is not empty. Remaining nodes: {remaining_nodes}")
                # Attempt to find cycles again just in case
                if not nx.is_directed_acyclic_graph(graph_copy):
                     cycles = list(nx.simple_cycles(graph_copy))
                     raise ValueError(f"Cycle detected during schedule generation: {cycles}")
                else:
                     raise RuntimeError("Cannot find nodes to schedule in a supposed DAG.")

            # Add the set of ready nodes as the next step in the schedule
            schedule.append(ready_nodes)
            processed_nodes.update(ready_nodes)

            # Remove the processed nodes from the graph copy
            graph_copy.remove_nodes_from(ready_nodes)

        self.schedule = schedule
        return self.schedule

    def print_schedule(self):
        """ Prints the generated execution schedule in a readable format, highlighting dependencies. """
        if not self.schedule:
            print("No execution schedule generated.")
            return

        print("\n--- Primitive Execution Schedule ---")
        all_previous_step_tasks = set()
        for i, step_tasks in enumerate(self.schedule):
            step_num = i + 1
            print(f"\nStep {step_num}: Run in Parallel ({len(step_tasks)} tasks)")
            if i > 0:
                print(f"  (Depends on completion of all tasks in Step {i})")

            # Sort for consistent output, get task names
            sorted_tasks = sorted(list(step_tasks))
            for task_id in sorted_tasks:
                # Find direct predecessors for this task that were in the previous step(s)
                predecessors = set(self.primitive_graph.predecessors(task_id))
                relevant_deps = predecessors.intersection(all_previous_step_tasks)

                # Get action and params for current task
                current_task_details = self.tasks_by_id.get(task_id, {})
                action = current_task_details.get('orchestration_action', 'UNKNOWN_ACTION')
                params = current_task_details.get('parameters', {})
                task_str = f"{action}({params})"

                # Get action and params for dependencies
                dep_strs = []
                if relevant_deps:
                    for dep_id in sorted(list(relevant_deps)):
                        dep_task_details = self.tasks_by_id.get(dep_id, {})
                        dep_action = dep_task_details.get('orchestration_action', 'UNKNOWN')
                        dep_params = dep_task_details.get('parameters', {})
                        dep_strs.append(f"{dep_action}(...) #{dep_id}") # Abbreviate params for waits on
                    dep_str = f" (waits on: [{', '.join(dep_strs)}])"
                else:
                    dep_str = ""

                print(f"  - {task_id}: {task_str}{dep_str}")

            # Update the set of all tasks completed up to this point
            all_previous_step_tasks.update(step_tasks)

        print("\n-----------------------------------")

    def generate_action_map(self) -> Dict[str, Dict[str, Any]]:
        """
        Creates a mapping from primitive task IDs to their action and parameters.
        """
        action_map = {}
        for task_id in self.primitive_graph.nodes():
            task_details = self.tasks_by_id.get(task_id)
            if task_details and task_details.get("type") == "primitive":
                action_map[task_id] = {
                    "action": task_details.get("orchestration_action", "UNKNOWN_ACTION"),
                    "parameters": task_details.get("parameters", {}),
                    "name": task_details.get("name", task_id) # Keep name for context
                }
            else:
                logging.warning(f"Task ID {task_id} found in primitive graph but not in original tasks list or not primitive.")
        return action_map

# Add methods for schedule generation and printing here later