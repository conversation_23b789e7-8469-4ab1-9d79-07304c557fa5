#!/usr/bin/env python3
"""
Test script for enhanced reference resolution.

This script demonstrates the enhanced reference resolution capabilities
for the standardized output format.
"""

import os
import sys
import json
import logging
from typing import Dict, Any

# Add the parent directory to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the reference resolver
from gaiav2.utils.reference_resolver import ReferenceResolver
from gaiav2.utils.output_helpers import (
    is_standardized_output, get_data, get_metadata, get_error,
    is_success, get_field, get_ranked_items, get_top_ranked_item,
    get_selected_options, get_top_selected_option, get_final_answer,
    get_summary, get_recommendations
)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_results() -> Dict[str, Any]:
    """Create sample results for testing."""
    return {
        "results_of_t1": {
            "status": "success",
            "tool_name": "rank_data",
            "timestamp": "2025-05-10T12:34:56Z",
            "request_id": "rd-123456",
            "execution_time": 1.2,
            "metadata": {
                "criteria_used": "Price, rating, and amenities",
                "ranking_method": "Weighted scoring",
                "items_count": 4,
                "average_score": 85.5,
                "model_used": "o4-mini"
            },
            "data": {
                "ranked_items": [
                    {
                        "rank": 1,
                        "item": {
                            "name": "Grand Hotel",
                            "price": 250,
                            "rating": 4.5,
                            "amenities": ["pool", "spa", "gym"]
                        },
                        "score": 92,
                        "explanation": "Best overall value with excellent amenities"
                    },
                    {
                        "rank": 2,
                        "item": {
                            "name": "City Center Hotel",
                            "price": 200,
                            "rating": 4.2,
                            "amenities": ["wifi", "gym"]
                        },
                        "score": 85,
                        "explanation": "Good location and reasonable price"
                    }
                ],
                "ranking_summary": "Hotels ranked by overall value",
                "top_item": {
                    "name": "Grand Hotel",
                    "price": 250,
                    "rating": 4.5,
                    "amenities": ["pool", "spa", "gym"]
                },
                "bottom_item": {
                    "name": "Budget Inn",
                    "price": 120,
                    "rating": 3.8,
                    "amenities": ["wifi"]
                }
            }
        },
        "results_of_t2": {
            "status": "success",
            "tool_name": "select_data",
            "timestamp": "2025-05-10T12:35:56Z",
            "request_id": "sd-123456",
            "execution_time": 0.95,
            "metadata": {
                "criteria_applied": "Price under $300, rating above 4.0",
                "topk": 2,
                "options_considered": 5,
                "model_used": "o4-mini"
            },
            "data": {
                "selected_options": [
                    {
                        "option": {
                            "name": "City Center Hotel",
                            "price": 200,
                            "rating": 4.2,
                            "amenities": ["wifi", "gym"]
                        },
                        "explanation": "Best balance of price and location",
                        "confidence": 0.92
                    },
                    {
                        "option": {
                            "name": "Riverside Lodge",
                            "price": 220,
                            "rating": 4.3,
                            "amenities": ["pool", "restaurant"]
                        },
                        "explanation": "Great amenities for the price",
                        "confidence": 0.85
                    }
                ],
                "alternatives_considered": [
                    {
                        "option": {
                            "name": "Luxury Resort",
                            "price": 350,
                            "rating": 4.8,
                            "amenities": ["pool", "spa", "gym", "restaurant"]
                        },
                        "reason_not_selected": "Price exceeds budget constraint"
                    }
                ]
            }
        },
        "results_of_t3": {
            "status": "success",
            "tool_name": "generate_final_answers",
            "timestamp": "2025-05-10T12:36:56Z",
            "request_id": "gfa-123456",
            "execution_time": 2.45,
            "metadata": {
                "original_question": "What is the best hotel for my trip?",
                "format": "markdown",
                "model_used": "o4-mini"
            },
            "data": {
                "answer": "# Best Hotel Recommendations\n\nBased on your criteria, the **City Center Hotel** is the best option for your trip. It offers a great balance of price ($200/night) and location, with a solid 4.2 rating.\n\nThe **Riverside Lodge** is also an excellent choice at $220/night with a 4.3 rating and amenities including a pool and restaurant.",
                "sections": [
                    {
                        "title": "Best Hotel Recommendations",
                        "content": "Based on your criteria, the **City Center Hotel** is the best option for your trip. It offers a great balance of price ($200/night) and location, with a solid 4.2 rating.\n\nThe **Riverside Lodge** is also an excellent choice at $220/night with a 4.3 rating and amenities including a pool and restaurant."
                    }
                ],
                "summary": "City Center Hotel offers the best value with a good balance of price, location, and amenities.",
                "recommendations": [
                    "Book City Center Hotel for the best overall value",
                    "Consider Riverside Lodge if you want a pool and restaurant",
                    "Avoid Luxury Resort if you're on a budget"
                ]
            }
        }
    }

def test_reference_resolution():
    """Test the enhanced reference resolution capabilities."""
    # Create sample results
    results = create_sample_results()
    
    # Create a reference resolver
    resolver = ReferenceResolver(results)
    
    # Test references
    test_references = [
        # Basic references
        "results_of_t1",
        "output_of_t2",
        "ranked_items_from_t1",
        
        # Nested field access
        "results_of_t1.data.ranked_items",
        "results_of_t1.metadata.criteria_used",
        "results_of_t1.data.ranking_summary",
        
        # Array indexing
        "results_of_t1.data.ranked_items[0]",
        "results_of_t1.data.ranked_items[1].item.name",
        "results_of_t1.data.ranked_items[0].item.amenities[0]",
        
        # Field references
        "data_from_t1",
        "metadata_from_t2",
        "ranked_items_from_t1",
        
        # Complex nested references
        "results_of_t2.data.selected_options[0].option.amenities[1]",
        "results_of_t3.data.recommendations[0]",
        "results_of_t3.data.sections[0].title"
    ]
    
    print("Testing Enhanced Reference Resolution\n")
    print("=" * 80)
    
    for reference in test_references:
        print(f"\nReference: {reference}")
        result = resolver.resolve_reference(reference)
        
        # Print the result type and a preview
        print(f"Result type: {type(result).__name__}")
        if isinstance(result, dict):
            print(f"Result preview: {json.dumps(result, indent=2)[:200]}...")
        elif isinstance(result, list):
            print(f"Result preview: {json.dumps(result, indent=2)[:200]}...")
        else:
            print(f"Result: {result}")
    
    print("\n" + "=" * 80)
    print("\nTesting Output Helper Functions\n")
    print("=" * 80)
    
    # Test output helper functions
    t1_result = results["results_of_t1"]
    t2_result = results["results_of_t2"]
    t3_result = results["results_of_t3"]
    
    print("\nTesting with rank_data output (t1):")
    print(f"is_standardized_output: {is_standardized_output(t1_result)}")
    print(f"is_success: {is_success(t1_result)}")
    print(f"get_field('data.ranking_summary'): {get_field(t1_result, 'data.ranking_summary')}")
    print(f"get_top_ranked_item().name: {get_top_ranked_item(t1_result)['item']['name']}")
    
    print("\nTesting with select_data output (t2):")
    print(f"get_top_selected_option().name: {get_top_selected_option(t2_result)['option']['name']}")
    print(f"get_field('data.selected_options[0].confidence'): {get_field(t2_result, 'data.selected_options[0].confidence')}")
    
    print("\nTesting with generate_final_answers output (t3):")
    print(f"get_summary(): {get_summary(t3_result)}")
    print(f"get_recommendations()[0]: {get_recommendations(t3_result)[0]}")
    print(f"get_final_answer() preview: {get_final_answer(t3_result)[:100]}...")

if __name__ == "__main__":
    test_reference_resolution()
