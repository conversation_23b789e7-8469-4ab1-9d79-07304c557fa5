import asyncio
import os
import logging
import networkx as nx
import plotly.graph_objects as go
from dotenv import load_dotenv
import datetime # Added for timestamping log files
import json
import sys
from typing import Optional # Added for Python 3.9 compatibility
# import argparse # Removed argparse

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from visualization.graph_visualization import GraphVisualizer
from planners import QueryDecomposer, WebPlanner
# Import the new LLM module
from llm import LLM
import llm_config
from execution.primitive_executor import PrimitiveExecutor

# --- Improved Logging Setup ---
# Ensure logs and context directories exist
log_dir = "logs"
context_dir = "context"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)
if not os.path.exists(context_dir):
    os.makedirs(context_dir)

# Create a timestamped log file name
log_filename = os.path.join(log_dir, f"run_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

# Configure logging to file and console
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# File Handler
file_handler = logging.FileHandler(log_filename)
file_handler.setFormatter(log_formatter)
file_handler.setLevel(logging.INFO) # Or desired level for file logs

# Console Handler (optional, keep if you want console output too)
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)
console_handler.setLevel(logging.INFO) # Or desired level for console logs

# Get the root logger and add handlers
logger = logging.getLogger()
logger.setLevel(logging.INFO) # Set root logger level
logger.addHandler(file_handler)
logger.addHandler(console_handler)

# Remove basicConfig if it was previously called implicitly or explicitly
# (Adding handlers to the root logger replaces basicConfig)
# ------------------------------

# --- Argument Parser Setup --- (REMOVED)
# parser = argparse.ArgumentParser(description="Run GaiaV2 with optional plan loading.")
# parser.add_argument("--load-plan", type=str, help="Path to a JSON file containing a pre-existing plan to load.")

async def main(load_plan_path: Optional[str] = None): # Changed type hint for Python 3.9 compatibility
    """Main entry point of the application."""
    load_dotenv() # Load environment variables from .env file

    # args = parser.parse_args() # Removed argparse parsing

    # Initialize LLM with OpenAI provider
    llm = LLM(provider="openai")

    final_plan = None
    user_query = "" # Initialize user_query

    if load_plan_path: # Use the function argument
        logging.info(f"Attempting to load plan from: {load_plan_path}")
        try:
            with open(load_plan_path, 'r') as f:
                final_plan = json.load(f)
            logging.info(f"Successfully loaded plan from {load_plan_path}")

            if not isinstance(final_plan, dict) or "tasks" not in final_plan:
                logging.error("Loaded plan is not a valid dictionary with a 'tasks' key.")
                print(f"Error: The plan file {load_plan_path} does not seem to be a valid plan format.")
                return

            print("\n=== Plan Loaded from File ===")
            print(json.dumps(final_plan, indent=2))

            # Attempt to get user_query if stored in the plan, otherwise use a placeholder
            user_query = final_plan.get("original_user_query",
                                      final_plan.get("user_query_if_available",
                                                     "Loaded from file, original query not in plan JSON"))

        except FileNotFoundError:
            logging.error(f"Plan file not found: {load_plan_path}")
            print(f"Error: Plan file not found at {load_plan_path}")
            return
        except json.JSONDecodeError:
            logging.error(f"Invalid JSON in plan file: {load_plan_path}")
            print(f"Error: Could not decode JSON from plan file {load_plan_path}")
            return
        except Exception as e:
            logging.error(f"An unexpected error occurred while loading the plan: {e}", exc_info=True)
            print(f"An unexpected error occurred loading plan: {e}")
            return
    else:
        # Get user input for query - only if not loading a plan
        user_query = input("Enter your main query: ")
        query_decomposer = QueryDecomposer(llm=llm)
        current_context = "" # Accumulates questions and answers
        max_iterations = 5 # Prevent infinite loops
        for i in range(max_iterations):
            logging.info(f"Step 1 (Iteration {i+1}/{max_iterations}): Decomposing query...")
            decomposition_result = await query_decomposer.decompose_query(user_query, current_context)

            if isinstance(decomposition_result, dict):
                logging.info("Step 1 complete: Decomposition successful.")
                final_plan = decomposition_result

                # Save the plan to a JSON file
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                plan_filename = os.path.join(context_dir, f"plan_{timestamp}.json")
                with open(plan_filename, 'w') as f:
                    json.dump(final_plan, f, indent=2)
                logging.info(f"Plan saved to {plan_filename}")

                print("\n=== Final HTN Plan ===")
                print(json.dumps(final_plan, indent=2))
                print(f"\nPlan saved to {plan_filename}")
                break # Exit loop, we have a plan
            elif isinstance(decomposition_result, str):
                question = decomposition_result
                logging.info(f"Step 1 (Iteration {i+1}): Decomposition resulted in a question.")
                print(f"\nLLM needs more information (Attempt {i+1}):")
                print(question)
                user_feedback = input("\nYour answer: ")
                current_context += f"\nQ: {question}\nA: {user_feedback}"
                if i == max_iterations - 1:
                    logging.error("DECOMPOSITION | Max iterations reached without generating a plan.")
                    print("Error: Could not generate a plan after multiple attempts.")
                    return # Exit if max iterations reached
            else:
                logging.error(f"DECOMPOSITION | Unexpected result type: {type(decomposition_result)}")
                print("Error: Decomposition failed unexpectedly.")
                return # Exit

    # Initialize planners and QueryDecomposer
    web_planner = WebPlanner()

    # Step 1: Decompose the query
    # final_plan = None # Moved up
    # current_context = "" # Moved into else block
    # max_iterations = 5 # Moved into else block
    # The loop for decomposition is now inside the 'else' block for 'if args.load_plan'

    # Step 2: Build the plan graph
    logging.info("Step 2: Initializing WebPlanner and constructing graph")
    if not final_plan: # Ensure final_plan is available
        logging.error("No plan available to build the graph. Exiting.")
        print("Critical Error: No plan was loaded or generated.")
        return

    web_planner.add_plan(final_plan)
    logging.info("Step 2 complete: Graph constructed")

    # Step 3: Plot the plan graph
    logging.info("Step 3: Plotting the plan graph")
    try:
        web_planner.print_graph()
        plan_graph = web_planner.get_graph()
        visualizer = GraphVisualizer() # Instantiate the visualizer
        visualizer.plot_plan_interactive(plan_graph) # Call the method
        logging.info("Step 3 complete: Plan graph plotted")

    except Exception as e:
        logging.error(f"Error processing or plotting the plan: {e}", exc_info=True)
        print("An error occurred while processing the plan.")

    if not final_plan:
        print("Could not obtain a final plan.")
        return

    # Step 4: Generate and Print Primitive Execution Schedule
    logging.info("Step 4: Generating primitive execution schedule")
    try:
        # PrimitiveExecutor might be part of an older flow.
        # ToolExecutor now handles primitives directly from the plan.
        # If PrimitiveExecutor is still needed for some reason, ensure it's compatible.
        # For now, assuming final_plan['tasks'] is what ToolExecutor needs.
        # executor = PrimitiveExecutor(final_plan.get('tasks', []), final_plan.get('methods', []))
        # execution_schedule = executor.generate_execution_schedule()
        # executor.print_schedule()

        # This part might be skippable if ToolExecutor is the primary execution engine
        # and it derives its needs from final_plan.
        # Let's comment out the old schedule/action_map generation if it's redundant
        logging.info("Skipping legacy PrimitiveExecutor schedule/action_map generation as ToolExecutor will handle it.")

        # Save the execution schedule to a JSON file
        # timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        # schedule_filename = os.path.join(context_dir, f"schedule_{timestamp}.json")
        # serializable_schedule = [list(level) for level in execution_schedule]
        # with open(schedule_filename, 'w') as f:
        #    json.dump(serializable_schedule, f, indent=2)
        # logging.info(f"Execution schedule saved to {schedule_filename}")
        # print(f"\nExecution schedule saved to {schedule_filename}")
        # logging.info("Step 4 complete: Primitive schedule generated and printed.")

        # Step 5: Generate Action Map
        # logging.info("Step 5: Generating action map for primitive tasks")
        # action_map = executor.generate_action_map()
        # print("\n--- Action Map (Task ID -> Action/Params) ---")
        # print(json.dumps(action_map, indent=2))
        # timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        # action_map_filename = os.path.join(context_dir, f"action_map_{timestamp}.json")
        # with open(action_map_filename, 'w') as f:
        #    json.dump(action_map, f, indent=2)
        # logging.info(f"Action map saved to {action_map_filename}")
        # print(f"\nAction map saved to {action_map_filename}")
        # logging.info("Step 5 complete: Action map generated.")

    except Exception as e:
        logging.error(f"Error during legacy schedule/action map generation (Step 4/5): {e}", exc_info=True)

    # Step 6: Execute all levels of tasks using the tools library
    from execution.tool_executor import ToolExecutor # Ensures ToolExecutor is imported before use
    logging.info("Step 6: Executing all levels of tasks using the tools library")
    # Ensure user_query is defined. If loading from plan, it might not be directly available unless stored in the plan.
    # current_user_query = final_plan["original_user_query"]
    # The user_query variable is now set consistently above, whether loading from plan or input.

    tool_executor = ToolExecutor(plan=final_plan, user_query=user_query) # Use the user_query variable

    # Step 6.1: Register task templates for dynamic task generation
    logging.info("Step 6.1: Registering task templates for dynamic task generation")

    # Register a template for processing ranked items
    tool_executor.register_task_template("process_ranked_items", {
        "type": "for_each",
        "id_prefix": "process",
        "collection": "${result.data.ranked_items}",
        "action": "generate_structured_data",
        "parameters": {
            "input_data": "${item.item}",
            "output_type": "json",
            "output_filename": "item_${index}"
        },
        "name": "Process ranked item ${index}"
    })

    # Register a template for processing selected options
    tool_executor.register_task_template("process_selected_options", {
        "type": "for_each",
        "id_prefix": "option",
        "collection": "${result.data.selected_options}",
        "action": "generate_structured_data",
        "parameters": {
            "input_data": "${item.option}",
            "output_type": "json",
            "output_filename": "option_${index}"
        },
        "name": "Process selected option ${index}"
    })

    # Register a conditional template for high-scoring items
    tool_executor.register_task_template("conditional_high_score", {
        "type": "conditional",
        "id_prefix": "highlight",
        "condition": "${item.score > 80}",
        "then": {
            "action": "generate_final_answers",
            "parameters": {
                "input_data": "${item.item}",
                "question": "Why is this a high-scoring option?",
                "format": "markdown"
            },
            "name": "Highlight high-scoring item: ${item.item.name}"
        }
    })

    logging.info("Step 6.1 complete: Task templates registered")

    # Ask user if they want to execute the tasks
    execute_tasks = input("\nDo you want to execute all tasks using the dependency-based asynchronous execution model? (y/n): ")
    if execute_tasks.lower() in ('y', 'yes'):
        print("\nExecuting tasks based on dependencies...")
        print("Tasks will start as soon as their dependencies are satisfied.")
        print("Results will be saved to individual task files in the 'context/tasks/' directory.")
        print("Dynamic tasks will be generated based on task results.")

        # Execute all tasks
        await tool_executor.execute_plan()

        logging.info("Step 6 complete: All tasks executed and result files generated.")
        print("\nExecution complete. Task result files have been generated in the 'context/tasks/' directory.")
        print("An execution summary has been generated in 'context/execution_summary.md'.")

        # Print information about dynamic tasks
        if tool_executor.dynamic_tasks:
            print(f"\nDynamic Tasks: {len(tool_executor.dynamic_tasks)} tasks were dynamically generated during execution.")

        # Check if final_answer.md exists and mention it
        final_answer_path = os.path.join("context", "final_answer.md")
        if os.path.exists(final_answer_path):
            print("The final answer has been saved to 'context/final_answer.md'.")

        # Provide a summary of what was executed
        print("\nExecution Summary:")
        print(f"Total tasks: {len(tool_executor.task_dependencies)}")
        print(f"Completed tasks: {len(tool_executor.completed_tasks)}")

        # Suggest next steps
        print("\nNext Steps:")
        print("1. Review the execution summary in 'context/execution_summary_YYYYMMDD_HHMMSS.json'")
        print("2. Examine individual task results in the 'context/tasks/' directory")
        print("3. Check for final answer files (e.g., 'final_answer_*.md' or '.json') in the 'context' directory")

        # Check if final_answer.md exists and suggest it as a next step (This might be from an old version, ToolExecutor saves timestamped files)
        # final_answer_path = os.path.join("context", "final_answer.md")
        # if os.path.exists(final_answer_path):
        # print("4. Read the final answer in 'context/final_answer.md'")
        # else:
        # print("4. Use the final answers generated by the final task") # General advice

    else:
        # This logging was in the diff, so keeping it to match expectation if user chose 'n'
        logging.info("Step 6 skipped: User chose not to execute tasks.")
        print("\nTasks not executed.")

if __name__ == "__main__":
    try:
        asyncio.run(main()) # Called without arguments, so load_plan_path will be None by default
    except KeyboardInterrupt:
        print("\nExecution interrupted by user.")
        logging.info("Execution interrupted by user.")
    except Exception as e:
        logging.error(f"Unhandled exception in main execution: {e}", exc_info=True)
        print(f"An critical unhandled error occurred: {e}")