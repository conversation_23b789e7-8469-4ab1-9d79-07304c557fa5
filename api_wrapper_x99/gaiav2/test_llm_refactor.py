#!/usr/bin/env python3
"""
Test script to verify the LLM refactoring.
"""
import asyncio
import logging
from llm import LLM
import llm_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_llm():
    """Test the LLM module."""
    # Initialize LLM with OpenAI provider
    llm = LLM(provider="openai")
    
    # Prepare messages
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant."},
        {"role": "user", "content": "Say hello world in one sentence."}
    ]
    
    # Get model and parameters from config
    model = llm_config.DEFAULT_MODELS["decomposition"]
    params = llm_config.MODEL_PARAMS["decomposition"]
    
    # Generate content
    print(f"Generating content with model: {model}")
    response = await llm.generate(
        messages=messages,
        model=model,
        temperature=params["temperature"],
        max_tokens=params["max_tokens"]
    )
    
    print("\nResponse:")
    print(response)

if __name__ == "__main__":
    asyncio.run(test_llm())
