#!/usr/bin/env python3
"""
Test script for streaming final answers.
"""
import asyncio
import json
from tools.generate_final_answers import generate_final_answers

async def main():
    # Example processed data
    example_data = {
        "flights": [
            {
                "airline": "Delta",
                "departure": "2023-12-01 08:00",
                "arrival": "2023-12-01 12:00",
                "price": "$350"
            },
            {
                "airline": "United",
                "departure": "2023-12-01 10:00",
                "arrival": "2023-12-01 14:00",
                "price": "$320"
            }
        ],
        "hotels": [
            {
                "name": "Grand Hotel",
                "price": "$200/night",
                "rating": 4.5
            },
            {
                "name": "Budget Inn",
                "price": "$120/night",
                "rating": 3.8
            }
        ]
    }

    # Example with original question
    original_question = "What are the best flight and hotel options for my trip in December?"
    
    print("Testing streaming final answers...")
    print("You should see the answer being streamed in real-time, followed by the final JSON result.")
    print("\nOriginal Question:", original_question)
    
    # Call the generate_final_answers function
    # The streaming will happen automatically inside the tool
    result = await generate_final_answers(example_data, original_question)
    
    # Print the final result structure (not the content, which was already streamed)
    print("\nFinal Result Structure:")
    print(f"Status: {result['status']}")
    print(f"Tool: {result['tool_name']}")
    print(f"Request ID: {result['request_id']}")
    print(f"Execution Time: {result['execution_time']:.2f} seconds")
    
    # Print summary and recommendations
    if result["status"] == "success":
        print("\nSummary:", result["data"]["summary"])
        
        if result["data"]["recommendations"]:
            print("\nRecommendations:")
            for i, rec in enumerate(result["data"]["recommendations"], 1):
                print(f"{i}. {rec}")

if __name__ == "__main__":
    asyncio.run(main())
