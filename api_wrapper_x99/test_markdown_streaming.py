#!/usr/bin/env python3
"""
Test script for markdown streaming in GaiaV2.

This script tests the streaming of markdown content from GaiaV2 to ensure
that markdown formatting is preserved correctly.
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the GaiaV2 client
from clients import get_gaiav2_client

# Sample markdown content with various formatting elements
SAMPLE_MARKDOWN = """
# Trip Summary
Plan a 30-day solo trip to Thailand (Oct 1–30, 2025), split between Bangkok and Chiang Mai.
Stay centrally at Amara Bangkok and The Chiang Mai Old Town Hotel, enjoy top-rated local
attractions within walking distance, and keep total accommodation costs under USD 3,000.

## Key Recommendations
1. Book both hotels immediately to secure the best rates (Amara Bangkok – $80/night; Chiang Mai Old Town – $104/night).
2. Reserve timed-entry tickets online for your first-day activity (Grand Palace) to avoid queues.
3. Purchase a local SIM/data plan on arrival for easy ride-hailing and map access.

---

# Itinerary Overview
| Dates | City | Hotel | Nights | Nightly Rate (USD) | Subtotal (USD) |
|------------------|-------------|---------------------------------------|:------:|:------------------:|---------------:|
| Oct 1 – 15 (15 n)| Bangkok | Amara Bangkok | 15 | $80 | $1,200 |
| Oct 16 – 30 (15 n)| Chiang Mai | The Chiang Mai Old Town Hotel | 15 | $104 | $1,560 |
| Total | | | 30| | $2,760 |

---

# 1. Bangkok (Oct 1–15)
## Hotel: Amara Bangkok
• Location: Middle of Bangkok (Silom/Sathorn area)
• Rating: 5.0 ★ (>4.2 requirement)
• Amenities: Breakfast (fee), free Wi-Fi, outdoor pool, fitness center, hot tub, bar, free parking
• Booking link: https://www.google.com/travel/hotels/entity/ChgI9d2gu4_h-NtMGgwvZy8xcTY0Z3lscHAQAQ/prices
• Cost: $80 × 15 nights = **$1,200**
"""

class MockGaiaV2Client:
    """Mock GaiaV2 client for testing markdown streaming."""
    
    async def process_query_stream(self, query: str):
        """
        Stream a mock response with markdown content.
        
        Args:
            query: The query (ignored in this mock)
            
        Yields:
            Chunks of the response
        """
        # Yield some log messages first
        yield "LOG: Starting GaiaV2 search..."
        await asyncio.sleep(0.5)
        yield "LOG: Decomposing query..."
        await asyncio.sleep(0.5)
        yield "LOG: Building search plan..."
        await asyncio.sleep(0.5)
        yield "LOG: Executing plan..."
        await asyncio.sleep(0.5)
        
        # Yield the separator
        yield "--- ANSWER ---"
        
        # Split the markdown into lines
        lines = SAMPLE_MARKDOWN.split('\n')
        
        # Stream the markdown in chunks
        current_chunk = []
        current_chunk_size = 0
        
        for line in lines:
            # Start a new chunk if this is a header or if the current chunk is getting large
            if line.startswith('#') or current_chunk_size > 200:
                if current_chunk:
                    # Send the accumulated chunk
                    yield '\n'.join(current_chunk)
                    # Reset for next chunk
                    current_chunk = []
                    current_chunk_size = 0
                    # Small delay between major sections
                    await asyncio.sleep(0.2)
            
            # Add the current line to the chunk
            current_chunk.append(line)
            current_chunk_size += len(line)
            
            # If we hit a natural break point (blank line, end of paragraph, etc.)
            # and have accumulated enough content, send the chunk
            if (not line.strip() or line.endswith('.') or line.endswith(':')) and current_chunk_size > 50:
                yield '\n'.join(current_chunk)
                current_chunk = []
                current_chunk_size = 0
                # Smaller delay for minor breaks
                await asyncio.sleep(0.1)
        
        # Send any remaining content
        if current_chunk:
            yield '\n'.join(current_chunk)

async def test_streaming():
    """Test the GaiaV2 streaming functionality with markdown content."""
    print("Testing GaiaV2 markdown streaming...")
    
    # Create a mock GaiaV2 client
    mock_client = MockGaiaV2Client()
    
    # Test query
    query = "plan my trip to thailand, find the top 2 cities and find me the best hotel in each city"
    
    print(f"Query: {query}")
    print("=" * 80)
    print("Streaming response:")
    
    # Stream the response and collect it
    full_response = ""
    
    # Function to create a properly formatted SSE response
    def format_sse_response(content: str, is_log: bool = False) -> str:
        # Format differently based on type
        if is_log:
            # Add a trailing newline for readability
            content_str = f"Search: {content}\n"
            
            # Create a response object with a log indicator
            response_obj = {
                "id": "mock-id",
                "object": "chat.completion.chunk",
                "created": 0,
                "model": "mock-model",
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "content": content_str,
                            "role": "assistant"
                        },
                        "finish_reason": None
                    }
                ],
                "type": "log"
            }
        else:
            # For actual content
            response_obj = {
                "id": "mock-id",
                "object": "chat.completion.chunk",
                "created": 0,
                "model": "mock-model",
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": content},
                        "finish_reason": None
                    }
                ],
                "format": "markdown"
            }
        
        # Format as SSE
        return f"data: {json.dumps(response_obj)}\n\n"
    
    # Stream the response
    is_log = True
    async for chunk in mock_client.process_query_stream(query):
        # Check for separator that marks transition to actual content
        if chunk.strip() == "--- ANSWER ---":
            is_log = False
            print("Transitioning from logs to answer content")
            # Skip the separator itself
            continue
        
        # Format logs differently than content
        if is_log and chunk.startswith("LOG:"):
            # For logs, extract the actual log message
            log_content = chunk[4:].strip()  # Remove the "LOG: " prefix
            
            # Format the log message
            sse_response = format_sse_response(log_content, is_log=True)
            print(f"LOG: {log_content}")
        elif not is_log:
            # For actual content
            if chunk.strip():  # Only yield non-empty chunks
                # Format the content
                sse_response = format_sse_response(chunk, is_log=False)
                print(f"CONTENT: {chunk[:30]}...")  # Print first 30 chars
                full_response += chunk + "\n"
    
    print("\n" + "=" * 80)
    print("Streaming complete!")
    
    # Save the full response to a file for inspection
    with open("test_markdown_output.md", "w") as f:
        f.write(full_response)
    
    print(f"Full response saved to test_markdown_output.md")

if __name__ == "__main__":
    asyncio.run(test_streaming())
