# from tkinter.filedialog import Open
from pathlib import Path
import traceback
# Then proceed with other imports and logic:
from gaia.core.gaia import Gaia
import os
import asyncio
import sys
import time
import logging
import nest_asyncio
from asyncio import Semaphore
from openai import AsyncOpenAI
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Import additional modules if needed

# Load external resources

# Apply nest_asyncio to allow nested event loops
nest_asyncio.apply()

# Add rate limiting constants with default values
API_SEMAPHORE = Semaphore(3)  # Limit concurrent API calls
RATE_LIMIT_DELAY = 0.5  # Seconds between API calls


# Global variables for rate limiting
last_api_call_time = 0
api_call_lock = asyncio.Lock()


# Always use ChatGPT4o
MAX_NODES = 1
MAX_DEPTH = 1
RESULT_LIMIT = 1
SOURCE_TYPE = "Web"
QUERY = 'who is john banks?'

decomposition_model = "deepseek/deepseek-r1"
format_model = "deepseek/deepseek-r1"
generation_model = "minimax/minimax-01"

# Setup search limits
source_config = {
    "Web": {"web_search_limit": RESULT_LIMIT},
    "Scholar": {"scholar_search_limit": RESULT_LIMIT},
    "Video": {"video_search_limit": RESULT_LIMIT},
    "Image": {"image_search_limit": RESULT_LIMIT}
}
selected_config = source_config.get(SOURCE_TYPE, {})

# Default async main function
async def main():
    logging.info("Starting non-streaming mode")
    light_rag = Gaia(
        max_total_nodes=MAX_NODES,
        max_depth=MAX_DEPTH,
        decomposition_model=decomposition_model,
        format_model=format_model,
        generation_model=generation_model,
        web_search_limit=selected_config.get("web_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Web" else 0),
        scholar_search_limit=selected_config.get("scholar_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Scholar" else 0),
        video_search_limit=selected_config.get("video_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Video" else 0),
        image_search_limit=selected_config.get("image_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Image" else 0),
        fallback=False,
        use_scope=True

    )
    final_answer, context_files, graphs = await light_rag.process_query(QUERY)
    print(final_answer)

# Streaming version of main
async def main_streaming():
    logging.info(f"Starting streaming mode with model: {generation_model}")
    light_rag = Gaia(
        max_total_nodes=MAX_NODES,
        max_depth=MAX_DEPTH,
        decomposition_model=decomposition_model,
        format_model=format_model,
        generation_model=generation_model,
        web_search_limit=selected_config.get("web_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Web" else 0),
        scholar_search_limit=selected_config.get("scholar_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Scholar" else 0),
        video_search_limit=selected_config.get("video_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Video" else 0),
        image_search_limit=selected_config.get("image_search_limit", RESULT_LIMIT if SOURCE_TYPE == "Image" else 0),
        fallback=True,
        use_scope=True


    )
    
    print(f"Researching: {QUERY}")
    print("=" * 50)
    
    # Use streaming functionality
    full_response = ""
    chunk_count = 0
    start_time = time.time()
    last_update_time = start_time
    
    print("\nGenerating answer (streaming):", flush=True)
    async for chunk in light_rag.process_query_stream(QUERY):
        print(chunk, end="", flush=True)
        full_response += chunk
        chunk_count += 1
        
        # Show progress stats periodically without disrupting output
        # current_time = time.time()
        # if current_time - last_update_time > 5:  # Update every 5 seconds
        #     logging.info(f"Received {chunk_count} chunks, {len(full_response)} chars in {current_time - start_time:.2f}s")
        #     last_update_time = current_time
    
    end_time = time.time()
    print("\n" + "=" * 50)
    logging.info(f"Streaming complete: {chunk_count} chunks, {len(full_response)} chars in {end_time - start_time:.2f}s")
    
    return full_response

if __name__ == "__main__":
    # Use streaming by default
    streaming_enabled = True
    
    # Check if there's a command line argument to toggle streaming
    if len(sys.argv) > 1 and sys.argv[1].lower() == "no-stream":
        streaming_enabled = False
    
    if streaming_enabled:
        logging.info("Using streaming mode")
        asyncio.run(main_streaming())
    else:
        logging.info("Using non-streaming mode")
        asyncio.run(main())

