"""
Test script to verify markdown formatting in streaming responses.
"""

import asyncio
import j<PERSON>
from streaming import _ensure_markdown_formatting, _format_markdown_table

def test_header_formatting():
    """Test header formatting."""
    test_cases = [
        "#Header without space",
        "##Subheader without space",
        "### Header with space",
        "####Nested header"
    ]

    expected_results = [
        "# Header without space",
        "## Subheader without space",
        "### Header with space",
        "#### Nested header"
    ]

    for i, test_case in enumerate(test_cases):
        result = _ensure_markdown_formatting(test_case)
        print(f"Test case {i+1}:")
        print(f"Input:    '{test_case}'")
        print(f"Expected: '{expected_results[i]}'")
        print(f"Result:   '{result}'")
        print(f"Pass:     {result == expected_results[i]}")
        print()

def test_list_formatting():
    """Test list formatting."""
    test_cases = [
        "*Item without space",
        "-Item without space",
        "+ Item with space",
        "1.Item without space",
        "2. Item with space"
    ]

    expected_results = [
        "* Item without space",
        "- Item without space",
        "+ Item with space",
        "1.Item without space",  # This won't be changed as it doesn't match our pattern
        "2. Item with space"
    ]

    for i, test_case in enumerate(test_cases):
        result = _ensure_markdown_formatting(test_case)
        print(f"Test case {i+1}:")
        print(f"Input:    '{test_case}'")
        print(f"Expected: '{expected_results[i]}'")
        print(f"Result:   '{result}'")
        print(f"Pass:     {result == expected_results[i]}")
        print()

def test_table_formatting():
    """Test table formatting."""
    test_case = """
|Column1|Column2|Column3|
|---|---|---|
|Data1|Data2|Data3|
|LongerData1|Data2|Data3|
""".strip()

    result = _ensure_markdown_formatting(test_case)
    print("Table formatting test:")
    print(f"Input:\n{test_case}\n")
    print(f"Result:\n{result}\n")

    # Test table alignment function directly
    table_rows = test_case.split('\n')
    formatted_table = _format_markdown_table(table_rows)
    print("Direct table formatting test:")
    print("Result:")
    for row in formatted_table:
        print(row)
    print()

def test_math_formatting():
    """Test math expression formatting."""
    test_case = """
Here's an inline math expression: $x^2 + y^2 = z^2$

And a block math expression:
$$
\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}
$$

Rate: 103/night→15nights=**1,545**
""".strip()

    result = _ensure_markdown_formatting(test_case)
    print("Math formatting test:")
    print(f"Input:\n{test_case}\n")
    print(f"Result:\n{result}\n")

def test_code_block_formatting():
    """Test code block formatting."""
    test_case = """
```python
def hello_world():
    print("Hello, world!")
```
""".strip()

    result = _ensure_markdown_formatting(test_case)
    print("Code block formatting test:")
    print(f"Input:\n{test_case}\n")
    print(f"Result:\n{result}\n")

def test_complex_document():
    """Test a complex document with multiple elements."""
    test_case = """
#Summary
You'll spend October 1–30, 2025 exploring Thailand's top two destinations—Bangkok and Chiang Mai—staying at highly rated, free-breakfast hotels and enjoying premier nearby attractions.

##Key Recommendations

1. Book The Salil Hotel Riverside in Bangkok (5 ★, $103/night) and visit Asiatique The Riverfront for an evening of riverside dining and shopping.
2. Stay at The Chiang Mai OLD TOWN Hotel (5 ★, $105/night) and experience the Chiang Mai Night Bazaar—a bustling open-air market.
3. Split your 30-night stay equally (15 nights each) to balance city exploration and minimize travel costs.

##Trip Itinerary
1. Bangkok (Oct 1–15)
####Hotel: The Salil Hotel Riverside
Rating: 5 ★
Rate: 103/night→15nights=**1,545**
Amenities: Free breakfast, Wi-Fi, parking; outdoor pool; spa; fitness center; pet-friendly

|Item|Details|
|---|---|
|Why|Best value among 5 ★ options with complimentary breakfast and riverside setting|
|Booking|https://www.google.com/travel/hotels/entity/ChkIjoDu2YitzbsmGg0vZy8xMXJxejliZGQzEAE|

Activity: Asiatique The Riverfront
Type: Riverside night market & entertainment complex
Rating: 4.3 (63,463 reviews)
""".strip()

    result = _ensure_markdown_formatting(test_case)
    print("Complex document formatting test:")
    print(f"Input:\n{test_case}\n")
    print(f"Result:\n{result}\n")

def main():
    """Run all tests."""
    print("=== Testing Markdown Formatting ===\n")
    test_header_formatting()
    test_list_formatting()
    test_table_formatting()
    test_math_formatting()
    test_code_block_formatting()
    test_complex_document()

if __name__ == "__main__":
    main()
