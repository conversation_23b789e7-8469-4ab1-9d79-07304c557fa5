"""
Streaming response handlers for Gaia, GaiaV2, and LLM.
"""

import json
import asyncio
import traceback
from typing import Dict, List, Any, Optional, AsyncGenerator
from gaia.llm.llm import LLM
from config import logger
from clients import get_llm_client, get_gaia_client
from formatters import _clean_log_content, _should_skip_log

def _ensure_markdown_formatting(markdown_text: str) -> str:
    """
    Ensure proper markdown formatting for streaming content.

    Args:
        markdown_text: The markdown text to format

    Returns:
        Properly formatted markdown text
    """
    lines = markdown_text.split('\n')
    formatted_lines = []

    # Track if we're inside a code block or math block
    in_code_block = False
    in_math_block = False
    in_table = False
    table_rows = []

    for i, line in enumerate(lines):
        # Handle code blocks
        if line.strip().startswith('```'):
            in_code_block = not in_code_block
            formatted_lines.append(line)
            continue

        # If we're in a code block, preserve formatting exactly
        if in_code_block:
            formatted_lines.append(line)
            continue

        # Handle math blocks ($ or $$)
        if line.strip().startswith('$$') and not in_math_block:
            in_math_block = True
            formatted_lines.append(line)
            continue
        elif line.strip().endswith('$$') and in_math_block:
            in_math_block = False
            formatted_lines.append(line)
            continue

        # If we're in a math block, preserve formatting exactly
        if in_math_block:
            formatted_lines.append(line)
            continue

        # Handle tables - collect all table rows to ensure proper alignment
        if '|' in line:
            if not in_table:
                in_table = True
                table_rows = [line]
            else:
                table_rows.append(line)

            # If this is the last line or the next line doesn't have a pipe
            if i == len(lines) - 1 or '|' not in lines[i+1]:
                # Process the table to ensure proper alignment
                formatted_table = _format_markdown_table(table_rows)
                formatted_lines.extend(formatted_table)
                in_table = False
                table_rows = []
            continue

        # Ensure headers have proper spacing (# Header instead of #Header)
        if line.strip().startswith('#'):
            # Count the number of # characters
            header_level = 0
            for char in line.strip():
                if char == '#':
                    header_level += 1
                else:
                    break

            # Extract the header text and ensure there's a space after the #s
            header_text = line.strip()[header_level:].strip()
            formatted_line = '#' * header_level + ' ' + header_text
            formatted_lines.append(formatted_line)

        # Handle list items
        elif line.strip().startswith('*') or line.strip().startswith('-') or line.strip().startswith('+'):
            # Ensure there's a space after the list marker
            marker = line.strip()[0]
            list_text = line.strip()[1:].strip()
            formatted_lines.append(f"{marker} {list_text}")

        # Handle numbered lists
        elif line.strip() and line.strip()[0].isdigit() and '. ' in line:
            # Preserve numbered lists
            formatted_lines.append(line)

        # Handle inline math expressions (e.g., $x^2$)
        elif '$' in line and not line.strip().startswith('$') and not line.strip().endswith('$'):
            # Preserve inline math expressions
            formatted_lines.append(line)

        # Handle special rate expressions with arrows and equals signs
        elif ('→' in line or '->' in line) and '=' in line:
            # This is likely a rate calculation, preserve it exactly
            formatted_lines.append(line)

        # Default case: keep the line as is
        else:
            formatted_lines.append(line)

    return '\n'.join(formatted_lines)

def _format_markdown_table(table_rows: list) -> list:
    """
    Format a markdown table to ensure proper alignment.

    Args:
        table_rows: List of table row strings

    Returns:
        List of properly formatted table rows
    """
    if not table_rows:
        return []

    # Split each row into cells
    cells = [row.strip().split('|') for row in table_rows]

    # Remove empty cells at the beginning and end (from leading/trailing |)
    cells = [[cell for cell in row if cell.strip()] for row in cells]

    # Find the maximum width for each column
    col_widths = []
    for row in cells:
        for i, cell in enumerate(row):
            if i >= len(col_widths):
                col_widths.append(len(cell.strip()))
            else:
                col_widths[i] = max(col_widths[i], len(cell.strip()))

    # Format each row with proper spacing
    formatted_rows = []
    for i, row in enumerate(cells):
        formatted_cells = []
        for j, cell in enumerate(row):
            if j < len(col_widths):
                # Center-align header separator row (second row with dashes)
                if i == 1 and all(c == '-' for c in cell.strip()):
                    formatted_cells.append(' ' + '-' * col_widths[j] + ' ')
                else:
                    # Left-align other cells
                    formatted_cells.append(' ' + cell.strip().ljust(col_widths[j]) + ' ')

        formatted_rows.append('|' + '|'.join(formatted_cells) + '|')

    return formatted_rows

# Import GaiaV2 client
try:
    from clients import get_gaiav2_client
except ImportError as e:
    logger.error(f"Error importing GaiaV2 client: {e}")

# Import rag functionality for internal sources
try:
    from rag import get_context, get_final_prompt
except ImportError:
    # Try alternative import paths
    try:
        from .rag import get_context, get_final_prompt
    except ImportError:
        import sys
        import os
        # Add the parent directory to sys.path
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from different_package.rag import get_context, get_final_prompt

async def stream_gaia_response(
    llm: LLM,
    messages: List[Dict[str, Any]],
    model: str,
    temperature: float,
    max_tokens: int,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: Optional[Dict[str, Any]] = None
) -> AsyncGenerator[str, None]:
    """
    Stream the LLM response in the required format for chat-ui.

    Args:
        llm: The LLM client
        messages: The messages to send to the LLM
        model: The model to use
        temperature: The temperature to use
        max_tokens: The maximum number of tokens to generate
        tools: Optional tools to use with the LLM
        tool_choice: Optional tool choice configuration

    Yields:
        str: Server-Sent Events (SSE) formatted response chunks
    """
    try:
        # Prepare kwargs for stream_generate
        generate_kwargs = {
            "messages": messages,
            "model": model,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        # Add tools parameters if they exist
        if tools:
            generate_kwargs["tools"] = tools
        if tool_choice:
            generate_kwargs["tool_choice"] = tool_choice

        # Use LLM's stream_generate method
        token_id = 0
        current_loop = asyncio.get_running_loop()

        # Stream chunks from LLM
        async for chunk in llm.stream_generate(**generate_kwargs):
            # Create a response object that mimics OpenAI's streaming format
            response_obj = {
                "id": f"chatcmpl-{token_id}",
                "object": "chat.completion.chunk",
                "created": int(current_loop.time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": chunk},
                        "finish_reason": None
                    }
                ]
            }

            # Increment token ID for the next chunk
            token_id += 1

            yield f"data: {json.dumps(response_obj)}\n\n"

        # Send final chunk with finish_reason
        final_obj = {
            "id": f"chatcmpl-{token_id}",
            "object": "chat.completion.chunk",
            "created": int(current_loop.time()),
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }

        yield f"data: {json.dumps(final_obj)}\n\n"
        # Send end of stream
        yield "data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"Error in stream: {str(e)}")
        logger.error(traceback.format_exc())
        error_data = {
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_data)}\n\n"
        yield "data: [DONE]\n\n"

async def stream_gaia_search_response(
    query: str,
    model: str,
    use_deep_snipe: bool = False,
    source_type: str = "Web"
) -> AsyncGenerator[str, None]:
    """
    Stream the Gaia or GaiaV2 search response in the required format for chat-ui.

    Args:
        query: The search query
        model: The model name to include in the response
        use_deep_snipe: Whether to use deep snipe (Gaia Fallback)
        source_type: The type of search to perform (Web, Scholar, Video, Image, Internal, Auto)

    Yields:
        str: Server-Sent Events (SSE) formatted response chunks
    """
    try:
        # Special handling for Internal source type
        if source_type.lower() == "internal":
            async for chunk in _stream_internal_rag_response(query, model):
                yield chunk
            return

        # Special handling for Auto source type (GaiaV2)
        if source_type.lower() == "auto":
            async for chunk in _stream_gaiav2_response(query, model):
                yield chunk
            return

        # For other source types, use the existing Gaia functionality
        async for chunk in _stream_external_gaia_response(query, model, use_deep_snipe, source_type):
            yield chunk

    except Exception as e:
        logger.error(f"Error in search stream: {str(e)}")
        logger.error(traceback.format_exc())
        error_data = {
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_data)}\n\n"
        yield "data: [DONE]\n\n"

async def _stream_internal_rag_response(query: str, model: str) -> AsyncGenerator[str, None]:
    """
    Stream the internal RAG response in the required format for chat-ui.

    Args:
        query: The search query
        model: The model name to include in the response

    Yields:
        str: Server-Sent Events (SSE) formatted response chunks
    """
    logger.info(f"Streaming Internal RAG response for query: {query}")

    # Get context using the RAG system
    context = get_context(query)

    # Generate the final prompt with the context
    final_prompt = get_final_prompt(query, context)
    logger.info(f"Generated RAG prompt with {len(context)} characters of context")

    # Create LLM client for internal queries
    llm = await get_llm_client()

    # Create a token counter for response IDs
    token_id = 0
    current_loop = asyncio.get_running_loop()

    # Stream from the LLM using the RAG prompt
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant with access to internal information."},
        {"role": "user", "content": final_prompt}
    ]

    # Function to create a properly formatted SSE response
    def format_sse_response(content: str, is_log: bool = False) -> str:
        nonlocal token_id

        # Format differently based on type
        if is_log:
            # For log messages
            content_str = f"Search: {content}\n"

            # Create a response object with a log indicator
            response_obj = {
                "id": f"chatcmpl-{token_id}",
                "object": "chat.completion.chunk",
                "created": int(current_loop.time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "content": content_str,
                            "role": "assistant"
                        },
                        "finish_reason": None
                    }
                ],
                # Add a custom type field to indicate this is a log message
                "type": "log"
            }
        else:
            # For actual content - no type field
            response_obj = {
                "id": f"chatcmpl-{token_id}",
                "object": "chat.completion.chunk",
                "created": int(current_loop.time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": content},
                        "finish_reason": None
                    }
                ]
            }

        # Increment ID for next chunk
        token_id += 1

        # Format as SSE
        return f"data: {json.dumps(response_obj)}\n\n"

    # Format logs to show the RAG is being used
    yield format_sse_response("Using internal RAG system to retrieve information...", is_log=True)

    # Show a message about context retrieval
    yield format_sse_response("Retrieved internal context with relevant information", is_log=True)

    # Stream the response
    async for chunk in llm.stream_generate(
        messages=messages,
        model="gpt-4o",
        temperature=0.7
    ):
        yield format_sse_response(chunk, is_log=False)

    # Send final chunk with finish_reason
    final_obj = {
        "id": f"chatcmpl-{token_id}",
        "object": "chat.completion.chunk",
        "created": int(current_loop.time()),
        "model": model,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }

    yield f"data: {json.dumps(final_obj)}\n\n"
    # Send end of stream
    yield "data: [DONE]\n\n"

    logger.info(f"Internal RAG streaming complete. Sent {token_id} chunks.")

async def _stream_gaiav2_response(query: str, model: str) -> AsyncGenerator[str, None]:
    """
    Stream the GaiaV2 search response in the required format for chat-ui.

    Args:
        query: The search query
        model: The model name to include in the response

    Yields:
        str: Server-Sent Events (SSE) formatted response chunks
    """
    logger.info(f"Streaming GaiaV2 search for query: {query}")

    try:
        # Get the GaiaV2 client
        gaiav2_client = await get_gaiav2_client(search_type="Web")

        # Create a token counter for response IDs
        token_id = 0
        current_loop = asyncio.get_running_loop()

        # Function to create a properly formatted SSE response
        def format_sse_response(content: str, is_log: bool = False) -> Optional[str]:
            nonlocal token_id

            # Format differently based on type
            if is_log:
                # Add a trailing newline for readability
                content_str = f"Search: {content}\n"

                # Create a response object with a log indicator
                response_obj = {
                    "id": f"chatcmpl-{token_id}",
                    "object": "chat.completion.chunk",
                    "created": int(current_loop.time()),
                    "model": model,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {
                                "content": content_str,
                                "role": "assistant"
                            },
                            "finish_reason": None
                        }
                    ],
                    # Add a custom type field to indicate this is a log message
                    "type": "log"
                }
            else:
                # For actual content - no type field
                # Ensure content is properly escaped for JSON
                # This is especially important for markdown content with special characters
                response_obj = {
                    "id": f"chatcmpl-{token_id}",
                    "object": "chat.completion.chunk",
                    "created": int(current_loop.time()),
                    "model": model,
                    "choices": [
                        {
                            "index": 0,
                            "delta": {"content": content},
                            "finish_reason": None
                        }
                    ],
                    # Add a format hint for the frontend with explicit markdown type
                    "format": "markdown",
                    "content_type": "markdown"
                }

            # Increment ID for next chunk
            token_id += 1

            # Format as SSE, ensuring proper JSON serialization
            return f"data: {json.dumps(response_obj)}\n\n"

        # Stream in real-time from GaiaV2
        is_log = True

        # Yield initial log message
        yield format_sse_response("Starting GaiaV2 search...", is_log=True)

        # Stream chunks from GaiaV2
        async for chunk in gaiav2_client.process_query_stream(query):
            # Check for separator that marks transition to actual content
            if chunk.strip() == "--- ANSWER ---":
                is_log = False
                logger.info("Transitioning from logs to answer content")
                # Skip the separator itself
                continue

            # Format logs differently than content
            if is_log and chunk.startswith("LOG:"):
                # For logs, extract the actual log message
                log_content = chunk[4:].strip()  # Remove the "LOG: " prefix

                # Format the log message
                yield format_sse_response(log_content, is_log=True)

                # Add a small delay for logs to simulate real-time typing
                await asyncio.sleep(0.05)
            elif not is_log:
                # For actual content
                if chunk.strip():  # Only yield non-empty chunks
                    # Preserve markdown formatting by not breaking up the content
                    # This ensures tables, code blocks, and other markdown structures remain intact
                    formatted_chunk = chunk

                    # Process the markdown content to ensure proper formatting
                    # This is especially important for headers, tables, and lists
                    formatted_chunk = _ensure_markdown_formatting(formatted_chunk)

                    # Don't manually replace newlines - let json.dumps handle proper escaping
                    # This ensures proper JSON serialization while preserving markdown structure

                    yield format_sse_response(formatted_chunk, is_log=False)

        # Send final chunk with finish_reason
        final_obj = {
            "id": f"chatcmpl-{token_id}",
            "object": "chat.completion.chunk",
            "created": int(current_loop.time()),
            "model": model,
            "choices": [
                {
                    "index": 0,
                    "delta": {},
                    "finish_reason": "stop"
                }
            ]
        }

        yield f"data: {json.dumps(final_obj)}\n\n"
        # Send end of stream
        yield "data: [DONE]\n\n"

        logger.info(f"GaiaV2 search streaming complete. Sent {token_id} chunks.")
    except ImportError as e:
        logger.error(f"Error importing GaiaV2 client: {e}")
        logger.warning("Falling back to regular Gaia search")
        # Fall back to regular Gaia search
        async for chunk in _stream_external_gaia_response(query, model, False, "Web"):
            yield chunk
    except Exception as e:
        logger.error(f"Error in GaiaV2 search stream: {str(e)}")
        logger.error(traceback.format_exc())
        error_data = {
            "error": {
                "message": str(e),
                "type": "server_error"
            }
        }
        yield f"data: {json.dumps(error_data)}\n\n"
        yield "data: [DONE]\n\n"

async def _stream_external_gaia_response(
    query: str,
    model: str,
    use_deep_snipe: bool,
    source_type: str
) -> AsyncGenerator[str, None]:
    """
    Stream the external Gaia search response in the required format for chat-ui.

    Args:
        query: The search query
        model: The model name to include in the response
        use_deep_snipe: Whether to use deep snipe (Gaia Fallback)
        source_type: The type of search to perform (Web, Scholar, Video, Image)

    Yields:
        str: Server-Sent Events (SSE) formatted response chunks
    """
    if use_deep_snipe:
        logger.info(f"Streaming Gaia Fallback (deep snipe) search for query: {query} with source type: {source_type}")
    else:
        logger.info(f"Streaming Gaia search for query: {query} with source type: {source_type}")

    # Get the Gaia client with appropriate configuration
    gaia_client = await get_gaia_client(search_type=source_type, use_deep_snipe=use_deep_snipe)

    # Create a token counter for response IDs
    token_id = 0
    current_loop = asyncio.get_running_loop()

    # Function to create a properly formatted SSE response
    def format_sse_response(content: str, is_log: bool = False) -> Optional[str]:
        nonlocal token_id

        # Format differently based on type
        if is_log:
            # Extract just the meaningful part of the log message
            cleaned_content = _clean_log_content(content)

            # If the log should be skipped, return None
            if cleaned_content is None:
                return None

            # Add a trailing newline for readability
            content_str = f"Search: {cleaned_content}\n"

            # Create a response object with a log indicator
            response_obj = {
                "id": f"chatcmpl-{token_id}",
                "object": "chat.completion.chunk",
                "created": int(current_loop.time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {
                            "content": content_str,
                            "role": "assistant"
                        },
                        "finish_reason": None
                    }
                ],
                # Add a custom type field to indicate this is a log message
                "type": "log"
            }
        else:
            # For actual content - no type field
            response_obj = {
                "id": f"chatcmpl-{token_id}",
                "object": "chat.completion.chunk",
                "created": int(current_loop.time()),
                "model": model,
                "choices": [
                    {
                        "index": 0,
                        "delta": {"content": content},
                        "finish_reason": None
                    }
                ]
            }

        # Increment ID for next chunk
        token_id += 1

        # Format as SSE
        return f"data: {json.dumps(response_obj)}\n\n"

    # Stream chunks from process_query_stream
    is_log = True
    delay_counter = 0

    # Stream in real-time from Gaia
    async for chunk in gaia_client.process_query_stream(query):
        # Check for separator that marks transition to actual content
        if chunk.strip() == "--- ANSWER ---":
            is_log = False
            logger.info("Transitioning from logs to answer content")
            # Skip the separator itself
            continue

        # Format logs differently than content
        if is_log and chunk.startswith("LOG:"):
            # For logs, extract the actual log message
            log_content = chunk[4:].strip()  # Remove the "LOG: " prefix

            # Skip technical logs that aren't relevant to the user experience
            if _should_skip_log(log_content):
                continue

            # Format the log message (might be None if log should be skipped)
            formatted_response = format_sse_response(log_content, is_log=True)

            # Only yield if the log isn't being filtered out
            if formatted_response:
                yield formatted_response

            # Add a small delay for logs to simulate real-time typing
            # But do this only every few chunks to avoid too much delay
            delay_counter += 1
            if delay_counter % 3 == 0:
                await asyncio.sleep(0.05)
        elif not is_log:
            # For actual content - this is coming directly from the LLM now
            if chunk.strip():  # Only yield non-empty chunks
                # Ensure proper formatting by adding newlines where appropriate
                formatted_chunk = chunk
                yield format_sse_response(formatted_chunk, is_log=False)

    # Send final chunk with finish_reason
    final_obj = {
        "id": f"chatcmpl-{token_id}",
        "object": "chat.completion.chunk",
        "created": int(current_loop.time()),
        "model": model,
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }

    yield f"data: {json.dumps(final_obj)}\n\n"
    # Send end of stream
    yield "data: [DONE]\n\n"

    logger.info(f"Gaia search streaming complete. Sent {token_id} chunks.")