"""
Search functionality for Gaia, GaiaV2, and RAG systems.
"""

from typing import Optional
from config import logger
from clients import get_llm_client, get_gaia_client, get_search_client

# Import rag functionality for internal sources
try:
    from rag import get_context, get_final_prompt
except ImportError:
    # Try alternative import paths
    try:
        from .rag import get_context, get_final_prompt
    except ImportError:
        import sys
        import os
        # Add the parent directory to sys.path
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from different_package.rag import get_context, get_final_prompt

async def perform_gaia_search(query: str, use_deep_snipe: bool = False, source_type: str = "Web") -> str:
    """
    Perform a search using either the RAG system (for Internal source type), Gaia, or GaiaV2.

    Args:
        query: The search query
        use_deep_snipe: Whether to use deep snipe (Gaia Fallback) for more extensive search
        source_type: The type of search to perform (Web, Scholar, Video, Image, Internal, Auto)

    Returns:
        str: The search result text
    """
    # Handle Internal source type with RAG system
    if source_type.lower() == "internal":
        return await _perform_internal_rag_search(query)

    # Handle Auto source type with GaiaV2
    if source_type.lower() == "auto":
        return await _perform_gaiav2_search(query)

    # Regular Gaia search for other source types
    return await _perform_external_gaia_search(query, use_deep_snipe, source_type)

async def _perform_internal_rag_search(query: str) -> str:
    """
    Perform a search using the internal RAG system.

    Args:
        query: The search query

    Returns:
        str: The RAG search result
    """
    logger.info(f"Using Internal RAG system for query: {query}")

    # Get context using the RAG system
    context = get_context(query)

    # Generate the final prompt with the context
    final_prompt = get_final_prompt(query, context)
    logger.info(f"Generated RAG prompt with {len(context)} characters of context")

    # Create LLM client for internal queries
    llm = await get_llm_client()

    # Use the prompt to generate a response with the LLM
    messages = [
        {"role": "system", "content": "You are a helpful AI assistant with access to internal information."},
        {"role": "user", "content": final_prompt}
    ]

    response = await llm.generate(
        messages=messages,
        model="gpt-4.1",
        temperature=0
    )

    logger.info(f"RAG search complete. Answer length: {len(response)}")
    return response

async def _perform_gaiav2_search(query: str) -> str:
    """
    Perform a search using GaiaV2.

    Args:
        query: The search query

    Returns:
        str: The GaiaV2 search result
    """
    logger.info(f"Performing GaiaV2 search for query: {query}")

    # Get the GaiaV2 client
    try:
        from clients import get_gaiav2_client
        gaiav2_client = await get_gaiav2_client(search_type="Web")

        # Process the query with GaiaV2
        final_answer, _, _ = await gaiav2_client.process_query(query)

        logger.info(f"GaiaV2 search complete. Answer length: {len(final_answer)}")
        return final_answer
    except ImportError as e:
        logger.error(f"Error importing GaiaV2 client: {e}")
        logger.warning("Falling back to regular Gaia search")
        return await _perform_external_gaia_search(query, False, "Web")

async def _perform_external_gaia_search(query: str, use_deep_snipe: bool, source_type: str) -> str:
    """
    Perform a search using Gaia.

    Args:
        query: The search query
        use_deep_snipe: Whether to use deep snipe (Gaia Fallback)
        source_type: The type of search to perform (Web, Scholar, Video, Image)

    Returns:
        str: The Gaia search result
    """
    if use_deep_snipe:
        logger.info(f"Performing Gaia Fallback (deep snipe) search for query: {query} with source type: {source_type}")
    else:
        logger.info(f"Performing Gaia search for query: {query} with source type: {source_type}")

    # Get the appropriate Gaia client
    gaia_client = await get_gaia_client(search_type=source_type, use_deep_snipe=use_deep_snipe)

    # Process the query with Gaia
    final_answer, _, _ = await gaia_client.process_query(query)

    logger.info(f"Gaia search complete. Answer length: {len(final_answer)}")
    return final_answer
