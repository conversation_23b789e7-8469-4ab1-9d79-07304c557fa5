"""
Configuration settings and constants for the API wrapper.
"""

import os
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.info("Loaded environment variables from .env file")

# Print loaded API keys (masked for security)
openai_key = os.getenv("OPENAI_API_KEY", "")
if openai_key:
    logger.info(f"OPENAI_API_KEY loaded: {openai_key[:4]}...{openai_key[-4:] if len(openai_key) > 8 else ''}")
else:
    logger.warning("OPENAI_API_KEY not found in environment variables")

# Constants for regular Gaia search
MAX_NODES = 5
MAX_DEPTH = 3
RESULT_LIMIT = 3

# Constants for Gaia Fallback (Deep Snipe)
FALLBACK_MAX_NODES = 15
FALLBACK_MAX_DEPTH = 4
FALLBACK_RESULT_LIMIT = 5

# Source types
VALID_SOURCE_TYPES = ["Web", "Scholar", "Video", "Image", "Internal", "Auto"]
DEFAULT_SEARCH_TYPE = "Web"
