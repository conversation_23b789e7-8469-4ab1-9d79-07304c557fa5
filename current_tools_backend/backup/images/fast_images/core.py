import os
import logging
from typing import Dict, Any, List, Optional, Union
from .image_searcher import ImageSearcher

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Global searcher instance
_searcher = None

def get_searcher(api_key=None, lang="en", limit=10) -> ImageSearcher:
    """
    Get or create an ImageSearcher instance
    
    Args:
        api_key: SerpAPI key (optional, will use SERPAPI_KEY env var if not provided)
        lang: Language for search results
        limit: Maximum number of results to return
        
    Returns:
        ImageSearcher instance
    """
    global _searcher
    
    if _searcher is None:
        _searcher = ImageSearcher(
            api_key=api_key or os.getenv('SERPAPI_KEY'),
            lang=lang,
            limit=limit
        )
    
    return _searcher

async def search_images(
    query: str,
    api_key: Optional[str] = None,
    lang: str = "en",
    limit: int = 10,
    output_format: str = "dict"
) -> Union[Dict[str, Any], List[Dict[str, Any]]]:
    """
    Search for images using SerpAPI.
    
    Args:
        query: Search query
        api_key: SerpAPI key (optional, will use SERPAPI_KEY env var if not provided)
        lang: Language for search results
        limit: Maximum number of results to return
        output_format: Output format ('dict' or 'list')
        
    Returns:
        Dictionary or list of image results
    """
    searcher = get_searcher(api_key, lang, limit)
    
    try:
        results = await searcher.search(query, limit)
        
        # Format results based on output_format
        if output_format.lower() == "dict":
            return {
                "query": query,
                "images": results.get("images", []),
                "shopping": results.get("shopping", []),
                "related_searches": results.get("related_searches", [])
            }
        else:
            # Return raw data
            return results.get("images", [])
    
    except Exception as e:
        logger.error(f"Error searching images: {str(e)}")
        if output_format.lower() == "dict":
            return {
                "query": query, 
                "images": [], 
                "shopping": [], 
                "related_searches": [],
                "error": str(e)
            }
        else:
            return []
