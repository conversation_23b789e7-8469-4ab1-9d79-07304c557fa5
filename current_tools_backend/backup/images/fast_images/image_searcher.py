import os
import json
import logging
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImageSearcher:
    """
    Class for searching images using SerpAPI.
    """
    def __init__(self, api_key=None, lang="en", limit=10):
        """
        Initialize the ImageSearcher.
        
        Args:
            api_key: SerpAPI key (optional, will use SERPAPI_KEY env var if not provided)
            lang: Language for search results
            limit: Maximum number of results to return
        """
        self.api_key = api_key or os.getenv("SERPAPI_KEY")
        self.lang = lang
        self.limit = limit
        self.base_url = "https://serpapi.com/search"
        
    async def search(self, query: str, limit: Optional[int] = None) -> Dict[str, Any]:
        """
        Performs an image search using SerpAPI with the specified query.
        
        Args:
            query: Search query
            limit: Maximum number of results to return (overrides instance limit)
            
        Returns:
            Dictionary with search results
        """
        # Use provided limit or default
        search_limit = limit or self.limit
        
        params = {
            "engine": "google_images",
            "q": query,
            "api_key": self.api_key,
            "hl": self.lang,
            "num": search_limit,
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.base_url, params=params, timeout=30) as response:
                    if response.status != 200:
                        logger.error(f"Error from SerpAPI: {response.status}")
                        return {
                            "images": [],
                            "shopping": [],
                            "related_searches": []
                        }
                    
                    results = await response.json()
                    
                    # Extract and process different result types
                    images_results = self.preprocess_images(results)
                    shopping_results = self.preprocess_shopping(results)
                    related_searches = self.preprocess_related_searches(results)
                    
                    return {
                        "images": images_results,
                        "shopping": shopping_results,
                        "related_searches": related_searches
                    }
                    
        except Exception as e:
            logger.error(f"Error during image search: {str(e)}")
            return {
                "images": [],
                "shopping": [],
                "related_searches": []
            }
    
    def preprocess_images(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process image search results with useful information.
        
        Args:
            results: Raw search results from SerpAPI
            
        Returns:
            List of processed image results
        """
        processed_images = []
        for result in results.get('images_results', []):
            # Extract most useful information
            image_info = {
                'title': result.get('title', ''),
                'source': result.get('source', ''),
                'source_link': result.get('link', ''),
                'original_image': result.get('original', ''),
                'thumbnail': result.get('thumbnail', ''),
                'dimensions': {
                    'width': result.get('original_width'),
                    'height': result.get('original_height')
                },
                'is_product': result.get('is_product', False),
                'related_content_link': result.get('serpapi_related_content_link', '')
            }
            processed_images.append(image_info)
        return processed_images
    
    def preprocess_shopping(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process shopping results with useful product information.
        
        Args:
            results: Raw search results from SerpAPI
            
        Returns:
            List of processed shopping results
        """
        processed_shopping = []
        for result in results.get('shopping_results', []):
            # Extract most useful shopping information
            shopping_info = {
                'title': result.get('title', ''),
                'price': result.get('price', ''),
                'extracted_price': result.get('extracted_price'),
                'source': result.get('source', ''),
                'link': result.get('link', ''),
                'thumbnail': result.get('thumbnail', '')
            }
            processed_shopping.append(shopping_info)
        return processed_shopping
    
    def preprocess_related_searches(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Process related searches with useful information.
        
        Args:
            results: Raw search results from SerpAPI
            
        Returns:
            List of processed related search results
        """
        processed_related = []
        for result in results.get('suggested_searches', []):
            # Extract most useful related search information
            related_info = {
                'name': result.get('name', ''),
                'query': result.get('query', result.get('name', '')),
                'link': result.get('link', ''),
                'thumbnail': result.get('thumbnail', ''),
                'highlighted_words': result.get('highlighted_words', [])
            }
            processed_related.append(related_info)
        return processed_related
