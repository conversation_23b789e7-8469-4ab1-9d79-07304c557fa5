import os
import logging
from typing import Op<PERSON>, Dict, Any, List
from fastapi import <PERSON><PERSON><PERSON>, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from fast_images import search_images

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Image Search API",
    description="API for searching images using SerpAPI",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {
        "message": "Image Search API is running",
        "docs": "/docs",
        "endpoints": [
            "/images/search",
        ],
        "payload_formats": {
            "/images/search": {
                "method": "GET",
                "query_parameters": {
                    "query": "string (required) - Search query",
                    "limit": "integer (optional) - Maximum number of results to return (default: 10, max: 100)",
                    "lang": "string (optional) - Language for search results (default: 'en')",
                    "api_key": "string (optional) - SerpAPI key (can also be set as SERPAPI_KEY environment variable)",
                    "debug": "boolean (optional) - Enable debug mode (default: false)"
                },
                "example": "/images/search?query=sunset+beach&limit=20&lang=en"
            }
        }
    }

@app.get("/images/search")
async def search(
    query: str = Query(..., description="Search query"),
    limit: int = Query(10, description="Maximum number of results to return", ge=1, le=100),
    lang: str = Query("en", description="Language for search results"),
    api_key: Optional[str] = Query(None, description="SerpAPI key (optional)"),
    debug: bool = Query(False, description="Enable debug mode")
):
    try:
        # Use provided API key or get from environment
        serpapi_key = api_key or os.getenv("SERPAPI_KEY")

        if not serpapi_key:
            return JSONResponse(
                status_code=400,
                content={"error": "SerpAPI key is required. Provide it as a query parameter or set SERPAPI_KEY environment variable."}
            )

        # Log the request if debug is enabled
        if debug:
            logger.info(f"Searching for images with query: {query}, limit: {limit}, lang: {lang}")

        # Search for images
        result = await search_images(
            query=query,
            api_key=serpapi_key,
            lang=lang,
            limit=limit,
            output_format="dict"
        )

        return JSONResponse(content=result)

    except Exception as e:
        logger.error(f"Error searching images: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Run with: uvicorn api:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
