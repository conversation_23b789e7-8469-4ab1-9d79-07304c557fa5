# Image Search API

A microservice for searching images using SerpAPI.

## Features

- Search for images with a query
- Get detailed information about images (dimensions, source, etc.)
- Get related shopping results
- Get related search suggestions
- RESTful API with FastAPI

## API Endpoints

- `GET /` - API health check and information
- `GET /images/search` - Search for images

## Environment Variables

- `SERPAPI_KEY` - SerpAPI key (required)

## Running Locally

```bash
# Install dependencies
pip install -r requirements.txt

# Run the server
uvicorn api:app --reload
```

## Running with Docker

```bash
# Build the Docker image
docker build -t images-service .

# Run the container
docker run -d -p 8005:8005 -e SERPAPI_KEY=your_api_key images-service
```

## API Documentation

Once the server is running, you can access the API documentation at:

- Swagger UI: http://localhost:8005/docs
- ReDoc: http://localhost:8005/redoc
