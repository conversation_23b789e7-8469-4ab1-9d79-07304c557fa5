import requests, pprint, os, json, time

BASE_URL = os.getenv("API_URL", "http://localhost:8000")

params = {
    "query": "organic milk",
    "country": "UK",
    "limit": 5,
    "zyte_api_key": os.getenv("ZYTE_API_KEY") or "dummy_key_here",
}

print(f"Requesting {BASE_URL}/search with params {params}")
resp = requests.get(f"{BASE_URL}/search", params=params, timeout=60)
print("Status:", resp.status_code)

data = resp.json()
if not data.get("success"):
    print("API reported failure:")
    pprint.pprint(data)
    raise SystemExit(1)

expected = params["limit"]
actual = data.get("total_found")
print(f"✔️  Success flag OK. Received {actual}/{expected} detailed products.")

for i, prod in enumerate(data.get("products", []), 1):
    print(f"{i}. {prod.get('brand_name')} – {prod.get('name')} ({prod.get('price')})")

print(f"Listing contains {len(data.get('listing_products', []))} basic products.")

# Save full response
os.makedirs("results", exist_ok=True)
outfile = f"results/api_test_{params['query'].replace(' ', '_')}_{int(time.time())}.json"
with open(outfile, "w", encoding="utf-8") as f:
    json.dump(data, f, ensure_ascii=False, indent=2)
print("Full response written to", outfile) 