# Python-specific ignores
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
.env/
.venv/
env/

# Distribution / packaging
dist/
build/
*.egg-info/

# IDE-specific files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Jupyter Notebook
.ipynb_checkpoints

# Logs and databases
*.log
*.sqlite3

# OS-specific files
.DS_Store
Thumbs.db

# Sensitive information
.env
*.env
.secrets

# Pytest
.pytest_cache/

# Coverage reports
.coverage
htmlcov/

# Mypy cache
.mypy_cache/

# Temporary files
*.swp
*~
.*.kate-swp
.swp.* 
test.py
*.html

# Python-specific ignores
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.env/
.venv/

# Log files
*.log
lidl_crawler.log
lidl_product_extractor.log

# Data and output directories
lidl_extraction_*/
*.json
*.csv
*.xlsx

# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Operating System files
.DS_Store
Thumbs.db

# Sensitive or local configuration
.env
config.ini

# Temporary files
*.tmp
*.bak
*~

# Jupyter Notebook
.ipynb_checkpoints/

# Compiled Python files
*.pyc

# Distribution / packaging
dist/
build/
*.egg-info/

# User-specific files
user_messages_buffer.md 
ask_user.py