FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirement files and install
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy project source
COPY . .

# Expose port
EXPOSE 8000

ENV WEB_CONCURRENCY=4

CMD gunicorn api:app -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --workers $WEB_CONCURRENCY 