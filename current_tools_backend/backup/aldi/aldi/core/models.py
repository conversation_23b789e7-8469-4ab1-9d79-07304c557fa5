from __future__ import annotations

"""Datamodels for the Aldi scraper (trimmed).

We only expose the fields the scraper currently populates. If you extend
`AsyncAldiScraper` to capture more attributes you can safely append them here –
callers should not rely on strict schemas.
"""

from dataclasses import dataclass, field
from typing import Dict, Any


@dataclass
class Product:
    name: str
    price: str
    currency: str
    currency_symbol: str
    url: str
    main_image_url: str = ""
    availability: str = ""
    brand_name: str = ""
    sku: str = ""
    description: str = ""

    # Keep raw product JSON for full fidelity
    raw_product_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchQuery:
    query: str
    country: str = "UK"
    limit: int = 5

    def to_url_fragment(self) -> str:
        return self.query.replace(" ", "+") 