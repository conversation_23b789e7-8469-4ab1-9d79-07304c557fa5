"""Application controller for the Aldi scraper.

Mirrors the structure of `core/application_controller.py` in the Zalando project.
It coordinates console UI ↔ async scraper.
"""

from __future__ import annotations

import asyncio
import logging
from typing import Dict, Any

from utils.console_ui import ConsoleUI
from scrapers.async_scraper_service import AsyncAldiScraper

logger = logging.getLogger(__name__)


class ApplicationController:
    def __init__(self):
        self.ui = ConsoleUI()
        self.scraper = AsyncAldiScraper()

    async def run_async_scraper(self, query: str, country: str, limit: int) -> Dict[str, Any]:
        try:
            self.ui.print_header("🚀 ASYNC ALDI SCRAPER")
            params = {"query": query, "country": country, "limit": limit}
            self.ui.display_parameters_summary(params)
            result = await self.scraper.scrape_products(query=query, country=country, limit=limit)
            self.ui.display_scraping_results(result)
            return result
        except Exception as e:
            logger.error("Async scraper error: %s", e, exc_info=True)
            return {"success": False, "error": str(e)}

    def run_main_menu(self):
        while True:
            self.ui.print_main_menu()
            try:
                choice = input("Choose an option (1-2): ").strip()
            except KeyboardInterrupt:
                self.ui.print_goodbye()
                break

            if choice == "1":
                params = self.ui.get_scraping_parameters()
                if params:
                    asyncio.run(
                        self.run_async_scraper(
                            params["query"], params["country"], params["limit"]
                        )
                    )
                self.ui.wait_for_user()
            elif choice == "2":
                self.ui.print_goodbye()
                break
            else:
                self.ui.display_warning("Invalid choice. Please select 1 or 2.") 