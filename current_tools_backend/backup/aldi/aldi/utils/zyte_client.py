from __future__ import annotations

"""Lightweight async Zyte Extract API client.

Only supports the minimal subset we need for the Aldi scraper:
• fetch_html – return rendered browserHtml
• fetch_product_list – return J<PERSON><PERSON> with `productList`
• fetch_product – return <PERSON><PERSON><PERSON> with `product`
"""

import asyncio
import os
from typing import Optional, Any, Dict

import httpx
from dotenv import load_dotenv

_ZYTE_ENDPOINT = "https://api.zyte.com/v1/extract"


class ZyteClient:
    """Tiny async client for the Zyte Extract API (browserHtml / productList / product)."""

    def __init__(self, api_key: str, timeout: float = 60.0):
        self._api_key = api_key
        self._timeout = timeout

    # ------------------------------------------------------------------
    # Convenience wrappers
    # ------------------------------------------------------------------
    async def fetch_html(self, url: str, *, retries: int = 3) -> Optional[str]:
        """Return the rendered `browserHtml` for *url* or ``None`` on failure."""
        payload = {"url": url, "browserHtml": True}
        data = await self._post(payload, retries)
        return data.get("browserHtml") if data else None

    async def fetch_product_list(self, url: str, *, retries: int = 3) -> Optional[Dict[str, Any]]:
        """Return Zyte `productList` structure or ``None`` on failure."""
        payload = {
            "url": url,
            "productList": True,
            "productListOptions": {"extractFrom": "browserHtml"},
        }
        data = await self._post(payload, retries)
        return data.get("productList") if data else None

    async def fetch_product(self, url: str, *, ai: bool = True, retries: int = 3) -> Optional[Dict[str, Any]]:
        """Return Zyte `product` structure or ``None`` on failure."""
        options = {
            "extractFrom": "httpResponseBody",
        }
        if ai:
            options.update({"ai": True, "model": "2024-02-01"})
        payload = {
            "url": url,
            "product": True,
            "productOptions": options,
            "followRedirect": True,
        }
        data = await self._post(payload, retries)
        return data.get("product") if data else None

    # ------------------------------------------------------------------
    # Internal helper
    # ------------------------------------------------------------------
    async def _post(self, json_payload: Dict[str, Any], retries: int) -> Optional[Dict[str, Any]]:
        for attempt in range(retries):
            if attempt:
                await asyncio.sleep(2)
            try:
                async with httpx.AsyncClient(timeout=self._timeout) as client:
                    resp = await client.post(_ZYTE_ENDPOINT, auth=(self._api_key, ""), json=json_payload)
                if resp.status_code == 200:
                    return resp.json()
            except Exception:
                # swallow and retry
                pass
        return None

    # ------------------------------------------------------------------
    # Helpers
    # ------------------------------------------------------------------
    @staticmethod
    def from_env() -> Optional["ZyteClient"]:
        load_dotenv()
        key = os.getenv("ZYTE_API_KEY") or os.getenv("ZYTE_API_USER")
        return ZyteClient(key) if key else None 