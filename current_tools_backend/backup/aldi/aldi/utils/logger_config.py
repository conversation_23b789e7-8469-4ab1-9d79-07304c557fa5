"""Centralised logging setup for Aldi scraper"""

import logging


def setup_logging(level: int = logging.INFO, logfile: str | None = None) -> logging.Logger:
    fmt = "%(asctime)s %(levelname)s: %(message)s"
    logging.basicConfig(level=level, format=fmt, filename=logfile)
    return logging.getLogger(__name__)


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(f"aldi_scraper.{name}") 