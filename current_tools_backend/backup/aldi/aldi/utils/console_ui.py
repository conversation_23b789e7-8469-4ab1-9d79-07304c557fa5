"""Console UI helpers for the Aldi scraper (simplified).

This is a trimmed copy of `utils/console_ui.py` from the Zalando reference
project with Aldi-centric wording. The UI is intentionally minimal because most
production usage will be via the FastAPI endpoint, but it is convenient for
quick local tests.
"""

from typing import Dict, Any, Optional
import os
import platform


class ConsoleUI:
    def print_header(self, title: str):
        bar = "=" * 60
        print(f"\n{bar}\n{title.center(60)}\n{bar}")

    def print_main_menu(self):
        self.print_header("ALDI SCRAPER")
        print("1. Run async scraper")
        print("2. Exit\n")

    # --------------------------------------------------------------
    # Input helpers (very lightweight)
    # --------------------------------------------------------------

    _COUNTRY_LIST = list(
        {
            "UK": "United Kingdom",
            "Ireland": "Ireland",
            "France": "France",
            "Germany_Sud": "Germany Sud",
            "Spain": "Spain",
            "Australia": "Australia",
        }.items()
    )

    def get_scraping_parameters(self) -> Optional[Dict[str, Any]]:
        try:
            query = input("Search query [organic milk]: ").strip() or "organic milk"

            print("Available countries:")
            for idx, (code, name) in enumerate(self._COUNTRY_LIST, 1):
                print(f"{idx}. {code} – {name}")
            choice = input("Select country (1-{}): ".format(len(self._COUNTRY_LIST))).strip()
            try:
                idx = int(choice) - 1
                country_code = self._COUNTRY_LIST[idx][0]
            except Exception:
                country_code = "UK"

            limit_inp = input("Detailed products (k) [5]: ").strip()
            try:
                limit = int(limit_inp) if limit_inp else 5
                limit = max(1, min(20, limit))
            except ValueError:
                limit = 5

            return {"query": query, "country": country_code, "limit": limit}
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
            return None

    # --------------------------------------------------------------
    # Output helpers (minimal formatting)
    # --------------------------------------------------------------

    def display_parameters_summary(self, params: Dict[str, Any]):
        print("\nParameters:")
        for k, v in params.items():
            print(f"{k.title()}: {v}")
        print()

    def display_scraping_results(self, result: Dict[str, Any]):
        print("\nScraper results:")
        print("=" * 50)
        if result.get("success"):
            print(f"Listing products: {len(result.get('listing_products', []))}")
            print(f"Detailed products: {len(result.get('products', []))}")
            for i, p in enumerate(result.get("products", []), 1):
                print(f"{i}. {p.brand_name} – {p.name} ({p.price})")
        else:
            print("❌ Error:", result.get("error"))

    def wait_for_user(self):
        print()

    def display_warning(self, msg: str):
        print("Warning:", msg)

    def print_goodbye(self):
        print("\nGoodbye!") 