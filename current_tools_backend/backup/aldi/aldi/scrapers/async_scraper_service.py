#!/usr/bin/env python3
"""
Async Aldi Scraper Service
==========================

• Generates country-specific search URLs (static mapping)
• Uses Zyte Extract API to fetch *productList* JSON for the search page
• Returns up to 20 listing products and detailed information for the top *k* products (default 5)
• Runs everything asynchronously with concurrency limits (httpx + asyncio)

This mirrors the structure of `scrapers/async_scraper_service.py` in the Zalando reference project so
`core.application_controller` and the FastAPI layer can work the same way.
"""
from __future__ import annotations

import asyncio
import logging
import time
from dataclasses import dataclass, asdict, field
from typing import Any, Dict, List, Optional

import httpx

from utils.zyte_client import ZyteClient

logger = logging.getLogger(__name__)

# -----------------------------------------------------------------------------
# Dataclasses
# -----------------------------------------------------------------------------


@dataclass
class ProductInfo:
    # Basic
    name: str
    price: str
    currency: str
    currency_symbol: str
    url: str
    main_image_url: str = ""
    availability: str = ""
    brand_name: str = ""
    sku: str = ""
    description: str = ""

    # Raw fallback storage so that clients can inspect everything if needed
    raw_product_data: Dict[str, Any] = field(default_factory=dict)

    # Convenience – convert to dict (non-recursive for nested dataclasses)
    def to_dict(self):
        return asdict(self)


# -----------------------------------------------------------------------------
# Async Scraper Class
# -----------------------------------------------------------------------------


class AsyncAldiScraper:
    """Domain-aware Aldi scraper using the Zyte Extract API."""

    # Mapping copied (lightly adapted) from `aldi_crawler.AldiInteractiveCrawler.available_domains`
    _DOMAIN_CONFIGS: Dict[str, Dict[str, str]] = {
        "UK": {
            "domain": "aldi.co.uk",
            "currency": "GBP",
            "symbol": "£",
            "search_pattern": "https://www.aldi.co.uk/results?q={}",
        },
        "Ireland": {
            "domain": "aldi.ie",
            "currency": "EUR",
            "symbol": "€",
            "search_pattern": "https://www.aldi.ie/results?q={}",
        },
        "France": {
            "domain": "aldi.fr",
            "currency": "EUR",
            "symbol": "€",
            "search_pattern": "https://www.aldi.fr/recherche.html?query={}&searchCategory=Suggested%20Search",
        },
        "Germany_Nord": {
            "domain": "aldi-nord.de",
            "currency": "EUR",
            "symbol": "€",
            "search_pattern": "https://www.aldi-nord.de/suchergebnisse.html?query={}&searchCategory=Suggested%20Search",
        },
        "Germany_Sud": {
            "domain": "aldi-sued.de",
            "currency": "EUR",
            "symbol": "€",
            "search_pattern": "https://www.aldi-sued.de/de/suchergebnis.html?search={}",
        },
        "Spain": {
            "domain": "aldi.es",
            "currency": "EUR",
            "symbol": "€",
            "search_pattern": "https://www.aldi.es/busqueda.html?query={}&searchCategory=Suggested%20Search",
        },
        "Australia": {
            "domain": "aldi.com.au",
            "currency": "AUD",
            "symbol": "A$",
            "search_pattern": "https://www.aldi.com.au/results?q={}",
        },
        # ... you can add more configs if needed
    }

    def __init__(self, zyte_api_key: str | None = None):
        self._zyte = ZyteClient(zyte_api_key) if zyte_api_key else ZyteClient.from_env()
        if self._zyte is None:
            raise RuntimeError("Zyte API key is required for Aldi scraper. Set ZYTE_API_KEY env var or pass explicitly.")

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------

    async def scrape_products(
        self,
        query: str,
        country: str = "UK",
        *,
        limit: int = 5,
    ) -> Dict[str, Any]:
        """Return listing (max 20) + detailed data for *limit* products.

        Args:
            query: Search keywords like "organic milk".
            country: One of the keys in `_DOMAIN_CONFIGS` (case-sensitive).
            limit: Number of detailed products to fetch (<= 20).
        """
        try:
            country = country if country in self._DOMAIN_CONFIGS else "UK"
            limit = max(1, min(20, limit))

            search_url = self._generate_search_url(query, country)
            logger.info("🔗 Search URL %s", search_url)

            # -----------------------------------------------------------------
            # 1. Fetch productList via Zyte
            # -----------------------------------------------------------------
            product_list_data = await self._zyte.fetch_product_list(search_url)
            if not product_list_data or "products" not in product_list_data:
                return {"success": False, "error": "No product list returned", "search_url": search_url}

            all_listings = product_list_data["products"]
            listings_trimmed = all_listings[:20]  # keep top 20

            # Basic info structure we expose to clients
            listing_products = [
                {
                    "name": p.get("name", "N/A"),
                    "price": p.get("price", "N/A"),
                    "url": p.get("url", "N/A"),
                    "image": (p.get("mainImage", {}) or {}).get("url", "N/A")
                    if isinstance(p.get("mainImage"), dict)
                    else p.get("mainImage", "N/A"),
                }
                for p in listings_trimmed
            ]

            # -----------------------------------------------------------------
            # 2. Detailed extraction – concurrently for top *limit*
            # -----------------------------------------------------------------
            product_urls = [p.get("url", "") for p in listings_trimmed[:limit] if p.get("url")]
            detailed_products: List[ProductInfo] = []

            concurrency_limit = min(5, len(product_urls))
            semaphore = asyncio.Semaphore(concurrency_limit)

            async def _worker(idx: int, url: str):
                nonlocal detailed_products
                async with semaphore:
                    start_ts = time.perf_counter()
                    try:
                        raw_product = await self._zyte.fetch_product(url)
                        if not raw_product:
                            logger.warning("No product data for %s", url)
                            return
                        info = self._process_product_data(raw_product, url, country)
                        detailed_products.append(info)
                        logger.info("✅ Extracted %s", info.name)
                    except Exception as exc:
                        logger.error("❌ Error processing %s – %s", url, exc)
                    finally:
                        logger.debug("⏱️ %s done in %.2f s", url, time.perf_counter() - start_ts)

            await asyncio.gather(*[asyncio.create_task(_worker(i, u)) for i, u in enumerate(product_urls, 1)])

            result = {
                "success": True,
                "search_url": search_url,
                "country": country,
                "total_found": len(detailed_products),
                "products": detailed_products,
                "listing_products": listing_products,
            }
            return result
        except Exception as e:
            logger.error("Scraper error: %s", e, exc_info=True)
            return {"success": False, "error": str(e)}

    # ------------------------------------------------------------------
    # Helpers
    # ------------------------------------------------------------------

    def _generate_search_url(self, query: str, country: str) -> str:
        config = self._DOMAIN_CONFIGS[country]
        return config["search_pattern"].format(query.replace(" ", "+"))

    # Thin mapping layer – convert Zyte product JSON → ProductInfo dataclass
    def _process_product_data(self, raw: Dict[str, Any], url: str, country: str) -> ProductInfo:
        config = self._DOMAIN_CONFIGS.get(country, {})
        currency = raw.get("currency", config.get("currency", ""))
        price = raw.get("price", raw.get("salePrice", raw.get("regularPrice", "N/A")))

        return ProductInfo(
            name=raw.get("name", "N/A"),
            price=price,
            currency=currency,
            currency_symbol=config.get("symbol", ""),
            url=url,
            main_image_url=(raw.get("mainImage", {}) or {}).get("url", ""),
            availability=raw.get("availability", ""),
            brand_name=(raw.get("brand", {}) or {}).get("name", ""),
            sku=raw.get("sku", ""),
            description=raw.get("description", ""),
            raw_product_data=raw,
        )


# Aliases for backward compatibility (mirrors Zalando ref)
AsyncScraperService = AsyncAldiScraper
AsyncDomainSpecificScraper = AsyncAldiScraper 