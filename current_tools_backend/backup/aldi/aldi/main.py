#!/usr/bin/env python3
"""Non-interactive test entry-point for the Aldi Async scraper."""

import asyncio

from utils.logger_config import setup_logging
from scrapers.async_scraper_service import AsyncAldiScraper


def main() -> None:
    setup_logging()

    params = {"query": "organic milk", "country": "UK", "limit": 5}

    print("=" * 60)
    print("                 🚀 ASYNC ALDI SCRAPER – AUTO MODE                 ")
    print("=" * 60)

    for k, v in params.items():
        print(f"{k.title()}: {v}")

    print("\nInitializing scraper...\n")

    scraper = AsyncAldiScraper()
    result = asyncio.run(scraper.scrape_products(**params))

    if result.get("success"):
        print(f"\n✅ Finished – scraped {result.get('total_found')} products.")
    else:
        print(f"\n❌ Error: {result.get('error')}")


if __name__ == "__main__":
    main() 