# 🚀 Aldi Async Scraper

This project scrapes product listings from Aldi international websites using the Zyte Extract
API. It mirrors the architecture of the Zalando master scraper – clean layered
packages (`core/`, `scrapers/`, `utils/`) and both CLI & FastAPI entry-points.

## Quick start

```bash
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt

# Run simple CLI test
python main.py

# Run API (dev)
uvicorn api:app --reload --port 8000
```

## Features

* Async & parallel scraping with httpx
* Country-aware search URL generation (UK, France, Spain, …)
* Returns top-20 listing products plus detailed data for top-K (default 5)
* FastAPI endpoint `/search` suitable for agent usage
* Small console UI for ad-hoc runs (`core.ApplicationController`)

Set the `ZYTE_API_KEY` environment variable or pass `zyte_api_key` parameter to
API/CLI. 