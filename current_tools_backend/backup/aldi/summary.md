# 📄 Project Summary: Aldi Async Scraper

This repository provides an **async web scraper** for Aldi international websites.
It follows the same layered design as the reference Zalando scraper:

```
core/            # Business logic, models
scrapers/        # Async scraper implementation (Zyte Extract API)
utils/           # Console UI, logging, Zyte client helpers
api.py           # FastAPI entry-point (tool-style)
main.py          # Minimal CLI runner (non-interactive)
Dockerfile       # Container image for production
docker-compose.yml
```

Main features:
* Country-aware search URL generation
* Top-20 listings + detailed info for top-K (default 5)
* Fully async (httpx) with concurrency limits
* Re-usable FastAPI `/search` endpoint (used by `test_api.py`)
* Clean dataclasses & pretty console output 