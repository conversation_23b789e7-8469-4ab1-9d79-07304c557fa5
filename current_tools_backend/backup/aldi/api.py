from fastapi import FastAPI, Query
from fastapi.responses import JSONResponse
from dataclasses import asdict, is_dataclass
import asyncio

from scrapers.async_scraper_service import AsyncAldiScraper

app = FastAPI(
    title="Aldi Search Scraper API",
    description="Tool-style API to search Aldi stores and fetch product data.",
    version="0.1.0",
)


@app.on_event("startup")
async def startup_event():
    app.state.scraper_cache = {}
    app.state.cache_lock = asyncio.Lock()


@app.on_event("shutdown")
async def shutdown_event():
    app.state.scraper_cache.clear()


@app.get("/", tags=["meta"], summary="API manual for the LLM tool")
async def root_manual():
    manual = {
        "name": "Aldi Search Scraper API",
        "description": "Search Aldi and obtain product information.",
        "endpoints": {
            "/search": {
                "method": "GET",
                "parameters": {
                    "query": "string – required",
                    "country": "string – optional. Default UK",
                    "limit": "int – optional detailed products (1-20). Default 5",
                    "zyte_api_key": "string – required Zyte key",
                },
            }
        },
    }
    return manual


@app.get("/search", tags=["search"], summary="Search Aldi and fetch product data")
async def search_products(
    query: str = Query(..., min_length=1, description="Search keyword(s)"),
    country: str = Query("UK", description="Country key, e.g. UK, France, Spain"),
    zyte_api_key: str = Query(..., description="Zyte Extract API key"),
    limit: int = Query(5, ge=1, le=20, description="Number of detailed products to parse (1-20)"),
):
    # Re-use scraper per Zyte key
    async with app.state.cache_lock:
        scraper = app.state.scraper_cache.get(zyte_api_key)
        if scraper is None:
            scraper = AsyncAldiScraper(zyte_api_key=zyte_api_key)
            app.state.scraper_cache[zyte_api_key] = scraper

    result = await scraper.scrape_products(query=query, country=country, limit=limit)

    if result.get("success") and "products" in result:
        detailed_products = result["products"]
        result["products"] = [asdict(p) if is_dataclass(p) else p for p in detailed_products]

    return JSONResponse(content=result) 