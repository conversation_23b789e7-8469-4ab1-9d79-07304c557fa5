import os
import json
from fast_hotels import get_hotels
from pprint import pprint

def test():
    try:
        # Get hotel results for Mila, Algeria
        result_json = get_hotels(
            destination="skikda, algeria",
            check_in="2025-05-01",
            check_out="2025-05-05",
            adults=2,
            children=0,
            debug=True,
            output_format="json",
            json_indent=2
        )
        
        pprint(result_json)
        # Save the JSON output to a file
        with open("mila_algeria_results.json", "w", encoding="utf-8") as f:
            f.write(result_json)
        
        print("Saved search results to mila_algeria_results.json")
        
        # Parse the JSON to check for the specific hotel
        result_dict = json.loads(result_json)
    
        # Print summary of results
        print(f"Found {len(result_dict['hotels'])} hotels")
        
        # Check if "Hôtel TAPIS ROUGE" is in the results
        
        for i, hotel in enumerate(result_dict['hotels']):
            print(f"\n{i+1}. {hotel['name']} - {hotel['price']}")
            print(f"   Location: {hotel['location']}")
            print('')
            print('')
            
            # if "tapis rouge" in hotel['name'].lower():
            #     tapis_rouge_found = True
            #     print("   *** FOUND TARGET HOTEL: Hôtel TAPIS ROUGE ***")
            
            # if hotel['providers']:
            #     print(f"   Providers: {len(hotel['providers'])}")
            #     for j, provider in enumerate(hotel['providers'][:2]):
            #         print(f"     - {provider['name']}: {provider['price']}")
            #         if provider['link']:
            #             print(f"       Link: {provider['link'][:60]}...")
        
        # if tapis_rouge_found:
        #     print("\nSUCCESS: Found 'Hôtel TAPIS ROUGE' in the results!")
        # else:
        #     print("\nFAILURE: 'Hôtel TAPIS ROUGE' was not found in the results.")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")

if __name__ == "__main__":
    test()
