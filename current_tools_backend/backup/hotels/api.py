import json
from typing import Optional, Dict, Any, List, Union
from datetime import date, datetime
from pprint import pprint
from fastapi import FastAPI, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from fast_hotels import (
    get_hotels, get_hotel_details, get_providers_sync,
    HotelDestination, HotelDateRange, HotelDate, HotelGuests,
    HotelSettings, HotelCurrency, create_filter
)

app = FastAPI(
    title="Fast Hotels API",
    description="API for Google Hotels search and booking",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Helper function to convert objects to dictionaries
def to_dict(obj):
    if hasattr(obj, '__dict__'):
        result = {}
        for key, value in obj.__dict__.items():
            if not key.startswith('_'):
                result[key] = to_dict(value)
        return result
    elif isinstance(obj, list):
        return [to_dict(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: to_dict(value) for key, value in obj.items()}
    else:
        return obj

@app.get("/")
async def root():
    return {
        "message": "Fast Hotels API is running",
        "docs": "/docs",
        "endpoints": [
            "/hotels/search",
            "/hotels/details",
            "/hotels/providers"
        ],
        "payload_formats": {
            "/hotels/search": {
                "method": "GET",
                "query_parameters": {
                    "destination": "string (required) - Destination (city, region, etc.)",
                    "check_in": "string (required) - Check-in date (YYYY-MM-DD)",
                    "check_out": "string (required) - Check-out date (YYYY-MM-DD)",
                    "adults": "integer (optional) - Number of adults (default: 1)",
                    "children": "integer (optional) - Number of children (default: 0)",
                    "currency": "string (optional) - Currency code (e.g., USD, EUR) (default: USD)",
                    "include_providers": "boolean (optional) - Include provider details for each hotel (default: false)",
                    "debug": "boolean (optional) - Enable debug mode (default: false)"
                },
                "example": "/hotels/search?destination=New+York&check_in=2025-07-01&check_out=2025-07-05&adults=2&currency=USD"
            },
            "/hotels/details/{hotel_id}": {
                "method": "GET",
                "path_parameters": {
                    "hotel_id": "string (required) - Hotel ID"
                },
                "query_parameters": {
                    "check_in": "string (required) - Check-in date (YYYY-MM-DD)",
                    "check_out": "string (required) - Check-out date (YYYY-MM-DD)",
                    "adults": "integer (optional) - Number of adults (default: 1)",
                    "children": "integer (optional) - Number of children (default: 0)",
                    "currency": "string (optional) - Currency code (e.g., USD, EUR) (default: USD)",
                    "debug": "boolean (optional) - Enable debug mode (default: false)"
                },
                "example": "/hotels/details/123456?check_in=2025-07-01&check_out=2025-07-05&adults=2"
            },
            "/hotels/providers": {
                "method": "GET",
                "query_parameters": {
                    "booking_link": "string (required) - Hotel booking link",
                    "debug": "boolean (optional) - Enable debug mode (default: false)"
                },
                "example": "/hotels/providers?booking_link=https://www.google.com/travel/hotels/entity/..."
            }
        }
    }

@app.get("/hotels/search")
async def search_hotels(
    destination: str = Query(..., description="Destination (city, region, etc.)"),
    check_in: str = Query(..., description="Check-in date (YYYY-MM-DD)"),
    check_out: str = Query(..., description="Check-out date (YYYY-MM-DD)"),
    adults: int = Query(1, description="Number of adults"),
    children: int = Query(0, description="Number of children"),
    currency: str = Query("USD", description="Currency code (e.g., USD, EUR)"),
    include_providers: bool = Query(False, description="Include provider details for each hotel"),
    debug: bool = Query(False, description="Enable debug mode")
):
    try:
        # Validate dates
        try:
            check_in_date = datetime.strptime(check_in, "%Y-%m-%d").date()
            check_out_date = datetime.strptime(check_out, "%Y-%m-%d").date()
            if check_in_date >= check_out_date:
                raise HTTPException(status_code=400, detail="Check-out date must be after check-in date")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

        # Get hotels
        result = get_hotels(
            destination=destination,
            check_in=check_in,
            check_out=check_out,
            adults=adults,
            children=children,
            currency=currency,
            debug=debug,
            output_format="dict"
        )

        pprint(result)

        # # Fetch provider details if requested
        # if include_providers and result.get('hotels'):
        #     # Collect booking links from all hotels
        #     booking_links = []
        #     hotel_indices = {}

        #     for i, hotel in enumerate(result['hotels']):
        #         if 'booking_link' in hotel and hotel['booking_link']:
        #             booking_links.append(hotel['booking_link'])
        #             hotel_indices[hotel['booking_link']] = i

        #     # Fetch provider details for all hotels in parallel
        #     if booking_links:
        #         providers_by_link = get_providers_sync(booking_links, debug)

        #         # Add provider details to the result
        #         for link, providers in providers_by_link.items():
        #             if link in hotel_indices and providers:
        #                 hotel_index = hotel_indices[link]
        #                 result['hotels'][hotel_index]['providers'] = [
        #                     {
        #                         'name': provider.name,
        #                         'price': provider.price,
        #                         'link': provider.link
        #                     }
        #                     for provider in providers
        #                 ]

        return JSONResponse(content=result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/hotels/details/{hotel_id}")
async def hotel_details(
    hotel_id: str,
    check_in: str = Query(..., description="Check-in date (YYYY-MM-DD)"),
    check_out: str = Query(..., description="Check-out date (YYYY-MM-DD)"),
    adults: int = Query(1, description="Number of adults"),
    children: int = Query(0, description="Number of children"),
    currency: str = Query("USD", description="Currency code (e.g., USD, EUR)"),
    debug: bool = Query(False, description="Enable debug mode")
):
    try:
        # Validate dates
        try:
            check_in_date = datetime.strptime(check_in, "%Y-%m-%d").date()
            check_out_date = datetime.strptime(check_out, "%Y-%m-%d").date()
            if check_in_date >= check_out_date:
                raise HTTPException(status_code=400, detail="Check-out date must be after check-in date")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")

        # Get hotel details
        result = get_hotel_details(
            hotel_id=hotel_id,
            check_in=check_in,
            check_out=check_out,
            adults=adults,
            children=children,
            currency=currency,
            debug=debug,
            output_format="dict"
        )

        return JSONResponse(content=result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/hotels/providers")
async def get_providers(
    booking_link: str = Query(..., description="Hotel booking link"),
    debug: bool = Query(False, description="Enable debug mode")
):
    try:
        # Get provider details
        providers = get_providers_sync([booking_link], debug)

        if booking_link in providers and providers[booking_link]:
            # Convert provider objects to dictionaries
            providers_list = [
                {
                    'name': provider.name,
                    'price': provider.price,
                    'link': provider.link
                }
                for provider in providers[booking_link]
            ]

            return JSONResponse(content={"providers": providers_list})
        else:
            return JSONResponse(content={"providers": []})

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Run with: uvicorn api:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)