# Fast Hotels API Test Script

This script tests the Fast Hotels API endpoints by making HTTP requests to the running API service.

## Prerequisites

- Python 3.6+
- The Fast Hotels API service must be running
- Required Python packages:
  - requests
  - argparse

## Installation

```bash
pip install requests
```

## Usage

### Start the API Service

Before running the tests, make sure the API service is running:

```bash
cd fsp
uvicorn api:app --reload --port 8001
```

### Run the Tests

To run all tests:

```bash
python test_api.py
```

To run a specific test:

```bash
python test_api.py --test root
python test_api.py --test search
python test_api.py --test details
python test_api.py --test providers
```

To test against a different URL:

```bash
python test_api.py --url http://localhost:8000
```

## Test Descriptions

The script tests the following endpoints:

1. **Root Endpoint Test**
   - Tests the `/` endpoint
   - Verifies that the API is running and returns basic information

2. **Hotels Search Test**
   - Tests the `/hotels/search` endpoint
   - Searches for hotels in New York with a check-in date 7 days from now
   - Displays information about the first few hotels found

3. **Hotel Details Test**
   - Tests the `/hotels/details/{hotel_id}` endpoint
   - Uses the first hotel ID from the search test
   - Displays detailed information about the hotel

4. **Hotel Providers Test**
   - Tests the `/hotels/providers` endpoint
   - Uses the booking link from the first hotel in the search test
   - Displays the booking providers and their prices

## Notes

- The details and providers tests depend on the search test to get a hotel ID and booking link
- If you run the tests individually, run the search test first
- The API key is managed by the API service, not the test script 