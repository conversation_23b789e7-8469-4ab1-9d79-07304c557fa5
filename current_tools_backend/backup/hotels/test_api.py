import json
import argparse
import requests
from datetime import datetime, timedelta
from pprint import pprint


def test_root_endpoint(base_url):
    """Test the root endpoint."""
    print("\n=== TESTING ROOT ENDPOINT ===\n")
    
    try:
        response = requests.get(f"{base_url}/")
        response.raise_for_status()
        
        data = response.json()
        print("Root endpoint response:")
        pprint(data)
        
        return True
    except Exception as e:
        print(f"Error testing root endpoint: {e}")
        return False


def test_hotels_search(base_url):
    """Test the hotels search endpoint."""
    print("\n=== TESTING HOTELS SEARCH ENDPOINT ===\n")
    
    # Set up test parameters
    today = datetime.now().date()
    check_in = (today + timedelta(days=7)).strftime("%Y-%m-%d")
    check_out = (today + timedelta(days=10)).strftime("%Y-%m-%d")
    
    params = {
        "destination": "New York",
        "check_in": check_in,
        "check_out": check_out,
        "adults": 2,
        "children": 0,
        "currency": "USD",
        "include_providers": True,
        "debug": True
    }
    
    try:
        print(f"Searching for hotels in {params['destination']} from {params['check_in']} to {params['check_out']}")
        response = requests.get(f"{base_url}/hotels/search", params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if 'hotels' in data and data['hotels']:
            hotels = data['hotels']
            print(f"Found {len(hotels)} hotels")
            
            # Print details of first 3 hotels
            for i, hotel in enumerate(hotels[:3]):
                print(f"\n{i+1}. {hotel.get('name', 'N/A')}")
                print(f"   Price: {hotel.get('price', 'N/A')}")
                print(f"   Rating: {hotel.get('rating', 'N/A')}")
                print(f"   Address: {hotel.get('address', 'N/A')}")
                print(f"   Hotel ID: {hotel.get('id', 'N/A')}")
                
                # Save the first hotel ID for details test
                if i == 0 and 'id' in hotel:
                    global first_hotel_id
                    first_hotel_id = hotel['id']
                    
                # Save the first booking link for providers test
                if i == 0 and 'booking_link' in hotel:
                    global first_booking_link
                    first_booking_link = hotel['booking_link']
                
                # Print provider information if available
                if 'providers' in hotel and hotel['providers']:
                    print(f"   Providers:")
                    for provider in hotel['providers'][:3]:
                        print(f"     - {provider.get('name', 'N/A')}: {provider.get('price', 'N/A')}")
            
            if len(hotels) > 3:
                print(f"\n...and {len(hotels) - 3} more hotels")
        else:
            print("No hotels found or API error")
        
        return True
    except Exception as e:
        print(f"Error testing hotels search: {e}")
        return False


def test_hotel_details(base_url):
    """Test the hotel details endpoint."""
    print("\n=== TESTING HOTEL DETAILS ENDPOINT ===\n")
    
    # Check if we have a hotel ID from the search test
    global first_hotel_id
    if not first_hotel_id:
        print("No hotel ID available for testing. Run the search test first.")
        return False
    
    # Set up test parameters
    today = datetime.now().date()
    check_in = (today + timedelta(days=7)).strftime("%Y-%m-%d")
    check_out = (today + timedelta(days=10)).strftime("%Y-%m-%d")
    
    params = {
        "check_in": check_in,
        "check_out": check_out,
        "adults": 2,
        "children": 0,
        "currency": "USD",
        "debug": True
    }
    
    try:
        print(f"Getting details for hotel ID: {first_hotel_id}")
        response = requests.get(f"{base_url}/hotels/details/{first_hotel_id}", params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if data:
            hotel = data
            print(f"\nHotel Details:")
            print(f"Name: {hotel.get('name', 'N/A')}")
            print(f"Address: {hotel.get('address', 'N/A')}")
            print(f"Rating: {hotel.get('rating', 'N/A')}")
            print(f"Price: {hotel.get('price', 'N/A')}")
            
            # Print amenities if available
            if 'amenities' in hotel and hotel['amenities']:
                print(f"\nAmenities:")
                for amenity in hotel['amenities'][:5]:
                    print(f"- {amenity}")
                
                if len(hotel['amenities']) > 5:
                    print(f"...and {len(hotel['amenities']) - 5} more amenities")
            
            # Print reviews if available
            if 'reviews' in hotel and hotel['reviews']:
                print(f"\nReviews:")
                for review in hotel['reviews'][:3]:
                    print(f"- {review.get('rating', 'N/A')}/5: {review.get('text', 'N/A')[:100]}...")
                
                if len(hotel['reviews']) > 3:
                    print(f"...and {len(hotel['reviews']) - 3} more reviews")
        else:
            print("No hotel details found or API error")
        
        return True
    except Exception as e:
        print(f"Error testing hotel details: {e}")
        return False


def test_hotel_providers(base_url):
    """Test the hotel providers endpoint."""
    print("\n=== TESTING HOTEL PROVIDERS ENDPOINT ===\n")
    
    # Check if we have a booking link from the search test
    global first_booking_link
    if not first_booking_link:
        print("No booking link available for testing. Run the search test first.")
        return False
    
    params = {
        "booking_link": first_booking_link,
        "debug": True
    }
    
    try:
        print(f"Getting providers for booking link: {first_booking_link[:50]}...")
        response = requests.get(f"{base_url}/hotels/providers", params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if 'providers' in data and data['providers']:
            providers = data['providers']
            print(f"\nFound {len(providers)} providers:")
            
            for i, provider in enumerate(providers):
                print(f"{i+1}. {provider.get('name', 'N/A')}: {provider.get('price', 'N/A')}")
        else:
            print("No providers found or API error")
        
        return True
    except Exception as e:
        print(f"Error testing hotel providers: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Test Fast Hotels API endpoints")
    parser.add_argument('--test', choices=['root', 'search', 'details', 'providers', 'all'], 
                      default='all', help="Type of test to run")
    parser.add_argument('--url', default='http://localhost:8005', 
                      help="Base URL of the API (default: http://localhost:8005)")
    
    args = parser.parse_args()
    base_url = args.url
    
    # Initialize global variables
    global first_hotel_id, first_booking_link
    first_hotel_id = None
    first_booking_link = None
    

    test_hotels_search(base_url)
    
    # if args.test == 'details' or args.test == 'all':
    #     test_hotel_details(base_url)
    
    # if args.test == 'providers' or args.test == 'all':
    #     test_hotel_providers(base_url)


if __name__ == "__main__":
    main() 