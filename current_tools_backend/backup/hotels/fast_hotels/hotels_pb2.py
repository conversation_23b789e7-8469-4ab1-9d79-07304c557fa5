# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hotels.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='hotels.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x0chotels.proto\"\x1d\n\rHotelLocation\x12\x0c\n\x04name\x18\x07 \x01(\t\"4\n\x10HotelDestination\x12 \n\x08location\x18\x02 \x01(\x0b\x32\x0e.HotelLocation\"5\n\tHotelDate\x12\x0c\n\x04year\x18\x01 \x01(\x05\x12\r\n\x05month\x18\x02 \x01(\x05\x12\x0b\n\x03\x64\x61y\x18\x03 \x01(\x05\"D\n\x0eHotelDateRange\x12\x19\n\x05start\x18\x01 \x01(\x0b\x32\n.HotelDate\x12\x17\n\x03\x65nd\x18\x02 \x01(\x0b\x32\n.HotelDate\"3\n\x11HotelDateSettings\x12\x1e\n\x05\x64\x61tes\x18\x02 \x01(\x0b\x32\x0f.HotelDateRange\"[\n\x0bHotelSearch\x12&\n\x0b\x64\x65stination\x18\x01 \x01(\x0b\x32\x11.HotelDestination\x12$\n\x08settings\x18\x02 \x01(\x0b\x32\x12.HotelDateSettings\"\x1d\n\rHotelCurrency\x12\x0c\n\x04\x63ode\x18\x07 \x01(\t\"1\n\rHotelSettings\x12 \n\x08\x63urrency\x18\x01 \x01(\x0b\x32\x0e.HotelCurrency\"K\n\tHotelInfo\x12\x1c\n\x06search\x18\x03 \x01(\x0b\x32\x0c.HotelSearch\x12 \n\x08settings\x18\x05 \x01(\x0b\x32\x0e.HotelSettings\".\n\x0eHotelGuestInfo\x12\x1c\n\x06guests\x18\x01 \x03(\x0e\x32\x0c.HotelGuests*k\n\x0bHotelRating\x12\x12\n\x0eUNKNOWN_RATING\x10\x00\x12\x0c\n\x08ONE_STAR\x10\x01\x12\x0c\n\x08TWO_STAR\x10\x02\x12\x0e\n\nTHREE_STAR\x10\x03\x12\r\n\tFOUR_STAR\x10\x04\x12\r\n\tFIVE_STAR\x10\x05*6\n\x0bHotelGuests\x12\x11\n\rUNKNOWN_GUEST\x10\x00\x12\t\n\x05\x41\x44ULT\x10\x01\x12\t\n\x05\x43HILD\x10\x02\x62\x06proto3'
)

_HOTELRATING = _descriptor.EnumDescriptor(
  name='HotelRating',
  full_name='HotelRating',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN_RATING', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ONE_STAR', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TWO_STAR', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THREE_STAR', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FOUR_STAR', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FIVE_STAR', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=579,
  serialized_end=686,
)
_sym_db.RegisterEnumDescriptor(_HOTELRATING)

HotelRating = enum_type_wrapper.EnumTypeWrapper(_HOTELRATING)
_HOTELGUESTS = _descriptor.EnumDescriptor(
  name='HotelGuests',
  full_name='HotelGuests',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN_GUEST', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ADULT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CHILD', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=688,
  serialized_end=742,
)
_sym_db.RegisterEnumDescriptor(_HOTELGUESTS)

HotelGuests = enum_type_wrapper.EnumTypeWrapper(_HOTELGUESTS)
UNKNOWN_RATING = 0
ONE_STAR = 1
TWO_STAR = 2
THREE_STAR = 3
FOUR_STAR = 4
FIVE_STAR = 5
UNKNOWN_GUEST = 0
ADULT = 1
CHILD = 2



_HOTELLOCATION = _descriptor.Descriptor(
  name='HotelLocation',
  full_name='HotelLocation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='HotelLocation.name', index=0,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16,
  serialized_end=45,
)


_HOTELDESTINATION = _descriptor.Descriptor(
  name='HotelDestination',
  full_name='HotelDestination',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='location', full_name='HotelDestination.location', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=47,
  serialized_end=99,
)


_HOTELDATE = _descriptor.Descriptor(
  name='HotelDate',
  full_name='HotelDate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='year', full_name='HotelDate.year', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='month', full_name='HotelDate.month', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='day', full_name='HotelDate.day', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=154,
)


_HOTELDATERANGE = _descriptor.Descriptor(
  name='HotelDateRange',
  full_name='HotelDateRange',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start', full_name='HotelDateRange.start', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='end', full_name='HotelDateRange.end', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=156,
  serialized_end=224,
)


_HOTELDATESETTINGS = _descriptor.Descriptor(
  name='HotelDateSettings',
  full_name='HotelDateSettings',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='dates', full_name='HotelDateSettings.dates', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=277,
)


_HOTELSEARCH = _descriptor.Descriptor(
  name='HotelSearch',
  full_name='HotelSearch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='destination', full_name='HotelSearch.destination', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settings', full_name='HotelSearch.settings', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=279,
  serialized_end=370,
)


_HOTELCURRENCY = _descriptor.Descriptor(
  name='HotelCurrency',
  full_name='HotelCurrency',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='HotelCurrency.code', index=0,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=372,
  serialized_end=401,
)


_HOTELSETTINGS = _descriptor.Descriptor(
  name='HotelSettings',
  full_name='HotelSettings',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='currency', full_name='HotelSettings.currency', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=403,
  serialized_end=452,
)


_HOTELINFO = _descriptor.Descriptor(
  name='HotelInfo',
  full_name='HotelInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='search', full_name='HotelInfo.search', index=0,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='settings', full_name='HotelInfo.settings', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=454,
  serialized_end=529,
)


_HOTELGUESTINFO = _descriptor.Descriptor(
  name='HotelGuestInfo',
  full_name='HotelGuestInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='guests', full_name='HotelGuestInfo.guests', index=0,
      number=1, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=531,
  serialized_end=577,
)

_HOTELDESTINATION.fields_by_name['location'].message_type = _HOTELLOCATION
_HOTELDATERANGE.fields_by_name['start'].message_type = _HOTELDATE
_HOTELDATERANGE.fields_by_name['end'].message_type = _HOTELDATE
_HOTELDATESETTINGS.fields_by_name['dates'].message_type = _HOTELDATERANGE
_HOTELSEARCH.fields_by_name['destination'].message_type = _HOTELDESTINATION
_HOTELSEARCH.fields_by_name['settings'].message_type = _HOTELDATESETTINGS
_HOTELSETTINGS.fields_by_name['currency'].message_type = _HOTELCURRENCY
_HOTELINFO.fields_by_name['search'].message_type = _HOTELSEARCH
_HOTELINFO.fields_by_name['settings'].message_type = _HOTELSETTINGS
_HOTELGUESTINFO.fields_by_name['guests'].enum_type = _HOTELGUESTS
DESCRIPTOR.message_types_by_name['HotelLocation'] = _HOTELLOCATION
DESCRIPTOR.message_types_by_name['HotelDestination'] = _HOTELDESTINATION
DESCRIPTOR.message_types_by_name['HotelDate'] = _HOTELDATE
DESCRIPTOR.message_types_by_name['HotelDateRange'] = _HOTELDATERANGE
DESCRIPTOR.message_types_by_name['HotelDateSettings'] = _HOTELDATESETTINGS
DESCRIPTOR.message_types_by_name['HotelSearch'] = _HOTELSEARCH
DESCRIPTOR.message_types_by_name['HotelCurrency'] = _HOTELCURRENCY
DESCRIPTOR.message_types_by_name['HotelSettings'] = _HOTELSETTINGS
DESCRIPTOR.message_types_by_name['HotelInfo'] = _HOTELINFO
DESCRIPTOR.message_types_by_name['HotelGuestInfo'] = _HOTELGUESTINFO
DESCRIPTOR.enum_types_by_name['HotelRating'] = _HOTELRATING
DESCRIPTOR.enum_types_by_name['HotelGuests'] = _HOTELGUESTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

HotelLocation = _reflection.GeneratedProtocolMessageType('HotelLocation', (_message.Message,), {
  'DESCRIPTOR' : _HOTELLOCATION,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelLocation)
  })
_sym_db.RegisterMessage(HotelLocation)

HotelDestination = _reflection.GeneratedProtocolMessageType('HotelDestination', (_message.Message,), {
  'DESCRIPTOR' : _HOTELDESTINATION,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelDestination)
  })
_sym_db.RegisterMessage(HotelDestination)

HotelDate = _reflection.GeneratedProtocolMessageType('HotelDate', (_message.Message,), {
  'DESCRIPTOR' : _HOTELDATE,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelDate)
  })
_sym_db.RegisterMessage(HotelDate)

HotelDateRange = _reflection.GeneratedProtocolMessageType('HotelDateRange', (_message.Message,), {
  'DESCRIPTOR' : _HOTELDATERANGE,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelDateRange)
  })
_sym_db.RegisterMessage(HotelDateRange)

HotelDateSettings = _reflection.GeneratedProtocolMessageType('HotelDateSettings', (_message.Message,), {
  'DESCRIPTOR' : _HOTELDATESETTINGS,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelDateSettings)
  })
_sym_db.RegisterMessage(HotelDateSettings)

HotelSearch = _reflection.GeneratedProtocolMessageType('HotelSearch', (_message.Message,), {
  'DESCRIPTOR' : _HOTELSEARCH,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelSearch)
  })
_sym_db.RegisterMessage(HotelSearch)

HotelCurrency = _reflection.GeneratedProtocolMessageType('HotelCurrency', (_message.Message,), {
  'DESCRIPTOR' : _HOTELCURRENCY,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelCurrency)
  })
_sym_db.RegisterMessage(HotelCurrency)

HotelSettings = _reflection.GeneratedProtocolMessageType('HotelSettings', (_message.Message,), {
  'DESCRIPTOR' : _HOTELSETTINGS,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelSettings)
  })
_sym_db.RegisterMessage(HotelSettings)

HotelInfo = _reflection.GeneratedProtocolMessageType('HotelInfo', (_message.Message,), {
  'DESCRIPTOR' : _HOTELINFO,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelInfo)
  })
_sym_db.RegisterMessage(HotelInfo)

HotelGuestInfo = _reflection.GeneratedProtocolMessageType('HotelGuestInfo', (_message.Message,), {
  'DESCRIPTOR' : _HOTELGUESTINFO,
  '__module__' : 'hotels_pb2'
  # @@protoc_insertion_point(class_scope:HotelGuestInfo)
  })
_sym_db.RegisterMessage(HotelGuestInfo)


# @@protoc_insertion_point(module_scope)
