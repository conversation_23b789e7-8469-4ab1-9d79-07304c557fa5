from dataclasses import dataclass, field
from typing import Dict, List, Literal, Optional, Any, Union
import json

@dataclass
class Provider:
    """Provider information."""
    name: str
    price: str = "Price unavailable"
    link: str = ""
    logo_url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation.
        """
        result = {
            "name": self.name,
            "price": self.price,
            "link": self.link
        }
        
        if self.logo_url:
            result["logo_url"] = self.logo_url
            
        return result

@dataclass
class Hotel:
    """Hotel information."""
    
    def __init__(
        self,
        *,
        name: str,
        price: str,
        rating: int,
        location: str,
        is_featured: bool = False,
        property_type: str = "Hotel",
        review_score: str = "",
        review_count: str = "",
        image_url: Optional[str] = None,
        description: Optional[str] = None,
        amenities: Optional[List[str]] = None,
        providers: Optional[List["Provider"]] = None,
        booking_link: Optional[str] = None
    ):
        """Initialize Hotel.
        
        Args:
            name (str): Hotel name.
            price (str): Price.
            rating (int): Rating.
            location (str): Location.
            is_featured (bool, optional): Whether this is a featured hotel. Defaults to False.
            property_type (str, optional): Property type. Defaults to "Hotel".
            review_score (str, optional): Review score. Defaults to "".
            review_count (str, optional): Review count. Defaults to "".
            image_url (Optional[str], optional): Image URL. Defaults to None.
            description (Optional[str], optional): Description. Defaults to None.
            amenities (Optional[List[str]], optional): Amenities. Defaults to None.
            providers (Optional[List["Provider"]], optional): Providers. Defaults to None.
            booking_link (Optional[str], optional): Direct booking link. Defaults to None.
        """
        self.name = name
        self.price = price
        self.rating = rating
        self.location = location
        self.is_featured = is_featured
        self.property_type = property_type
        self.review_score = review_score
        self.review_count = review_count
        self.image_url = image_url
        self.description = description
        self.amenities = amenities or []
        self.providers = providers or []
        self.booking_link = booking_link
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary.
        
        Returns:
            Dict[str, Any]: Dictionary representation.
        """
        result = {
            "name": self.name,
            "price": self.price,
            "rating": self.rating,
            "location": self.location,
            "is_featured": self.is_featured,
            "property_type": self.property_type
        }
        
        # Add optional fields if they exist
        if self.review_score:
            result["review_score"] = self.review_score
        
        if self.review_count:
            result["review_count"] = self.review_count
        
        if self.image_url:
            result["image_url"] = self.image_url
        
        if self.description:
            result["description"] = self.description
        
        if self.amenities:
            result["amenities"] = self.amenities
        
        if self.providers:
            result["providers"] = [provider.to_dict() for provider in self.providers]
        
        if self.booking_link:
            result["booking_link"] = self.booking_link
            
        return result
    
    def to_json(self, indent: Optional[int] = None) -> str:
        """Convert to JSON.
        
        Args:
            indent (Optional[int], optional): Indentation level. Defaults to None.
        
        Returns:
            str: JSON representation.
        """
        return json.dumps(self.to_dict(), indent=indent)

@dataclass
class Result:
    """Search result."""
    hotels: List[Hotel]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "hotels": [hotel.to_dict() for hotel in self.hotels]
        }
    
    def to_json(self, indent: Optional[int] = None) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=indent)
