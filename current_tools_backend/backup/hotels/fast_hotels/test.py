import sys
import os

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fast_hotels import get_hotels

def test_basic_search():
    """Test basic hotel search functionality."""
    try:
        result = get_hotels(
            destination="New York",
            check_in="2025-05-01",
            check_out="2025-05-05",
            adults=2,
            children=0,
            debug=True
        )
        
        print(f"Found {len(result.hotels)} hotels in New York")
        for i, hotel in enumerate(result.hotels[:5]):  # Print first 5 hotels
            print(f"{i+1}. {hotel.name} - {hotel.price}")
            print(f"   Rating: {hotel.rating} stars")
            print(f"   Location: {hotel.location}")
            print(f"   Reviews: {hotel.review_score} ({hotel.review_count})")
            print()
        
        return True
    except Exception as e:
        print(f"Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_basic_search()
