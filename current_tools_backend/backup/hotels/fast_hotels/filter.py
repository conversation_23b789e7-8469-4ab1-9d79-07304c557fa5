from typing import Literal, Optional
from .hotels_impl import HotelDestination, HotelDateRange, HotelGuests, HotelSettings, TFSData

def create_filter(
    *,
    destination: str,
    check_in: str,
    check_out: str,
    adults: int = 1,
    children: int = 0,
    currency: str = "USD"
) -> TFSData:
    """Create a filter for Google Hotels search.
    
    Args:
        destination (str): Destination name (city, region, etc.).
        check_in (str): Check-in date in YYYY-MM-DD format.
        check_out (str): Check-out date in YYYY-MM-DD format.
        adults (int): Number of adults. Defaults to 1.
        children (int): Number of children. Defaults to 0.
        currency (str): Currency code. Defaults to "USD".
    
    Returns:
        TFSData: Filter data for Google Hotels search.
    """
    return TFSData.from_interface(
        destination=destination,
        check_in=check_in,
        check_out=check_out,
        adults=adults,
        children=children,
        currency=currency
    )
