"""
Fast, robust Google Hotels scraper (API) for Python.
"""

from .hotels_impl import HotelDestination, HotelDateRange, HotelDate, HotelGuests, HotelSettings, HotelCurrency
from .filter import create_filter
from .core import get_hotels, get_hotel_details
from .schema import Hotel, Result, Provider
from .providers import get_providers_sync, fetch_all_providers
from .zyte_client import ZyteClient

__all__ = [
    "HotelDestination",
    "HotelDateRange",
    "HotelDate",
    "HotelGuests",
    "HotelSettings",
    "HotelCurrency",
    "create_filter",
    "get_hotels",
    "get_hotel_details",
    "Result",
    "Hotel",
    "Provider",
    "get_providers_sync",
    "fetch_all_providers",
    "ZyteClient"
]
