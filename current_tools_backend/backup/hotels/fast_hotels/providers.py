import asyncio
import aiohttp
import re
import os
import logging
import requests
from base64 import b64decode
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse, urljoin

from .schema import Provider

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fast_hotels.providers')

async def fetch_booking_page(booking_link: str, session: aiohttp.ClientSession, timeout: int = 30) -> Optional[str]:
    """Fetch HTML content from a booking link.

    Args:
        booking_link (str): The booking link URL.
        session (aiohttp.ClientSession): Async HTTP session.
        timeout (int, optional): Request timeout in seconds. Defaults to 30.

    Returns:
        Optional[str]: HTML content or None if request failed.
    """
    # Check if we should use Zyte API
    use_zyte = os.environ.get('USE_ZYTE_API', 'true').lower() in ('true', '1', 'yes')

    if use_zyte:
        try:
            # Use Zyte API for the request
            zyte_api_key = os.environ.get('ZYTE_API_KEY', '864598b3f7e9432a8fcc7233b9dbe71d')

            # Make a synchronous request using requests library
            api_response = requests.post(
                "https://api.zyte.com/v1/extract",
                auth=(zyte_api_key, ""),
                json={
                    "url": booking_link,
                    "httpResponseBody": True,
                },
                timeout=timeout
            )

            if api_response.status_code == 200:
                response_data = api_response.json()
                if "httpResponseBody" in response_data:
                    http_response_body = b64decode(response_data["httpResponseBody"])
                    return http_response_body.decode('utf-8', errors='replace')
                else:
                    logger.error(f"Zyte API response missing httpResponseBody: {response_data}")
                    return None
            else:
                logger.warning(f"Zyte API request failed: {api_response.status_code}")
                # Fall back to direct request
                logger.info("Falling back to direct request")
                use_zyte = False
        except Exception as e:
            logger.error(f"Error using Zyte API: {str(e)}")
            # Fall back to direct request
            logger.info("Falling back to direct request")
            use_zyte = False

    # If not using Zyte or Zyte failed, use direct request
    if not use_zyte:
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Referer': 'https://www.google.com/',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Cache-Control': 'max-age=0'
            }

            async with session.get(booking_link, headers=headers, timeout=timeout) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.warning(f"Failed to fetch booking page: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching booking page: {str(e)}")
            return None

def extract_providers_from_html(html_content: str, debug: bool = False) -> List[Provider]:
    """Extract provider information from HTML content.

    Args:
        html_content (str): HTML content of the booking page.
        debug (bool, optional): Whether to enable debug logging. Defaults to False.

    Returns:
        List[Provider]: List of extracted providers.
    """
    providers = []

    try:
        # Look for provider containers - these are the elements in the sample HTML
        provider_patterns = [
            # Pattern for provider container
            r'<div class="ADs2Tc"[^>]*>.*?<span class="FjC1We[^"]*"[^>]*>(.*?)</span>.*?<span class="nDkDDb">(.*?)</span>.*?<a class="hUGVEe[^"]*" href="([^"]*)"',
            # Fallback pattern for more general extraction
            r'<a[^>]*href="(/travel/clk\?[^"]*)"[^>]*>.*?<span[^>]*>(.*?)</span>.*?<span[^>]*>(DZD[^<]*)</span>'
        ]

        for pattern in provider_patterns:
            matches = re.finditer(pattern, html_content, re.DOTALL)
            for match in matches:
                try:
                    if pattern == provider_patterns[0]:
                        # First pattern: name, price, link
                        name = match.group(1).strip()
                        price = match.group(2).strip()
                        link = match.group(3).strip()
                    else:
                        # Second pattern: link, name, price
                        link = match.group(1).strip()
                        name = match.group(2).strip()
                        price = match.group(3).strip()

                    # Clean up the name (remove HTML tags)
                    name = re.sub(r'<[^>]*>', '', name)

                    # Clean up the price
                    price = re.sub(r'<[^>]*>', '', price)

                    # Make sure the link is absolute
                    if link.startswith('/'):
                        link = f"https://www.google.com{link}"

                    # Extract the actual destination URL
                    pcurl_match = re.search(r'pcurl=([^&]*)', link)
                    if pcurl_match:
                        try:
                            # URL-decode the pcurl parameter to get the actual provider URL
                            from urllib.parse import unquote
                            actual_url = unquote(pcurl_match.group(1))
                            link = actual_url
                        except Exception as e:
                            if debug:
                                logger.warning(f"Error extracting actual URL: {str(e)}")

                    # Only add if we have all required fields
                    if name and price and link:
                        # Check if this provider already exists (avoid duplicates)
                        if not any(p.name == name for p in providers):
                            providers.append(Provider(
                                name=name,
                                price=price,
                                link=link
                            ))
                except Exception as e:
                    if debug:
                        logger.warning(f"Error processing provider match: {str(e)}")

        # If we still couldn't find providers, try a more aggressive approach
        if not providers:
            # Look for any link with a provider name
            provider_names = ["Booking.com", "Expedia", "Hotels.com", "Agoda", "Trip.com",
                             "Priceline", "Travelocity", "Orbitz", "Kayak", "Hotwire"]

            for name in provider_names:
                name_pattern = re.escape(name)
                matches = re.finditer(f'<a[^>]*href="([^"]*)"[^>]*>.*?{name_pattern}.*?</a>.*?<span[^>]*>(DZD[^<]*)</span>',
                                    html_content, re.DOTALL)
                for match in matches:
                    link = match.group(1).strip()
                    price = match.group(2).strip()

                    # Make sure the link is absolute
                    if link.startswith('/'):
                        link = f"https://www.google.com{link}"

                    # Only add if we have all required fields
                    if name and price and link:
                        # Check if this provider already exists (avoid duplicates)
                        if not any(p.name == name for p in providers):
                            providers.append(Provider(
                                name=name,
                                price=price,
                                link=link
                            ))
    except Exception as e:
        if debug:
            logger.error(f"Error extracting providers: {str(e)}")

    return providers

async def fetch_all_providers(booking_links: List[str], debug: bool = False) -> Dict[str, List[Provider]]:
    """Fetch provider information for multiple booking links in parallel.

    Args:
        booking_links (List[str]): List of booking links.
        debug (bool, optional): Whether to enable debug logging. Defaults to False.

    Returns:
        Dict[str, List[Provider]]: Dictionary mapping booking links to their providers.
    """
    results = {}

    if not booking_links:
        return results

    if debug:
        logger.info(f"Fetching providers for {len(booking_links)} booking links in parallel")

    async with aiohttp.ClientSession() as session:
        # Create tasks for all links at once
        tasks = {}
        for link in booking_links:
            if link:  # Skip None or empty links
                tasks[link] = asyncio.create_task(fetch_booking_page(link, session))

        # Wait for all tasks to complete simultaneously
        await asyncio.gather(*tasks.values(), return_exceptions=True)

        # Process all completed tasks
        for link, task in tasks.items():
            try:
                if task.exception():
                    if debug:
                        logger.error(f"Error fetching {link}: {task.exception()}")
                    results[link] = []
                    continue

                html_content = task.result()
                if html_content:
                    providers = extract_providers_from_html(html_content, debug)
                    results[link] = providers
                    if debug:
                        logger.info(f"Found {len(providers)} providers for {link}")
                else:
                    results[link] = []
            except Exception as e:
                if debug:
                    logger.error(f"Error processing {link}: {str(e)}")
                results[link] = []

    if debug:
        total_providers = sum(len(providers) for providers in results.values())
        logger.info(f"Completed fetching all providers. Found {total_providers} providers across {len(results)} links")

    return results

def get_providers_sync(booking_links: List[str], debug: bool = False) -> Dict[str, List[Provider]]:
    """Synchronous wrapper for fetch_all_providers.

    Args:
        booking_links (List[str]): List of booking links.
        debug (bool, optional): Whether to enable debug logging. Defaults to False.

    Returns:
        Dict[str, List[Provider]]: Dictionary mapping booking links to their providers.
    """
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(fetch_all_providers(booking_links, debug))