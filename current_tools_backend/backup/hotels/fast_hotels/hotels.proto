syntax = "proto3";

message HotelLocation {
  string name = 7;
}

message HotelDestination {
  HotelLocation location = 2;
}

message HotelDate {
  int32 year = 1;
  int32 month = 2;
  int32 day = 3;
}

message HotelDateRange {
  HotelDate start = 1;
  HotelDate end = 2;
}

message HotelDateSettings {
  HotelDateRange dates = 2;
}

message HotelSearch {
  HotelDestination destination = 1;
  HotelDateSettings settings = 2;
}

message HotelCurrency {
  string code = 7;
}

message HotelSettings {
  HotelCurrency currency = 1;
}

message HotelInfo {
  HotelSearch search = 3;
  HotelSettings settings = 5;
}

enum HotelRating {
  UNKNOWN_RATING = 0;
  ONE_STAR = 1;
  TWO_STAR = 2;
  THREE_STAR = 3;
  FOUR_STAR = 4;
  FIVE_STAR = 5;
}

enum HotelGuests {
  UNKNOWN_GUEST = 0;
  ADULT = 1;
  CHILD = 2;
}

message HotelGuestInfo {
  repeated HotelGuests guests = 1;
}
