"""Typed implementation of hotels_pb2.py"""
import base64
from typing import Any, List, Optional, TYPE_CHECKING, Literal, Union, Dict
from datetime import date
from . import hotels_pb2 as PB

if TYPE_CHECKING:
    PB: Any

class HotelDate:
    """Represents a date for hotel search.
    
    Args:
        year (int): Year.
        month (int): Month.
        day (int): Day.
    """
    __slots__ = ("year", "month", "day")
    year: int
    month: int
    day: int
    
    def __init__(self, *, year: int, month: int, day: int):
        self.year = year
        self.month = month
        self.day = day
    
    @classmethod
    def from_date(cls, date_obj: date) -> 'HotelDate':
        """Create a HotelDate from a datetime.date object."""
        return cls(
            year=date_obj.year,
            month=date_obj.month,
            day=date_obj.day
        )
    
    @classmethod
    def from_string(cls, date_str: str) -> 'HotelDate':
        """Create a HotelDate from a string in YYYY-MM-DD format."""
        year, month, day = map(int, date_str.split('-'))
        return cls(year=year, month=month, day=day)
    
    def attach(self, pb_date: PB.HotelDate) -> None:
        """Attach this date to a protobuf HotelDate object."""
        pb_date.year = self.year
        pb_date.month = self.month
        pb_date.day = self.day
    
    def __repr__(self) -> str:
        return f"HotelDate(year={self.year}, month={self.month}, day={self.day})"

class HotelDateRange:
    """Represents a date range for hotel search.
    
    Args:
        check_in (HotelDate): Check-in date.
        check_out (HotelDate): Check-out date.
    """
    __slots__ = ("check_in", "check_out")
    check_in: HotelDate
    check_out: HotelDate
    
    def __init__(self, *, check_in: HotelDate, check_out: HotelDate):
        self.check_in = check_in
        self.check_out = check_out
    
    @classmethod
    def from_string_dates(cls, check_in: str, check_out: str) -> 'HotelDateRange':
        """Create a HotelDateRange from string dates in YYYY-MM-DD format."""
        return cls(
            check_in=HotelDate.from_string(check_in),
            check_out=HotelDate.from_string(check_out)
        )
    
    def attach(self, date_range: PB.HotelDateRange) -> None:
        """Attach this date range to a protobuf HotelDateRange object."""
        self.check_in.attach(date_range.start)
        self.check_out.attach(date_range.end)
    
    def __repr__(self) -> str:
        return f"HotelDateRange(check_in={self.check_in!r}, check_out={self.check_out!r})"

class HotelLocation:
    """Represents a hotel location.
    
    Args:
        name (str): Location name.
    """
    __slots__ = ("name",)
    name: str
    
    def __init__(self, *, name: str):
        self.name = name
    
    def attach(self, location: PB.HotelLocation) -> None:
        """Attach this location to a protobuf HotelLocation object."""
        location.name = self.name
    
    def __repr__(self) -> str:
        return f"HotelLocation(name={self.name!r})"

class HotelDestination:
    """Represents a hotel destination.
    
    Args:
        location (HotelLocation): Location.
    """
    __slots__ = ("location",)
    location: HotelLocation
    
    def __init__(self, *, location: HotelLocation):
        self.location = location
    
    @classmethod
    def from_location_name(cls, location_name: str) -> 'HotelDestination':
        """Create a HotelDestination from a location name."""
        return cls(location=HotelLocation(name=location_name))
    
    def attach(self, destination: PB.HotelDestination) -> None:
        """Attach this destination to a protobuf HotelDestination object."""
        self.location.attach(destination.location)
    
    def __repr__(self) -> str:
        return f"HotelDestination(location={self.location!r})"

class HotelGuests:
    """Represents hotel guests.
    
    Args:
        adults (int): Number of adults.
        children (int): Number of children.
    """
    __slots__ = ("pb",)
    pb: List[int]
    
    def __init__(self, *, adults: int = 1, children: int = 0):
        assert sum((adults, children)) <= 9, "Too many guests (> 9)"
        self.pb = []
        self.pb += [PB.HotelGuests.ADULT for _ in range(adults)]
        self.pb += [PB.HotelGuests.CHILD for _ in range(children)]
    
    def attach(self, guest_info: PB.HotelGuestInfo) -> None:
        """Attach these guests to a protobuf HotelGuestInfo object."""
        for g in self.pb:
            guest_info.guests.append(g)
    
    def __repr__(self) -> str:
        adults = sum(1 for g in self.pb if g == PB.HotelGuests.ADULT)
        children = sum(1 for g in self.pb if g == PB.HotelGuests.CHILD)
        return f"HotelGuests(adults={adults}, children={children})"

class HotelCurrency:
    """Represents a currency for hotel prices.
    
    Args:
        code (str): Currency code (e.g., "USD").
    """
    __slots__ = ("code",)
    code: str
    
    def __init__(self, *, code: str = "USD"):
        self.code = code
    
    def attach(self, currency: PB.HotelCurrency) -> None:
        """Attach this currency to a protobuf HotelCurrency object."""
        currency.code = self.code
    
    def __repr__(self) -> str:
        return f"HotelCurrency(code={self.code!r})"

class HotelSettings:
    """Represents hotel search settings.
    
    Args:
        currency (HotelCurrency): Currency.
    """
    __slots__ = ("currency",)
    currency: HotelCurrency
    
    def __init__(self, *, currency: HotelCurrency = None):
        self.currency = currency or HotelCurrency(code="USD")
    
    def attach(self, settings: PB.HotelSettings) -> None:
        """Attach these settings to a protobuf HotelSettings object."""
        self.currency.attach(settings.currency)
    
    def __repr__(self) -> str:
        return f"HotelSettings(currency={self.currency!r})"

class TFSData:
    """?tfs= data for Google Hotels. (internal)
    
    Use TFSData.from_interface instead.
    """
    def __init__(
        self, 
        *, 
        destination: HotelDestination,
        date_range: HotelDateRange,
        guests: HotelGuests = None,
        settings: HotelSettings = None
    ):
        self.destination = destination
        self.date_range = date_range
        self.guests = guests or HotelGuests()
        self.settings = settings or HotelSettings()
    
    def pb(self) -> PB.HotelInfo:
        """Create a protobuf HotelInfo object."""
        info = PB.HotelInfo()
        
        # Set up search
        search = info.search
        self.destination.attach(search.destination)
        
        # Set up date settings
        date_settings = search.settings
        self.date_range.attach(date_settings.dates)
        
        # Set up settings
        self.settings.attach(info.settings)
        
        return info
    
    def to_string(self) -> bytes:
        """Serialize to protobuf string."""
        return self.pb().SerializeToString()
    
    def as_b64(self) -> bytes:
        """Encode as base64."""
        return base64.b64encode(self.to_string())
    
    @staticmethod
    def from_interface(
        *,
        destination: str,
        check_in: str,
        check_out: str,
        adults: int = 1,
        children: int = 0,
        currency: str = "USD"
    ) -> 'TFSData':
        """Create TFSData from interface parameters.
        
        Args:
            destination (str): Destination name.
            check_in (str): Check-in date in YYYY-MM-DD format.
            check_out (str): Check-out date in YYYY-MM-DD format.
            adults (int): Number of adults.
            children (int): Number of children.
            currency (str): Currency code.
        """
        return TFSData(
            destination=HotelDestination.from_location_name(destination),
            date_range=HotelDateRange.from_string_dates(check_in, check_out),
            guests=HotelGuests(adults=adults, children=children),
            settings=HotelSettings(currency=HotelCurrency(code=currency))
        )
    
    def __repr__(self) -> str:
        return (
            f"TFSData(destination={self.destination!r}, "
            f"date_range={self.date_range!r}, "
            f"guests={self.guests!r}, "
            f"settings={self.settings!r})"
        )
