from setuptools import setup, find_packages

setup(
    name="fast_hotels",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "protobuf>=3.19.0,<=3.20.0",
        "selectolax>=0.3.0",
        "requests>=2.25.0",
    ],
    python_requires=">=3.7",
    description="Fast, robust Google Hotels scraper (API) for Python",
    author="Converted from AWeirdDev/flights",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)
