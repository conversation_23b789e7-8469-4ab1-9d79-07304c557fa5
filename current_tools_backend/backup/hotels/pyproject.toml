[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "fast_hotels"
version = "0.1.0"
description = "Fast, robust Google Hotels scraper (API) for Python"
readme = "README.md"
requires-python = ">=3.7"
license = {text = "MIT"}
authors = [
    {name = "Converted from AWeirdDev/flights"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
]
dependencies = [
    "protobuf>=3.19.0,<=3.20.0",
    "selectolax>=0.3.0",
    "requests>=2.25.0",
]

[project.urls]
"Homepage" = "https://github.com/yourusername/hotels"
"Bug Tracker" = "https://github.com/yourusername/hotels/issues"
