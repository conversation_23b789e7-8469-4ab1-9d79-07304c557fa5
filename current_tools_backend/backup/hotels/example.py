import argparse
import json
from fast_hotels import get_hotels, get_providers_sync

def main():
    # Argument parser for command-line input
    parser = argparse.ArgumentParser(description="Hotel Search Example")
    parser.add_argument('--destination', required=True, help="Destination (city, region, etc.)")
    parser.add_argument('--check_in', required=True, help="Check-in date (YYYY-MM-DD)")
    parser.add_argument('--check_out', required=True, help="Check-out date (YYYY-MM-DD)")
    parser.add_argument('--adults', type=int, default=1, help="Number of adults")
    parser.add_argument('--children', type=int, default=0, help="Number of children")
    parser.add_argument('--currency', type=str, default="USD", help="Currency code (e.g., USD, EUR)")
    parser.add_argument('--debug', action='store_true', help="Enable debug mode")
    parser.add_argument('--providers', action='store_true', help="Fetch provider details for each hotel")
    args = parser.parse_args()

    # Get hotels
    try:
        result = get_hotels(
            destination=args.destination,
            check_in=args.check_in,
            check_out=args.check_out,
            adults=args.adults,
            children=args.children,
            currency=args.currency,
            debug=args.debug,
            output_format="dict"  # Get result as a dictionary for easier handling
        )
        
        # Fetch provider details if requested
        if args.providers:
            print("Fetching provider details (this may take a moment)...")
            
            # Collect booking links from all hotels
            booking_links = []
            hotel_indices = {}
            
            for i, hotel in enumerate(result['hotels']):
                if 'booking_link' in hotel and hotel['booking_link']:
                    booking_links.append(hotel['booking_link'])
                    hotel_indices[hotel['booking_link']] = i
            
            # Fetch provider details for all hotels in parallel
            providers_by_link = get_providers_sync(booking_links, args.debug)
            
            # Add provider details to the result
            for link, providers in providers_by_link.items():
                if link in hotel_indices and providers:
                    hotel_index = hotel_indices[link]
                    result['hotels'][hotel_index]['providers'] = [
                        {
                            'name': provider.name,
                            'price': provider.price,
                            'link': provider.link
                        }
                        for provider in providers
                    ]
        
        # Print results
        print(json.dumps(result, indent=2))
        
        # Print summary
        print(f"\nFound {len(result['hotels'])} hotels in {args.destination}")
        
        # Print first few hotels
        if result['hotels']:
            print("\nTop hotels:")
            for i, hotel in enumerate(result['hotels'][:3]):  # Show top 3
                print(f"{i+1}. {hotel['name']} - {hotel['price']}")
                print(f"   Rating: {hotel['rating']} stars")
                if 'booking_link' in hotel:
                    print(f"   Booking: {hotel['booking_link']}")
                
                # Show providers if available
                if 'providers' in hotel and hotel['providers']:
                    print(f"   Providers:")
                    for provider in hotel['providers'][:3]:  # Show up to 3 providers
                        print(f"     - {provider['name']}: {provider['price']}")
                        print(f"       {provider['link']}")
                
                print()
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    import time
    start_time = time.time()
    main()
    end_time = time.time()
    print(f"Time taken: {end_time - start_time} seconds")
