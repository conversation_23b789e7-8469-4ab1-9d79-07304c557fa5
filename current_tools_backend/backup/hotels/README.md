# Fast Hotels API

A FastAPI application that provides endpoints to search for hotels, get hotel details, and fetch booking providers.

## Installation

1. Install the required packages:

```bash
pip install fastapi uvicorn fast_hotels
```

## Configuration

### Environment Variables

The API can be configured using the following environment variables:

- `ZYTE_API_KEY`: Your Zyte API key for making proxied requests (default: a test key)
- `USE_ZYTE_API`: Whether to use Zyte API for requests (default: "true")

### Using Zyte API

The API uses Zyte's proxy service to avoid IP blocking when making requests to hotel booking sites. This is especially important when deploying to production servers where the IP might be recognized as a bot.

To configure Zyte API:

1. Sign up for a Zyte account at https://www.zyte.com/
2. Get your API key from the Zyte dashboard
3. Set the `ZYTE_API_KEY` environment variable:

```bash
export ZYTE_API_KEY="your_zyte_api_key_here"
```

## Usage

### Running the API

```bash
uvicorn api:app --reload
```

The API will be available at http://localhost:8000

### API Documentation

Interactive API documentation is available at http://localhost:8000/docs

## Endpoints

### 1. Root

```
GET /
```

Returns basic information about the API.

### 2. Search Hotels

```
GET /hotels/search
```

**Query Parameters:**
- `destination` (required): Destination (city, region, etc.)
- `check_in` (required): Check-in date (YYYY-MM-DD)
- `check_out` (required): Check-out date (YYYY-MM-DD)
- `adults` (default: 1): Number of adults
- `children` (default: 0): Number of children
- `currency` (default: "USD"): Currency code (e.g., USD, EUR)
- `include_providers` (default: false): Include provider details for each hotel
- `debug` (default: false): Enable debug mode

**Example:**
```
GET /hotels/search?destination=New%20York&check_in=2023-11-01&check_out=2023-11-05&adults=2&currency=USD
```

### 3. Hotel Details

```
GET /hotels/details/{hotel_id}
```

**Path Parameters:**
- `hotel_id` (required): Hotel ID

**Query Parameters:**
- `check_in` (required): Check-in date (YYYY-MM-DD)
- `check_out` (required): Check-out date (YYYY-MM-DD)
- `adults` (default: 1): Number of adults
- `children` (default: 0): Number of children
- `currency` (default: "USD"): Currency code (e.g., USD, EUR)
- `debug` (default: false): Enable debug mode

**Example:**
```
GET /hotels/details/123456?check_in=2023-11-01&check_out=2023-11-05&adults=2&currency=USD
```

### 4. Hotel Providers

```
GET /hotels/providers
```

**Query Parameters:**
- `booking_link` (required): Hotel booking link
- `debug` (default: false): Enable debug mode

**Example:**
```
GET /hotels/providers?booking_link=https://www.google.com/travel/search?...
```

## Response Format

All endpoints return JSON responses. Errors are returned with appropriate HTTP status codes and error details.
