[build-system]
requires = ["flit_core >=3.2,<4"]
build-backend = "flit_core.buildapi"

[project]
name = "fast-flights"
version = "2.2"
description = "The fast, robust, strongly-typed Google Flights scraper (API) implemented in Python."
keywords = ["flights", "google", "google-flights", "scraper", "protobuf", "travel", "trip", "passengers", "airport"]
authors = [
  { name = "AWeirdDev", email = "<EMAIL>" },
]
license = { file = "LICENSE" }
readme = "README.md"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
requires-python = ">=3.8"
dependencies = [
  "primp",
  "protobuf",
  "selectolax"
]

[project.optional-dependencies]
local = [
    "playwright"
]

[project.urls]
"Source" = "https://github.com/AWeirdDev/flights"
"Issues" = "https://github.com/AWeirdDev/flights/issues"
"Documentation" = "https://aweirddev.github.io/flights/"

[tool.setuptools]
packages = [
    "fast_flights"
]
