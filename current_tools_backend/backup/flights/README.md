# Fast Flights API

A FastAPI application that provides endpoints to search for flights based on various criteria.

## Installation

1. Install the required packages:

```bash
pip install fastapi uvicorn requests fast_flights
```

## Configuration

### Environment Variables

The API can be configured using the following environment variables:

- `ZYTE_API_KEY`: Your Zyte API key for making proxied requests (default: a test key)
- `USE_ZYTE_API`: Whether to use Zyte API for requests (default: "true")

### Using Zyte API

The API uses Zyte's proxy service to avoid IP blocking when making requests to flight booking sites. This is especially important when deploying to production servers where the IP might be recognized as a bot.

To configure Zyte API:

1. Sign up for a Zyte account at https://www.zyte.com/
2. Get your API key from the Zyte dashboard
3. Set the `ZYTE_API_KEY` environment variable:

```bash
export ZYTE_API_KEY="your_zyte_api_key_here"
```

## Usage

### Running the API

Start the FastAPI server with:

```bash
cd flights
uvicorn api:app --reload --port 8002
```

The API will be available at http://localhost:8002

### API Documentation

Interactive API documentation is available at http://localhost:8002/docs

## Endpoints

### 1. Root

```
GET /
```

Returns basic information about the API.

### 2. Search Flights

```
GET /flights/search
```

**Required Parameters:**
- `from_airport`: Departure airport code (e.g., 'JFK')
- `to_airport`: Arrival airport code (e.g., 'LAX')
- `date`: Date of departure (YYYY-MM-DD)

**Optional Parameters:**
- `return_date`: Date of return for round trips (YYYY-MM-DD)
- `trip`: Trip type: 'one-way' (default) or 'round-trip'
- `adults`: Number of adult passengers (default: 1)
- `children`: Number of child passengers (default: 0)
- `infants_in_seat`: Number of infants with seats (default: 0)
- `infants_on_lap`: Number of infants on lap (default: 0)
- `seat`: Seat type: 'economy', 'premium_economy', 'business', 'first' (default: 'economy')
- `max_stops`: Maximum number of stops (default: 5)
- `max_results`: Maximum number of results to return (default: 50)
- `debug`: Enable debug mode (default: false)

**Example:**
```
GET /flights/search?from_airport=JFK&to_airport=LAX&date=2025-07-01&trip=one-way&adults=1&seat=economy
```

## Testing the API

A test script is provided to test the API functionality.

### Running the Tests

To run all tests:

```bash
python test_api.py
```

To run specific tests:

```bash
python test_api.py --test root
python test_api.py --test oneway
python test_api.py --test roundtrip
```

To test against a different URL:

```bash
python test_api.py --url http://localhost:5000
```

## Response Format

The flight search endpoint returns a JSON response with the following structure:

```json
{
  "current_price": "string",
  "flights": [
    {
      "is_best": true/false,
      "name": "string",
      "departure": "string",
      "arrival": "string",
      "arrival_time_ahead": "string",
      "duration": "string",
      "stops": "string",
      "delay": "string",
      "price": "string"
    }
  ]
}
```

## Example Implementation

For an example of how to use the Fast Flights package directly, see the `test.py` file.
