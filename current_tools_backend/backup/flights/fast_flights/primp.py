import os
import requests
import logging
from .zyte_client import ZyteClient

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fast_flights.primp')

class Response:
    """Response class that mimics the primp Response interface."""
    def __init__(self, response):
        self.response = response

        # Handle both regular requests Response and our custom Zyte response dict
        if hasattr(response, 'status_code'):
            self.status_code = response.status_code
            self.text = response.text
            self.text_markdown = response.text
        else:
            # This is a Zyte API response dict
            self.status_code = response.get('status_code', 500)
            self.text = response.get('text', '')
            self.text_markdown = self.text

class Client:
    """Client class that mimics the primp Client interface."""
    def __init__(self, impersonate=None, verify=True):
        self.impersonate = impersonate
        self.verify = verify

        # Check if we should use Zyte API
        self.use_zyte = os.environ.get('USE_ZYTE_API', 'true').lower() in ('true', '1', 'yes')

        if self.use_zyte:
            logger.info("Using Zyte API for requests")
            self.zyte_client = ZyteClient()
        else:
            logger.info("Using direct requests (no Zyte API)")
            self.session = requests.Session()

            # Set headers based on impersonation
            if impersonate == "chrome_126":
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                })
            elif impersonate == "chrome_100":
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                })
            else:
                # Default headers
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36',
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
                })

    def get(self, url, params=None):
        """Make a GET request to the specified URL.

        Args:
            url (str): URL to request.
            params (dict, optional): Query parameters. Defaults to None.

        Returns:
            Response: Response object.
        """
        if self.use_zyte:
            try:
                zyte_response = self.zyte_client.get(url, params)
                return Response(zyte_response)
            except Exception as e:
                logger.error(f"Zyte API request failed, falling back to direct request: {str(e)}")
                # Fall back to direct request if Zyte fails
                self.use_zyte = False
                self.session = requests.Session()
                response = self.session.get(url, params=params, verify=self.verify)
                return Response(response)
        else:
            response = self.session.get(url, params=params, verify=self.verify)
            return Response(response)

    def post(self, url, data=None, json=None):
        """Make a POST request to the specified URL.

        Args:
            url (str): URL to request.
            data (dict, optional): Form data. Defaults to None.
            json (dict, optional): JSON data. Defaults to None.

        Returns:
            Response: Response object.
        """
        if self.use_zyte and url != "https://try.playwright.tech/service/control/run":
            # Use Zyte API for POST requests except for the playwright service
            try:
                zyte_response = self.zyte_client.post(url, data, json)
                return Response(zyte_response)
            except Exception as e:
                logger.error(f"Zyte API POST request failed, falling back to direct request: {str(e)}")
                # Fall back to direct request if Zyte fails
                self.use_zyte = False
                self.session = requests.Session()
                response = self.session.post(url, data=data, json=json, verify=self.verify)
                return Response(response)
        else:
            # For the playwright service or if Zyte is disabled, use direct request
            response = self.session.post(url, data=data, json=json, verify=self.verify)
            return Response(response)

# Export the classes
__all__ = ["Client", "Response"]
