"""
Zyte API client for making requests through Zyte's proxy service.
"""

import os
import logging
import requests
from base64 import b64decode
from typing import Optional, Dict, Any, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fast_flights.zyte_client')

class ZyteClient:
    """Client for making requests through Zyte API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Zyte client.
        
        Args:
            api_key (Optional[str]): Zyte API key. If None, will try to get from environment.
        """
        self.api_key = api_key or os.environ.get('ZYTE_API_KEY', '864598b3f7e9432a8fcc7233b9dbe71d')
        self.extract_url = "https://api.zyte.com/v1/extract"
        
    def get(self, url: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make a GET request through Zyte API.
        
        Args:
            url (str): URL to request.
            params (Optional[Dict[str, Any]]): Query parameters to include in the URL.
            
        Returns:
            Dict[str, Any]: Response data from Zyte API.
        """
        # If params are provided, append them to the URL
        if params:
            from urllib.parse import urlencode
            url_params = urlencode(params)
            if '?' in url:
                url = f"{url}&{url_params}"
            else:
                url = f"{url}?{url_params}"
        
        logger.info(f"Making Zyte API request to: {url}")
        
        try:
            api_response = requests.post(
                self.extract_url,
                auth=(self.api_key, ""),
                json={
                    "url": url,
                    "httpResponseBody": True,
                },
            )
            
            # Check if the request was successful
            api_response.raise_for_status()
            
            # Parse the response
            response_data = api_response.json()
            
            # Decode the HTTP response body
            if "httpResponseBody" in response_data:
                http_response_body = b64decode(response_data["httpResponseBody"])
                return {
                    "status_code": 200,
                    "content": http_response_body,
                    "text": http_response_body.decode('utf-8', errors='replace'),
                    "raw_response": response_data
                }
            else:
                logger.error(f"Zyte API response missing httpResponseBody: {response_data}")
                return {
                    "status_code": 500,
                    "content": b"",
                    "text": "",
                    "raw_response": response_data,
                    "error": "Missing httpResponseBody in Zyte API response"
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Zyte API request failed: {str(e)}")
            return {
                "status_code": 500,
                "content": b"",
                "text": "",
                "error": str(e)
            }
    
    def post(self, url: str, data: Optional[Dict[str, Any]] = None, json: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Make a POST request through Zyte API.
        
        Args:
            url (str): URL to request.
            data (Optional[Dict[str, Any]]): Form data to include in the request.
            json (Optional[Dict[str, Any]]): JSON data to include in the request.
            
        Returns:
            Dict[str, Any]: Response data from Zyte API.
        """
        logger.info(f"Making Zyte API POST request to: {url}")
        
        try:
            # For POST requests, we need to include the form data or JSON data in the request
            zyte_request = {
                "url": url,
                "httpResponseBody": True,
                "httpRequestMethod": "POST",
            }
            
            # Add form data or JSON data if provided
            if data:
                zyte_request["httpRequestFormData"] = data
            if json:
                zyte_request["httpRequestBody"] = str(json)
                zyte_request["httpRequestHeaders"] = {"Content-Type": "application/json"}
            
            api_response = requests.post(
                self.extract_url,
                auth=(self.api_key, ""),
                json=zyte_request,
            )
            
            # Check if the request was successful
            api_response.raise_for_status()
            
            # Parse the response
            response_data = api_response.json()
            
            # Decode the HTTP response body
            if "httpResponseBody" in response_data:
                http_response_body = b64decode(response_data["httpResponseBody"])
                return {
                    "status_code": 200,
                    "content": http_response_body,
                    "text": http_response_body.decode('utf-8', errors='replace'),
                    "raw_response": response_data
                }
            else:
                logger.error(f"Zyte API response missing httpResponseBody: {response_data}")
                return {
                    "status_code": 500,
                    "content": b"",
                    "text": "",
                    "raw_response": response_data,
                    "error": "Missing httpResponseBody in Zyte API response"
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Zyte API request failed: {str(e)}")
            return {
                "status_code": 500,
                "content": b"",
                "text": "",
                "error": str(e)
            }
    
    def save_debug_response(self, response_data: Dict[str, Any], filename: str = "zyte_debug_response.html") -> None:
        """Save the response data to a file for debugging.
        
        Args:
            response_data (Dict[str, Any]): Response data from Zyte API.
            filename (str): Filename to save the response to.
        """
        if "text" in response_data and response_data["text"]:
            with open(filename, "w", encoding="utf-8") as f:
                f.write(response_data["text"])
            logger.info(f"Saved Zyte API response to {filename}")
