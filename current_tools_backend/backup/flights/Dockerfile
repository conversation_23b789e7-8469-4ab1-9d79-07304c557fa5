FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first to leverage Docker cache
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Install additional dependencies
RUN pip install --no-cache-dir selectolax protobuf

# Set PYTHONPATH to include the current directory
ENV PYTHONPATH="${PYTHONPATH}:/app"

# Set Zyte API configuration (can be overridden at runtime)
ENV ZYTE_API_KEY="864598b3f7e9432a8fcc7233b9dbe71d"
ENV USE_ZYTE_API="true"

# Expose the port
EXPOSE 8002

# Create a non-root user and switch to it
RUN useradd -m appuser
USER appuser

# Command to run the application
CMD ["uvicorn", "api:app", "--host", "0.0.0.0", "--port", "8002"]
