import json
from typing import Optional, Dict, Any, List, Union
from fastapi import FastAP<PERSON>, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from fast_flights import (
    create_filter, get_flights_from_filter,
    FlightData, Passengers
)

app = FastAPI(
    title="Fast Flights API",
    description="API for flight search",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Helper function to convert flight objects to dictionaries
def flight_to_dict(flight):
    return {
        "is_best": getattr(flight, 'is_best', None),
        "name": getattr(flight, 'name', None),
        "departure": getattr(flight, 'departure', None),
        "arrival": getattr(flight, 'arrival', None),
        "arrival_time_ahead": getattr(flight, 'arrival_time_ahead', None),
        "duration": getattr(flight, 'duration', None),
        "stops": getattr(flight, 'stops', None),
        "delay": getattr(flight, 'delay', None),
        "price": getattr(flight, 'price', None),
    }

# Helper function to convert result objects to dictionaries
def result_to_dict(result):
    return {
        "current_price": getattr(result, 'current_price', None),
        "flights": [flight_to_dict(flight) for flight in getattr(result, 'flights', [])]
    }

@app.get("/")
async def root():
    return {
        "message": "Fast Flights API is running",
        "docs": "/docs",
        "endpoints": [
            "/flights/search",
        ],
        "payload_formats": {
            "/flights/search": {
                "method": "GET",
                "query_parameters": {
                    "from_airport": "string (required) - Departure airport code (e.g., 'JFK')",
                    "to_airport": "string (required) - Arrival airport code (e.g., 'LAX')",
                    "date": "string (required) - Date of departure (YYYY-MM-DD)",
                    "return_date": "string (optional) - Date of return for round trips (YYYY-MM-DD)",
                    "trip": "string (optional) - Trip type: 'one-way' or 'round-trip' (default: 'one-way')",
                    "adults": "integer (optional) - Number of adult passengers (default: 1)",
                    "children": "integer (optional) - Number of child passengers (default: 0)",
                    "infants_in_seat": "integer (optional) - Number of infants with seats (default: 0)",
                    "infants_on_lap": "integer (optional) - Number of infants on lap (default: 0)",
                    "seat": "string (optional) - Seat type: 'economy', 'premium_economy', 'business', 'first' (default: 'economy')",
                    "max_stops": "integer (optional) - Maximum number of stops (default: 5)",
                    "max_results": "integer (optional) - Maximum number of results to return (default: 50)",
                    "debug": "boolean (optional) - Enable debug mode (default: false)"
                },
                "example": "/flights/search?from_airport=JFK&to_airport=LAX&date=2025-07-01&trip=round-trip&return_date=2025-07-10&adults=2&seat=economy"
            }
        }
    }

@app.get("/flights/search")
async def search_flights(
    from_airport: str = Query(..., description="Departure airport code (e.g., 'JFK')"),
    to_airport: str = Query(..., description="Arrival airport code (e.g., 'LAX')"),
    date: str = Query(..., description="Date of departure (YYYY-MM-DD)"),
    return_date: Optional[str] = Query(None, description="Date of return for round trips (YYYY-MM-DD)"),
    trip: str = Query("one-way", description="Trip type: 'one-way' or 'round-trip'"),
    adults: int = Query(1, description="Number of adult passengers"),
    children: int = Query(0, description="Number of child passengers"),
    infants_in_seat: int = Query(0, description="Number of infants with seats"),
    infants_on_lap: int = Query(0, description="Number of infants on lap"),
    seat: str = Query("economy", description="Seat type: 'economy', 'premium_economy', 'business', 'first'"),
    max_stops: int = Query(5, description="Maximum number of stops"),
    max_results: int = Query(50, description="Maximum number of results to return"),
    debug: bool = Query(False, description="Enable debug mode")
):
    try:
        # Create flight data
        flight_data_list = [
            FlightData(
                date=date,
                from_airport=from_airport,
                to_airport=to_airport
            )
        ]

        # Add return flight if it's a round trip
        if trip.lower() == "round-trip" and return_date:
            flight_data_list.append(
                FlightData(
                    date=return_date,
                    from_airport=to_airport,
                    to_airport=from_airport
                )
            )

        # Create passengers object
        passengers = Passengers(
            adults=adults,
            children=children,
            infants_in_seat=infants_in_seat,
            infants_on_lap=infants_on_lap
        )

        # Create filter
        filter = create_filter(
            flight_data=flight_data_list,
            trip=trip.lower(),
            passengers=passengers,
            seat=seat.lower(),
            max_stops=max_stops
        )

        if debug:
            print(f"Filter: {filter.as_b64().decode('utf-8')}")

        # Get flights
        result = get_flights_from_filter(filter, mode="common")

        # Convert to dictionary
        result_dict = result_to_dict(result)

        # Limit the number of flights returned
        if max_results and len(result_dict['flights']) > max_results:
            result_dict['flights'] = result_dict['flights'][:max_results]

        return JSONResponse(content=result_dict)

    except Exception as e:
        return HTTPException(status_code=500, detail=str(e))

# Run with: uvicorn api:app --reload
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)