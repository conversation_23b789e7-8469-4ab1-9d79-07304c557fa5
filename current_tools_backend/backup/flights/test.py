from pprint import pprint
from fast_flights import create_filter, get_flights_from_filter, FlightData, Passengers
import time



def flight_to_dict(flight):
    return {
        "is_best": getattr(flight, 'is_best', None),
        "name": getattr(flight, 'name', None),
        "departure": getattr(flight, 'departure', None),
        "arrival": getattr(flight, 'arrival', None),
        "arrival_time_ahead": getattr(flight, 'arrival_time_ahead', None),
        "duration": getattr(flight, 'duration', None),
        "stops": getattr(flight, 'stops', None),
        "delay": getattr(flight, 'delay', None),
        "price": getattr(flight, 'price', None),
    }

def result_to_dict(result):
    return {
        "current_price": getattr(result, 'current_price', None),
        "flights": [flight_to_dict(flight) for flight in getattr(result, 'flights', [])]
    }


start_time = time.time()
filter = create_filter(
    flight_data=[
        # Include more if it's not a one-way trip
        FlightData(
            date="2025-07-01",  # Date of departure
            from_airport="ALG",  # Departure (airport)
            to_airport="LAX",  # Arrival (airport)
        )
    ],
    trip="one-way",  # Trip type
    passengers=Passengers(adults=1, children=0, infants_in_seat=0, infants_on_lap=0),  # Passengers
    seat="economy",  # Seat type
    max_stops=5,  # Maximum number of stops
)
pprint(filter.as_b64().decode("utf-8"))
x = get_flights_from_filter(filter, mode="common")
pprint(x)
end_time = time.time()
print(f"Time taken: {end_time - start_time} seconds")

pprint(result_to_dict(x)['flights'][:50])