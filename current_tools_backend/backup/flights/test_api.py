import json
import argparse
import requests
from datetime import datetime, timedelta
from pprint import pprint


def test_root_endpoint(base_url):
    """Test the root endpoint."""
    print("\n=== TESTING ROOT ENDPOINT ===\n")
    
    try:
        response = requests.get(f"{base_url}/")
        response.raise_for_status()
        
        data = response.json()
        print("Root endpoint response:")
        pprint(data)
        
        return True
    except Exception as e:
        print(f"Error testing root endpoint: {e}")
        return False


def test_flights_search(base_url):
    """Test the flights search endpoint."""
    print("\n=== TESTING FLIGHTS SEARCH ENDPOINT ===\n")
    
    # Future date for testing
    future_date = (datetime.now() + timedelta(days=180)).strftime("%Y-%m-%d")
    
    # Set up test parameters for one-way flight
    params = {
        "from_airport": "JFK",
        "to_airport": "LAX", 
        "date": future_date,
        "trip": "one-way",
        "adults": 1,
        "children": 0,
        "infants_in_seat": 0,
        "infants_on_lap": 0,
        "seat": "economy",
        "max_stops": 5,
        "max_results": 10,
        "debug": True
    }
    
    try:
        print(f"Searching for flights from {params['from_airport']} to {params['to_airport']} on {params['date']}")
        response = requests.get(f"{base_url}/flights/search", params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if 'flights' in data and data['flights']:
            flights = data['flights']
            print(f"Found {len(flights)} flights")
            
            # Print details of all flights (limited by max_results in the API call)
            for i, flight in enumerate(flights):
                print(f"\n{i+1}. {flight.get('name', 'N/A')}")
                print(f"   Departure: {flight.get('departure', 'N/A')}")
                print(f"   Arrival: {flight.get('arrival', 'N/A')}")
                print(f"   Duration: {flight.get('duration', 'N/A')}")
                print(f"   Stops: {flight.get('stops', 'N/A')}")
                print(f"   Price: {flight.get('price', 'N/A')}")
                if flight.get('is_best'):
                    print(f"   Best Option")
        else:
            print("No flights found or API error")
            if 'detail' in data:
                print(f"Error: {data['detail']}")
        
        return True
    except Exception as e:
        print(f"Error testing flights search: {e}")
        return False


def test_round_trip_search(base_url):
    """Test the flights search endpoint for a round trip."""
    print("\n=== TESTING ROUND TRIP FLIGHTS SEARCH ===\n")
    
    # Future dates for testing
    outbound_date = (datetime.now() + timedelta(days=180)).strftime("%Y-%m-%d")
    return_date = (datetime.now() + timedelta(days=187)).strftime("%Y-%m-%d")
    
    # Set up test parameters for round trip
    params = {
        "from_airport": "SFO",
        "to_airport": "LHR", 
        "date": outbound_date,
        "return_date": return_date,
        "trip": "round-trip",
        "adults": 2,
        "children": 1,
        "seat": "business",
        "max_results": 5,
        "debug": True
    }
    
    try:
        print(f"Searching for round trip flights from {params['from_airport']} to {params['to_airport']}")
        print(f"Outbound: {params['date']}, Return: {params['return_date']}")
        response = requests.get(f"{base_url}/flights/search", params=params)
        response.raise_for_status()
        
        data = response.json()
        
        if 'flights' in data and data['flights']:
            flights = data['flights']
            print(f"Found {len(flights)} flights")
            
            # Print details of all flights (limited by max_results in the API call)
            for i, flight in enumerate(flights):
                print(f"\n{i+1}. {flight.get('name', 'N/A')}")
                print(f"   Departure: {flight.get('departure', 'N/A')}")
                print(f"   Arrival: {flight.get('arrival', 'N/A')}")
                print(f"   Duration: {flight.get('duration', 'N/A')}")
                print(f"   Stops: {flight.get('stops', 'N/A')}")
                print(f"   Price: {flight.get('price', 'N/A')}")
        else:
            print("No flights found or API error")
            if 'detail' in data:
                print(f"Error: {data['detail']}")
        
        return True
    except Exception as e:
        print(f"Error testing round trip flights search: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(description="Test Fast Flights API endpoints")
    parser.add_argument('--test', choices=['root', 'oneway', 'roundtrip', 'all'], 
                      default='all', help="Type of test to run")
    parser.add_argument('--url', default='http://localhost:8002', 
                      help="Base URL of the API (default: http://localhost:8002)")
    
    args = parser.parse_args()
    base_url = args.url
    
    # Run tests based on the argument
    if args.test == 'root' or args.test == 'all':
        test_root_endpoint(base_url)
    
    if args.test == 'oneway' or args.test == 'all':
        test_flights_search(base_url)
    
    # if args.test == 'roundtrip' or args.test == 'all':
    #     test_round_trip_search(base_url)


if __name__ == "__main__":
    main() 