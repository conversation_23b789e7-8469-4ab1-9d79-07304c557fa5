import asyncio
import re
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup, Tag
from urllib.parse import urljoin
import time
import json

from core.models import Product, PriceInfo, RatingInfo, ScraperResults, Review
from utils.zyte_client import ZyteClient

class HomeDepotScraper:
    """A scraper for Homedepot.com to fetch product information."""

    def __init__(self, api_key: str):
        self.base_url = "https://www.homedepot.com"
        self.zyte_client = ZyteClient(api_key)

    def _generate_search_url(self, query: str) -> str:
        """Generates a search URL for a given query."""
        return f"{self.base_url}/s/{query.replace(' ', '%20')}?NCNI-5"

    async def scrape(self, query: str, detailed_count: int = 5, basic_count: int = 15) -> ScraperResults:
        """
        Orchestrates the scraping process.

        Args:
            query: The search term.
            detailed_count: Number of products to scrape in detail.
            basic_count: Number of products to scrape with basic info.

        Returns:
            A ScraperResults object containing all scraped data.
        """
        search_url = self._generate_search_url(query)
        print(f"Scraping search results from: {search_url}")

        html = await self.zyte_client.fetch_html(search_url)
        if not html:
            return ScraperResults(success=False, query=query, search_url=search_url, total_products_found=0, error="Failed to fetch search page HTML.")

        soup = BeautifulSoup(html, "lxml")
        all_pods = soup.select('div[data-testid="product-pod"]')

        if not all_pods:
            return ScraperResults(success=False, query=query, search_url=search_url, total_products_found=0, error="No product pods found on search results page.")
        
        total_to_process = detailed_count + basic_count
        pods_to_process = all_pods[:total_to_process]
        
        print(f"Found {len(all_pods)} products. Processing the top {len(pods_to_process)}.")

        # Extract URLs for detailed scraping
        urls_to_scrape_detail = [urljoin(self.base_url, pod.find('a')['href']) for pod in pods_to_process[:detailed_count] if pod.find('a')]

        # Concurrently scrape detailed product pages
        tasks = [self._parse_product_page(url) for url in urls_to_scrape_detail]
        detailed_products = await asyncio.gather(*tasks)
        # Filter out None results from failed scrapes
        detailed_products = [p for p in detailed_products if p]

        # Get basic info for the rest
        basic_products = [self._parse_search_pod(pod) for pod in pods_to_process[detailed_count:]]

        return ScraperResults(
            success=True,
            query=query,
            search_url=search_url,
            total_products_found=len(all_pods),
            detailed_products=detailed_products,
            basic_products=basic_products
        )

    def _parse_search_pod(self, pod: Tag) -> Product:
        """Extracts basic product information from a search result pod."""
        title_tag = pod.select_one('span[data-testid="attribute-product-label"]')
        brand_tag = pod.select_one('span[data-testid="attribute-brandname-inline"]')
        url_tag = pod.find('a')
        img_tag = pod.select_one('div[data-testid="product-image__wrapper"] img')

        return Product(
            product_url=urljoin(self.base_url, url_tag['href']) if url_tag else "N/A",
            title=title_tag.text.strip() if title_tag else "N/A",
            brand=brand_tag.text.strip() if brand_tag else "N/A",
            image_url=img_tag['src'] if img_tag else "N/A",
            price=self._parse_price_block(pod),
            rating=self._parse_rating_block(pod),
            basic_info_only=True
        )

    async def _parse_product_page(self, url: str) -> Optional[Product]:
        """Fetches and parses a single product detail page."""
        print(f"Fetching detailed info for: {url}")
        html = await self.zyte_client.fetch_html(url)
        if not html:
            print(f"Failed to fetch HTML for product page: {url}")
            return None

        soup = BeautifulSoup(html, "lxml")
        
        title_tag = soup.select_one('h1.sui-h4-bold')
        brand_tag = soup.select_one('a[href*="/b/"][rel="noopener noreferrer"]')
        
        product = Product(
            product_url=url,
            title=title_tag.text.strip() if title_tag else "N/A",
            brand=brand_tag.text.strip() if brand_tag else "N/A",
            image_url="", # Will be populated by main_image_urls
            basic_info_only=False
        )

        # Extract details
        product.price = self._parse_price_block(soup.select_one('div[data-fusion-slot="slotF2"]'))

        # Fallback: if current_price not found, parse JSON-LD structured data
        if not product.price.current_price:
            price_ld_script = soup.select_one('script#thd-helmet__script--productStructureData')
            if price_ld_script and price_ld_script.string:
                try:
                    data = json.loads(price_ld_script.string)
                    # `offers` can be a list or a dict
                    offers = data.get("offers", {})
                    # If list, take first
                    if isinstance(offers, list):
                        offers = offers[0] if offers else {}
                    price_val = offers.get("price")
                    price_currency = offers.get("priceCurrency", "USD")
                    if price_val:
                        product.price.current_price = f"${price_val}" if price_currency == "USD" else f"{price_val} {price_currency}"
                    # Look for crossed out price in priceSpecification
                    spec = offers.get("priceSpecification", {})
                    if isinstance(spec, dict):
                        was_price = spec.get("price")
                        if was_price and not product.price.was_price:
                            product.price.was_price = f"${was_price}" if price_currency == "USD" else f"{was_price} {price_currency}"

                    # ------------------ REVIEWS ------------------
                    reviews_data = data.get("review", [])
                    if isinstance(reviews_data, dict):
                        reviews_data = [reviews_data]

                    for rev in reviews_data[:10]:  # limit to first 10 to keep payload reasonable
                        try:
                            rating_val = rev.get("reviewRating", {}).get("ratingValue")
                            author = rev.get("author", {}).get("name", "")
                            headline = rev.get("headline") or ""
                            body = rev.get("reviewBody") or ""
                            if rating_val is not None and author:
                                product.reviews.append(
                                    Review(
                                        rating_value=float(rating_val),
                                        author=author,
                                        headline=headline,
                                        body=body,
                                    )
                                )
                        except Exception:
                            continue
                except Exception:
                    pass

        product.rating = self._parse_rating_block(soup.select_one('span#product-details__review__target'))
        product.main_image_urls = [img['src'] for img in soup.select('.mediagallery__thumbnail img')]
        if product.main_image_urls:
            product.image_url = product.main_image_urls[0].replace('_100.jpg', '_600.jpg')

        product.highlights = [li.text.strip() for li in soup.select('div[data-testid="SalientPoints"] ul li')]
        
        info_bar = soup.select_one('div.product-info-bar')
        if info_bar:
            model_text = info_bar.find(string=re.compile(r"Model #"))
            sku_text = info_bar.find(string=re.compile(r"Store SKU #"))
            if model_text:
                product.model_number = model_text.find_next('span').text.strip()
            if sku_text:
                product.sku = sku_text.find_next('span').text.strip()
        
        specs = {}
        spec_rows = soup.select("#product-section-key-feat .kpf__specblock")
        for row in spec_rows:
            name = row.select_one(".kpf__name")
            value = row.select_one(".kpf__value")
            if name and value:
                specs[name.text.strip()] = value.text.strip()
        product.specifications = specs

        return product

    def _parse_price_block(self, block: Optional[Tag]) -> PriceInfo:
        """Parses a price block from either a search pod or product page."""
        if not block:
            return PriceInfo()

        price_info = PriceInfo()
        
        # ---------- CURRENT PRICE ----------
        # Home Depot renders the price in 3 spans: a $ sign (3xl), the dollar part (9xl) and cents (3xl).
        # The selector below reliably grabs the dollar span. We then look for its immediate
        # sibling that contains the cents value.

        price_dollars = block.select_one('span.sui-text-9xl')
        if not price_dollars:
            # Fallback used on some views (e.g. special price banners)
            price_dollars = block.select_one('span.sui-text-4xl')

        if price_dollars:
            dollars = price_dollars.get_text(strip=True)

            # Cents are usually in the following <span class="sui-font-display sui-text-3xl">00</span>
            next_span = price_dollars.find_next_sibling('span')
            cents = None
            if next_span and re.fullmatch(r'\d{2}', next_span.get_text(strip=True)):
                cents = next_span.get_text(strip=True)

            price_info.current_price = f"${dollars}"
            if cents and cents != "00":
                price_info.current_price += f".{cents}"

        # Was/Save price
        was_price_tag = block.find('span', string=lambda x: isinstance(x, str) and x.strip().startswith('Was'))
        if was_price_tag and was_price_tag.find_next('span'):
            price_info.was_price = was_price_tag.find_next('span').get_text(strip=True)
        
        save_amount_tag = block.find('div', string=re.compile(r'Save'))
        if save_amount_tag and save_amount_tag.find('span'):
            price_info.save_amount = save_amount_tag.find('span').text.strip()

        # Special financing
        financing_tag = block.select_one('.payment-estimator-message span')
        if financing_tag:
            price_info.special_financing = financing_tag.text.strip()
            
        return price_info

    def _parse_rating_block(self, block: Optional[Tag]) -> RatingInfo:
        """Parses a rating block."""
        if not block:
            return RatingInfo()
            
        rating_info = RatingInfo()
        
        # For product page
        rating_button = block.select_one('button[data-testid="rating-button"]')
        if rating_button:
            label = rating_button.get('title', '') # "4.4 out of 5"
            match = re.search(r'(\d\.?\d*)', label)
            if match:
                rating_info.average_rating = float(match.group(1))
            
            count_tag = rating_button.find('span', class_='sui-text-primary')
            if count_tag:
                count_text = count_tag.text.strip('()')
                if count_text.isdigit():
                    rating_info.review_count = int(count_text)
            return rating_info

        # For search results page
        rating_link = block.select_one('a[data-testid="product-pod__ratings-link"]')
        if rating_link:
            img_span = rating_link.select_one('span[role="img"]')
            if img_span:
                label = img_span.get('aria-label', '') # "4.5 Stars"
                match = re.search(r'(\d\.?\d*)', label)
                if match:
                    rating_info.average_rating = float(match.group(1))

            count_span = rating_link.select_one('span.sui-text-subtle')
            if count_span:
                count_text = re.sub(r'[\(\),]', '', count_span.text)
                if count_text.isdigit():
                    rating_info.review_count = int(count_text)

        return rating_info 