from fastapi import FastAP<PERSON>, Query, HTTPException
from fastapi.responses import JSONResponse
from dataclasses import asdict, is_dataclass
import os

from scrapers.homedepot_scraper import HomeDepotScraper
from core.models import ScraperResults

app = FastAPI(
    title="Home Depot Scraper API",
    description="An API to scrape product search results from homedepot.com",
    version="1.0.0",
)

@app.get("/search", 
         tags=["Scraping"], 
         summary="Scrape Home Depot search results",
         response_model=ScraperResults)
async def search_home_depot(
    query: str = Query(..., description="The product to search for.", min_length=2),
    zyte_api_key: str = Query(os.getenv("ZYTE_API_KEY"), description="Your Zyte Data API key. Can also be set via .env file."),
    limit: int = Query(5, ge=1, le=10, description="Number of products to get detailed information for."),
):
    """
    Performs a search on homedepot.com, scrapes the top products,
    and returns detailed and basic information in a JSON format.
    """
    if not zyte_api_key:
        raise HTTPException(status_code=400, detail="Zyte API key is required. Provide it as a query parameter or in the .env file.")

    scraper = HomeDepotScraper(api_key=zyte_api_key)
    results = await scraper.scrape(query=query, detailed_count=limit)

    if not results.success:
        raise HTTPException(status_code=500, detail=results.error or "Scraping failed for an unknown reason.")

    # Convert dataclasses to dicts for JSON serialization
    return JSONResponse(content=asdict(results))

@app.get("/", tags=["Meta"])
async def root():
    return {
        "message": "Welcome to the Home Depot Scraper API!",
        "documentation": "/docs"
    } 