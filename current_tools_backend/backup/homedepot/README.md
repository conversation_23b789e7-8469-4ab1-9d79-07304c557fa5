# Home Depot Scraper

This project contains a Python-based asynchronous web scraper for `homedepot.com`, packaged as a Dockerized FastAPI application. It uses the Zyte Data API for fetching web pages to bypass bot detection.

## Features

-   **Async Scraping**: Utilizes `asyncio` to fetch multiple product pages in parallel.
-   **Robust Fetching**: Leverages the Zyte Data API to handle proxies, JavaScript rendering, and anti-bot measures.
-   **Detailed Data Extraction**: Parses key information from both search result pages and product detail pages.
-   **RESTful API**: Exposes the scraper functionality through a clean FastAPI interface.
-   **Dockerized**: Comes with a `Dockerfile` and `docker-compose.yml` for easy, reproducible deployment.
-   **CLI Runner**: Includes a `main.py` for direct command-line execution.

## How to Run

### Prerequisites

-   Docker and Docker Compose
-   A Zyte Data API key

### 1. Setup

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd homedepot_scraper
    ```

2.  **Create the `.env` file:**
    Copy the provided `.env.example` to `.env` and add your Zyte API key.
    ```bash
    cp .env.example .env
    # Now edit .env and add your key
    ```

### 2. Running with Docker Compose (Recommended)

This is the simplest way to run the API server.

```bash
docker-compose up --build
```

The API will be available at `http://localhost:8000`. You can access the auto-generated documentation at `http://localhost:8000/docs`.

### 3. Testing the API

Once the container is running, you can use the `test_api.py` script to send a sample request to the `/search` endpoint.

```bash
# Make sure your ZYTE_API_KEY is exported in your terminal
export ZYTE_API_KEY="your-key-here"

python test_api.py
```

### 4. Running the CLI

To run the scraper directly without the API server:

1.  Create a virtual environment and install dependencies:
    ```bash
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    ```
2.  Run the `main.py` script:
    ```bash
    python main.py
    ```
The script will prompt you for a search query and save the results to a JSON file in the `results/` directory.

## API Endpoint

### `GET /search`

-   **Description**: Scrapes Home Depot for a given search query.
-   **Query Parameters**:
    -   `query` (string, required): The search term (e.g., "fridge").
    -   `limit` (integer, optional, default: 5): The number of top products to scrape in detail.
    -   `zyte_api_key` (string, required): Your Zyte API key.

-   **Example Request**:
    ```
    http://localhost:8000/search?query=bosch%20fridge&limit=5&zyte_api_key=YOUR_KEY
    ```

-   **Success Response (200 OK)**:
    A JSON object containing detailed and basic product lists, along with metadata about the scrape.

-   **Error Response (4xx/5xx)**:
    A JSON object with a `detail` key explaining the error. 