import asyncio
import json
import os
from dotenv import load_dotenv

from scrapers.homedepot_scraper import HomeDepotScraper
from dataclasses import asdict

# --------------------------- CONFIGURABLE EXAMPLE ---------------------------
# Modify these constants if you want to scrape a different query or change
# how many products receive detailed vs basic extraction.

QUERY = "fridge"
DETAILED_COUNT = 5   # top N products scraped in detail
BASIC_COUNT = 15     # additional products scraped with basic info (DETAILED+BASIC = 20)

# ---------------------------------------------------------------------------


def main() -> None:
    """Run the scraper with a predefined example (no interactive input)."""

    load_dotenv()

    api_key = os.getenv("ZYTE_API_KEY")
    if not api_key:
        print("❌ Error: ZYTE_API_KEY not found in environment or .env file.")
        return

    print("--- Home Depot Scraper | Predefined Example ---")
    print(f"Query          : {QUERY}")
    print(f"Detailed count : {DETAILED_COUNT}")
    print(f"Basic count    : {BASIC_COUNT}\n")

    scraper = HomeDepotScraper(api_key=api_key)

    print("🚀 Starting scraper...")
    results = asyncio.run(
        scraper.scrape(QUERY, detailed_count=DETAILED_COUNT, basic_count=BASIC_COUNT)
    )
    print("✅ Scraping finished.\n")

    if not results.success:
        print(f"❌ Scraping failed: {results.error}")
        return

    # Save results to a file for easy inspection
    output_filename = f"results/homedepot_{QUERY.replace(' ', '_')}.json"
    os.makedirs("results", exist_ok=True)

    with open(output_filename, "w", encoding="utf-8") as f:
        json.dump(asdict(results), f, indent=2, ensure_ascii=False)

    print(f"📊 Results saved to {output_filename}")
    print(f"  - Found {results.total_products_found} total products on page.")
    print(f"  - Scraped {len(results.detailed_products)} products in detail.")
    print(f"  - Scraped {len(results.basic_products)} products with basic info.\n")


if __name__ == "__main__":
    main() 