from __future__ import annotations
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

@dataclass
class PriceInfo:
    """Detailed pricing information for a product."""
    current_price: str = ""
    was_price: str = ""
    save_amount: str = ""
    price_per: str = ""
    special_financing: str = ""
    low_price_guarantee: bool = False

@dataclass
class RatingInfo:
    """Product rating and review count."""
    average_rating: float = 0.0
    review_count: int = 0

@dataclass
class Review:
    """Individual customer review from JSON-LD data"""
    rating_value: float
    author: str
    headline: str = ""
    body: str = ""

@dataclass
class Product:
    """Represents a single product scraped from Home Depot."""
    # Core fields
    product_url: str
    title: str
    brand: str
    image_url: str

    # Detailed fields (from product page)
    price: PriceInfo = field(default_factory=PriceInfo)
    rating: RatingInfo = field(default_factory=RatingInfo)
    model_number: str = ""
    sku: str = ""
    main_image_urls: List[str] = field(default_factory=list)
    highlights: List[str] = field(default_factory=list)
    specifications: Dict[str, str] = field(default_factory=dict)

    # Optional list of parsed reviews
    reviews: List[Review] = field(default_factory=list)

    # Basic info (from search page, can be a fallback)
    basic_info_only: bool = True

@dataclass
class ScraperResults:
    """Wrapper for the final JSON output."""
    success: bool
    query: str
    search_url: str
    total_products_found: int
    detailed_products: List[Product] = field(default_factory=list)
    basic_products: List[Product] = field(default_factory=list)
    error: Optional[str] = None 