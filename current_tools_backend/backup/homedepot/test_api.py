import requests
import json
import os
import time

# Get the base URL from an environment variable, defaulting to localhost
BASE_URL = os.environ.get("API_URL", "http://localhost:8000")
API_KEY = os.getenv("ZYTE_API_KEY")

if not API_KEY:
    print("Error: ZYTE_API_KEY environment variable not set.")
    exit(1)

params = {
    "query": "bosch fridge",
    "limit": 3,
    "zyte_api_key": API_KEY
}

print(f"Requesting {BASE_URL}/search with params: {params['query']}, limit={params['limit']}")

try:
    start_time = time.time()
    resp = requests.get(f"{BASE_URL}/search", params=params, timeout=120)
    elapsed = time.time() - start_time
    print(f"Request completed in {elapsed:.2f} seconds.")
    print(f"Status: {resp.status_code}")
    resp.raise_for_status()

    data = resp.json()

    if not data.get("success"):
        print("API reported failure:")
        print(json.dumps(data, indent=2))
        exit(1)

    print(f"\n✔️ Success flag is True.")
    print(f"✔️ Found {data.get('total_products_found')} products on the search page.")
    print(f"✔️ Received {len(data.get('detailed_products', []))} detailed products.")
    print(f"✔️ Received {len(data.get('basic_products', []))} basic products.")

    print("\n--- Detailed Products Sample ---")
    for i, prod in enumerate(data.get("detailed_products", [])[:2], 1):
        print(f"{i}. {prod.get('brand')} - {prod.get('title')}")
        print(f"   Price: {prod.get('price', {}).get('current_price')}")
        print(f"   URL: {prod.get('product_url')}")

    # Save full response to a file
    os.makedirs("results", exist_ok=True)
    timestamp = int(time.time())
    outfile = f"results/api_test_{params['query'].replace(' ', '_')}_{timestamp}.json"
    with open(outfile, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"\nFull response written to {outfile}")

except requests.exceptions.RequestException as e:
    print(f"\nAn error occurred: {e}")
    if 'resp' in locals():
        print("Response content:", resp.text)
except json.JSONDecodeError:
    print("\nFailed to decode JSON from response.")
    print("Response content:", resp.text) 