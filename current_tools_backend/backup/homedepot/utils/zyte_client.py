from __future__ import annotations
import asyncio
from base64 import b64decode
import os
from typing import Optional
import requests
from dotenv import load_dotenv

class ZyteClient:
    """Async-compatible client for the Zyte Data API using the synchronous `requests` library."""

    def __init__(self, api_key: str, timeout: float = 90.0):
        if not api_key:
            raise ValueError("Zyte API key cannot be empty.")
        self._api_key = api_key
        self._timeout = timeout
        self._endpoint = "https://api.zyte.com/v1/extract"

    def _post_request(self, url: str) -> Optional[bytes]:
        """Sends a synchronous request to Zyte and returns the base64-decoded HTML."""
        try:
            api_response = requests.post(
                self._endpoint,
                auth=(self._api_key, ""),
                json={"url": url, "httpResponseBody": True},
                timeout=self._timeout
            )
            api_response.raise_for_status()
            json_response = api_response.json()

            if "httpResponseBody" in json_response:
                return b64decode(json_response["httpResponseBody"])
            else:
                print(f"Warning: 'httpResponseBody' not in Zyte response for {url}")
                return None

        except requests.RequestException as e:
            print(f"Zyte request to {url} failed: {e}")
            return None
        except Exception as e:
            print(f"An unexpected error occurred during Zyte request to {url}: {e}")
            return None

    async def fetch_html(self, url: str) -> Optional[str]:
        """
        Fetches rendered HTML for a URL.

        This method is async, but it runs the synchronous `_post_request`
        in a separate thread to avoid blocking the asyncio event loop.
        """
        html_bytes = await asyncio.to_thread(self._post_request, url)
        if html_bytes:
            return html_bytes.decode('utf-8', errors='ignore')
        return None

    @staticmethod
    def from_env() -> "ZyteClient":
        """Creates a ZyteClient instance from environment variables."""
        load_dotenv()
        key = os.getenv("ZYTE_API_KEY")
        if not key:
            raise ValueError("ZYTE_API_KEY not found in environment or .env file.")
        return ZyteClient(key) 