# Python-specific ignores
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.env/
.venv/

# Log files
*.log
etsy_crawler.log
etsy_product_extractor.log

# Data and output directories
results/
etsy_extraction_*/
*.json
*.csv
*.xlsx

# IDE and editor files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Operating System files
.DS_Store
Thumbs.db

# Sensitive or local configuration
.env
config.ini

# Temporary files
*.tmp
*.bak
*~

# Jupyter Notebook
.ipynb_checkpoints/

# Compiled Python files
*.pyc

# Distribution / packaging
dist/
build/
*.egg-info/

# User-specific files
user_messages_buffer.md 
ask_user.py 