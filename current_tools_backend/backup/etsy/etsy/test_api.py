import requests, pprint, os, json, time

BASE_URL = os.environ.get("API_URL", "http://localhost:8000")
ZYTE_API_KEY = os.getenv("ZYTE_API_KEY")

if not ZYTE_API_KEY:
    print("❌ ZYTE_API_KEY environment variable not set. Please set it to run the test.")
    raise SystemExit(1)

params = {
    "query": "handmade leather jacket",
    "k_limit": 2,
    "zyte_api_key": ZYTE_API_KEY,
    "countries": "USA,UK",
}

print(f"Requesting {BASE_URL}/search")
try:
    resp = requests.get(f"{BASE_URL}/search", params=params, timeout=120)
    resp.raise_for_status()  # Raise an exception for bad status codes
except requests.exceptions.RequestException as e:
    print(f"❌ Request failed: {e}")
    raise SystemExit(1)

print(f"Status: {resp.status_code}")

try:
    data = resp.json()
except json.JSONDecodeError:
    print("❌ Failed to decode JSON response.")
    print(f"Response text: {resp.text}")
    raise SystemExit(1)

if not data.get("success"):
    print("❌ API call was not successful.")
    pprint.pprint(data)
    raise SystemExit(1)

print(f"✔️  Received {data.get('total_found')} detailed products.")
for i, prod in enumerate(data.get("products", [])[:5], 1):
    print(f"{i}. [{prod.get('country')}] {prod.get('name')} ({prod.get('price')} {prod.get('currency')})")

# Check if results were saved by API
api_saved_file = data.get("saved_to")
if api_saved_file:
    print(f"\n✅ API automatically saved results to: {api_saved_file}")
    if os.path.exists(api_saved_file):
        print(f"📁 File exists and is {os.path.getsize(api_saved_file)} bytes")
    else:
        print("⚠️  File path returned but file not found")
else:
    print("\n⚠️  API did not return saved file path")

# Save additional copy for test purposes
os.makedirs("results", exist_ok=True)
file_path = f"results/etsy_api_test_{int(time.time())}.json"
with open(file_path, "w", encoding="utf-8") as fp:
    json.dump(data, fp, indent=2, ensure_ascii=False)
print(f"\n📄 Test results also saved to: {file_path}")

# Show results directory contents
print(f"\n📂 Results directory contents:")
if os.path.exists("results"):
    files = os.listdir("results")
    if files:
        for f in sorted(files)[-5:]:  # Show last 5 files
            filepath = os.path.join("results", f)
            size = os.path.getsize(filepath)
            print(f"  - {f} ({size} bytes)")
    else:
        print("  - No files found")
else:
    print("  - Results directory not found") 