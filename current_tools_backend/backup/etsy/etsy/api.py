from fastapi import FastAPI, Query
from fastapi.responses import JSONResponse
from typing import List, Optional
import asyncio
import logging
import json
import os
from datetime import datetime

from scrapers.async_scraper_service import AsyncEtsyScraper

app = FastAPI(
    title="Etsy Scraper API",
    description="Search Etsy sites across multiple regions and return listing + detailed data.",
    version="1.0.0",
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def _ensure_results_directory():
    """Create results directory if it doesn't exist."""
    results_dir = "results"
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
        logger.info(f"Created results directory: {results_dir}")
    return results_dir


def _save_results(result: dict, query: str, countries: list[str]) -> str:
    """Save results to JSON file and return the file path."""
    results_dir = _ensure_results_directory()
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    countries_str = "_".join(countries)
    query_safe = "".join(c for c in query if c.isalnum() or c in (' ', '-', '_')).rstrip()
    query_safe = query_safe.replace(' ', '_')
    
    filename = f"etsy_api_{query_safe}_{countries_str}_{timestamp}.json"
    filepath = os.path.join(results_dir, filename)
    
    # Add metadata to the result
    result_with_metadata = {
        "metadata": {
            "query": query,
            "countries": countries,
            "timestamp": timestamp,
            "scraper": "etsy",
            "version": "1.0.0",
            "source": "api"
        },
        "results": result
    }
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result_with_metadata, f, indent=2, ensure_ascii=False)
        logger.info(f"Results saved to: {filepath}")
        return filepath
    except Exception as e:
        logger.error(f"Failed to save results: {e}")
        return ""


@app.on_event("startup")
async def on_startup():
    app.state.scraper_cache = {}
    app.state.lock = asyncio.Lock()
    logger.info("Etsy Scraper API started up.")


@app.get("/", tags=["meta"], summary="Tool manual")
async def manual():
    return {
        "name": "Etsy Scraper API",
        "description": "An API to scrape product listings and details from Etsy using Zyte.",
        "endpoints": {
            "/search": {
                "method": "GET",
                "params": [
                    "query – required, search keyword(s)",
                    "countries – optional comma-separated list of country names or 'all' (default: all)",
                    "k_limit – optional integer (1-20), number of detailed products to fetch per country (default: 5)",
                    "zyte_api_key – required, your Zyte API key",
                ],
            }
        },
    }


@app.get("/search", tags=["search"], summary="Run Etsy search and fetch product data")
async def search(
    query: str = Query(..., min_length=1, description="Search keywords for products"),
    countries: str = Query("all", description="Comma-separated list of country names (e.g., USA,UK) or 'all'"),
    k_limit: int = Query(5, ge=1, le=20, description="Number of detailed products to fetch per country"),
    zyte_api_key: str = Query(..., description="Your Zyte API key"),
):
    # Resolve country list
    if countries.lower() == "all":
        country_list = list(AsyncEtsyScraper.AVAILABLE_DOMAINS.keys())
    else:
        available_countries_upper = {c.upper(): c for c in AsyncEtsyScraper.AVAILABLE_DOMAINS.keys()}
        country_list = []
        for c in countries.split(","):
            match = available_countries_upper.get(c.strip().upper())
            if match:
                country_list.append(match)
    
    if not country_list:
        return JSONResponse(
            status_code=400,
            content={"success": False, "error": "No valid countries specified. Please check the available countries."}
        )

    # Re-use scraper instance per API key to leverage connection pooling
    async with app.state.lock:
        scraper = app.state.scraper_cache.get(zyte_api_key)
        if scraper is None:
            logger.info(f"Creating new scraper instance for API key ending in ...{zyte_api_key[-4:]}")
            scraper = AsyncEtsyScraper(zyte_api_key=zyte_api_key)
            app.state.scraper_cache[zyte_api_key] = scraper

    logger.info(f"Starting scrape for query='{query}' in countries={country_list} with k_limit={k_limit}")
    result = await scraper.scrape_products(query=query, countries=country_list, k_limit=k_limit)
    
    # Save results to file
    if result.get("success"):
        filepath = _save_results(result, query, country_list)
        if filepath:
            result["saved_to"] = filepath
            logger.info(f"API results saved to: {filepath}")
    
    return JSONResponse(content=result) 