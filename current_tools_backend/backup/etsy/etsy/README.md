# 🛒 Etsy Multi-Region Web Scraper

A powerful web scraper for extracting product data from Etsy across multiple regions with Zyte API integration. This project is built with an asynchronous architecture for high performance and **automatically saves all results to JSON files**.

## 🚀 Quick Start

### API (Recommended)

The project is designed to run as a REST API using Docker.

1.  **Set up environment:** Create a `.env` file with your Zyte API key.
    ```bash
    echo "ZYTE_API_KEY=your_api_key_here" > .env
    ```

2.  **Build & run the API:**
    ```bash
    docker compose up --build -d
    ```

3.  **Query the running container:**
    ```bash
    # Search for "handmade leather jacket" in the USA and UK, getting top 2 results
    curl "http://localhost:8000/search?query=handmade+leather+jacket&countries=USA,UK&k_limit=2&zyte_api_key=<YOUR_KEY>"
    ```

The container exposes FastAPI docs at `http://localhost:8000/docs`.

### Command-Line Interface (CLI)

You can also run the scraper interactively from the command line.

1.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

2.  **Set up Zyte API key (if not already done):**
    ```bash
    # Set environment variable
    export ZYTE_API_KEY=your_api_key_here

    # Or add to your .env file
    echo "ZYTE_API_KEY=your_api_key_here" > .env
    ```

3.  **Run the CLI:**
    ```bash
    python main.py
    ```

## 💾 Automatic Result Saving

**All scraping results are automatically saved to JSON files** in the `results/` directory:

- **CLI Mode**: Files named `etsy_scrape_{query}_{countries}_{timestamp}.json`
- **API Mode**: Files named `etsy_api_{query}_{countries}_{timestamp}.json`

Each saved file contains:
```json
{
  "metadata": {
    "query": "handmade leather jacket",
    "countries": ["USA", "UK"],
    "timestamp": "20240101_120000",
    "scraper": "etsy",
    "version": "1.0.0",
    "source": "api"
  },
  "results": {
    "success": true,
    "listing_products": [...],
    "products": [...],
    "total_found": 10,
    "search_urls": {...}
  }
}
```

## 📁 Project Structure

```
etsy/
├── core/                    # Core components (models, controller)
├── scrapers/                # Scraping logic
├── utils/                   # Utilities (CLI, Zyte client)
├── results/                 # 📁 Saved scraping results (auto-created)
├── api.py                   # FastAPI application
├── main.py                  # CLI entry point
├── Dockerfile               # Container definition
├── docker-compose.yml       # Docker Compose configuration
├── requirements.txt         # Dependencies
└── README.md                # This file
```

## 🌍 Supported Regions

The scraper can target different regional Etsy domains:
- **USA**: www.etsy.com
- **UK**: www.etsy.co.uk
- **Germany**: www.etsy.de
- **France**: www.etsy.fr
- **Spain**: www.etsy.es
- **Italy**: www.etsy.it
- **Canada**: www.etsy.com/ca
- **Australia**: www.etsy.com/au

## 🎯 Features

- ✅ **Asynchronous Scraping**: High-performance, non-blocking architecture using `asyncio` and `httpx`.
- ✅ **Zyte API Integration**: Leverages Zyte API for robust, AI-powered extraction of `productList` and `product` data.
- ✅ **Multi-Region Support**: Scrapes across different Etsy domains in parallel.
- ✅ **REST API**: A clean, documented FastAPI interface for programmatic access.
- ✅ **Interactive CLI**: A user-friendly command-line menu for manual scraping tasks.
- ✅ **Automatic Saving**: All results automatically saved to timestamped JSON files.
- ✅ **Containerized**: Ready to be deployed with Docker and Docker Compose.

## 📊 Output Format

The scraper returns results in a structured JSON format.

### Sample Output

```json
{
  "success": true,
  "listing_products": [
    {
      "country": "USA",
      "name": "Handmade Leather Jacket for Men",
      "price": "127.50",
      "url": "https://www.etsy.com/listing/...",
      "image": "https://i.etsystatic.com/...",
      "seller_name": "LeatherArtisan",
      "rating": 4.8,
      "review_count": 152
    }
  ],
  "products": [
    {
      "country": "USA",
      "name": "Handmade Leather Jacket for Men",
      "price": "127.50",
      "currency": "USD",
      "url": "https://www.etsy.com/listing/...",
      "seller_name": "LeatherArtisan",
      "description": "A beautifully crafted men's jacket...",
      "images": ["https://...", "https://..."],
      "rating": 4.8,
      "review_count": 152,
      "materials": ["Genuine Leather", "YKK Zipper"]
    }
  ],
  "total_found": 1,
  "search_urls": {
    "USA": "https://www.etsy.com/search?q=handmade+leather+jacket"
  },
  "saved_to": "results/etsy_api_handmade_leather_jacket_USA_UK_20240101_120000.json"
}
```

## 🛡️ Anti-Bot & Reliability

- All requests are proxied through the Zyte API, which handles sophisticated anti-bot countermeasures.
- The `ZyteClient` utility includes a basic retry mechanism.

## 🔧 Troubleshooting

- **Zyte API errors**: Check that your `ZYTE_API_KEY` is correct and your account has sufficient credits.
- **No products found**: The search query might not yield results on Etsy. Try a different query.
- **Connection issues**: Ensure your internet connection is stable and that Docker can access the network.
- **Missing results files**: Check the `results/` directory for saved JSON files. If the directory doesn't exist, it will be created automatically on first run. 