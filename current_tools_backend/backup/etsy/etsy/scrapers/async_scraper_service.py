#!/usr/bin/env python3
"""
Async Etsy Scraper
------------------

Fetch up to 20 listing products per selected region using Zyte API
and then retrieve detailed information for the top *k* products per region
in parallel.
"""

from __future__ import annotations
import asyncio
import logging
from typing import Dict, List, Any, Optional

import httpx

from utils.zyte_client import ZyteClient

_ZYTE_ENDPOINT = "https://api.zyte.com/v1/extract"


class AsyncEtsyScraper:
    """Main async scraper class for Etsy stores."""

    AVAILABLE_DOMAINS: Dict[str, Dict[str, str]] = {
        "USA": {"domain": "www.etsy.com", "currency": "USD", "symbol": "$"},
        "UK": {"domain": "www.etsy.co.uk", "currency": "GBP", "symbol": "£"},
        "Germany": {"domain": "www.etsy.de", "currency": "EUR", "symbol": "€"},
        "France": {"domain": "www.etsy.fr", "currency": "EUR", "symbol": "€"},
        "Spain": {"domain": "www.etsy.es", "currency": "EUR", "symbol": "€"},
        "Italy": {"domain": "www.etsy.it", "currency": "EUR", "symbol": "€"},
        "Canada": {"domain": "www.etsy.com/ca", "currency": "CAD", "symbol": "$"},
        "Australia": {"domain": "www.etsy.com/au", "currency": "AUD", "symbol": "$"},
    }

    def __init__(self, zyte_api_key: str | None = None):
        self.logger = logging.getLogger(__name__)
        zyte_client = ZyteClient.from_env()
        self._api_key = zyte_api_key or (zyte_client._api_key if zyte_client else None)
        if not self._api_key:
            raise RuntimeError("Zyte API key must be provided via parameter or ZYTE_API_KEY env var")

    async def scrape_products(
        self,
        query: str,
        countries: List[str],
        k_limit: int = 5,
        zyte_api_key: str | None = None,
    ) -> Dict[str, Any]:
        """Run listing + detailed extraction for Etsy."""
        if zyte_api_key:
            self._api_key = zyte_api_key
        if not self._api_key:
            return {"success": False, "error": "No Zyte API key provided"}

        listing_products: List[Dict[str, Any]] = []
        search_urls_by_country: Dict[str, str] = {}
        
        for country in countries:
            country_info = self.AVAILABLE_DOMAINS.get(country)
            if not country_info:
                self.logger.warning(f"Country '{country}' not found in available domains. Skipping.")
                continue

            domain = country_info["domain"]
            search_url = f"https://{domain}/search?q={query.replace(' ', '+')}"
            search_urls_by_country[country] = search_url
            
            self.logger.info(f"Fetching listing page for '{query}' in {country} from {search_url}")
            result = await self._fetch_listing_products(search_url, country, country_info)
            listing_products.extend(result)

        urls_per_country: Dict[str, List[str]] = {}
        for prod in listing_products:
            urls_per_country.setdefault(prod["country"], []).append(prod["url"])

        detail_tasks = [
            self._fetch_product_detail(url, country)
            for country, urls in urls_per_country.items()
            for url in urls[:k_limit]
        ]
        
        semaphore = asyncio.Semaphore(5)
        async def _guarded(coro):
            async with semaphore:
                return await coro

        self.logger.info(f"Fetching details for {len(detail_tasks)} products concurrently.")
        detailed_products_raw = await asyncio.gather(*[_guarded(t) for t in detail_tasks])
        detailed_products = [p for p in detailed_products_raw if p]

        return {
            "success": True,
            "listing_products": listing_products,
            "products": detailed_products,
            "total_found": len(detailed_products),
            "search_urls": search_urls_by_country,
        }

    async def _fetch_listing_products(
        self, search_url: str, country: str, country_info: Dict[str, str]
    ) -> List[Dict[str, Any]]:
        """Call Zyte API for a search result page."""
        payload = {"url": search_url, "productList": True}
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                resp = await client.post(_ZYTE_ENDPOINT, auth=(self._api_key, ""), json=payload)
            if resp.status_code != 200:
                self.logger.error(f"Zyte API request for listing failed with status {resp.status_code}: {resp.text}")
                return []
            
            data = resp.json()
            products = data.get("productList", {}).get("products", [])
            results: List[Dict[str, Any]] = []
            for p in products[:20]:
                rating_info = p.get("aggregateRating", {}) or {}
                results.append({
                    "country": country,
                    "domain": country_info["domain"],
                    "currency": country_info["currency"],
                    "currency_symbol": country_info["symbol"],
                    "search_url": search_url,
                    "name": p.get("name", "N/A"),
                    "price": str(p.get("price", "N/A")),
                    "url": p.get("url", "N/A"),
                    "image": self._extract_main_image(p),
                    "seller_name": self._safe_get_nested(p, ["brand", "name"], "N/A"),
                    "rating": rating_info.get("ratingValue"),
                    "review_count": rating_info.get("reviewCount"),
                    "sku": p.get("sku", "N/A"),
                })
            return results
        except Exception as e:
            self.logger.error(f"Error fetching listing products from {search_url}: {e}", exc_info=True)
            return []

    async def _fetch_product_detail(self, url: str, country: str) -> Optional[Dict[str, Any]]:
        """Call Zyte API for a product detail page."""
        payload = {"url": url, "product": True, "productOptions": {"ai": True}}
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                resp = await client.post(_ZYTE_ENDPOINT, auth=(self._api_key, ""), json=payload)
            if resp.status_code != 200:
                self.logger.error(f"Zyte API request for product detail failed for {url} with status {resp.status_code}: {resp.text}")
                return None

            data = resp.json()
            prod = data.get("product")
            if not prod:
                return None
            
            rating_info = prod.get("aggregateRating", {}) or {}
            brand_info = prod.get("brand", {}) or {}

            return {
                "country": country,
                "url": url,
                "name": prod.get("name", "N/A"),
                "price": str(prod.get("price", "N/A")),
                "currency": prod.get("currency", "N/A"),
                "seller_name": brand_info.get("name", "N/A"),
                "description": prod.get("description", "N/A"),
                "availability": prod.get("availability", "N/A"),
                "images": self._gather_images(prod),
                "rating": rating_info.get("ratingValue"),
                "review_count": rating_info.get("reviewCount"),
                "materials": prod.get("material", []),
                "sku": prod.get("sku", "N/A"),
                "additional_properties": prod.get("additionalProperty", []),
            }
        except Exception as e:
            self.logger.error(f"Error fetching product detail from {url}: {e}", exc_info=True)
            return None

    def _extract_main_image(self, product: Dict[str, Any]) -> str:
        main = product.get("mainImage")
        if isinstance(main, dict):
            return main.get("url", "N/A")
        if isinstance(main, str):
            return main
        return "N/A"

    def _gather_images(self, product: Dict[str, Any]) -> List[str]:
        imgs = []
        main = product.get("mainImage")
        if isinstance(main, dict) and "url" in main:
            imgs.append(main["url"])
        elif isinstance(main, str):
            imgs.append(main)
        for img in product.get("images", []):
            if isinstance(img, dict) and "url" in img:
                imgs.append(img["url"])
            elif isinstance(img, str):
                imgs.append(img)
        return list(dict.fromkeys(imgs))  # Remove duplicates

    def _safe_get_nested(self, data: Dict[str, Any], keys: List[str], default: Any = "N/A") -> Any:
        current = data
        for k in keys:
            if isinstance(current, dict):
                current = current.get(k)
            else:
                return default
            if current is None:
                return default
        return current 