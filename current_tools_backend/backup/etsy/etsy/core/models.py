from __future__ import annotations

"""Minimal data models used by the Etsy async scraper."""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional


@dataclass
class Product:
    """Lightweight listing product (top-level)."""

    name: str
    price: str
    url: str
    image: str
    country: str
    currency: str
    currency_symbol: str
    # Extra optional fields
    seller_name: str = ""
    rating: Optional[float] = None
    review_count: int = 0
    sku: str = ""
    availability: str = ""


@dataclass
class ProductDetail:
    """Detailed product info (after second Zyte call)."""

    name: str
    price: str
    currency: str
    url: str
    country: str
    description: str = ""
    sku: str = ""
    availability: str = ""
    images: List[str] = field(default_factory=list)
    seller_name: str = ""
    rating: Optional[float] = None
    review_count: int = 0
    materials: List[str] = field(default_factory=list)
    additional_properties: List[Dict[str, Any]] = field(default_factory=list)
    raw: Dict[str, Any] = field(default_factory=dict)  # For unprocessed data


@dataclass
class SearchQuery:
    query: str
    countries: List[str]
    k_limit: int = 5

    def is_multi_country(self) -> bool:
        return len(self.countries) > 1 