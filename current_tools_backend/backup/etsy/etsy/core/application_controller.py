"""
Application Controller - Coordinates console UI and AsyncEtsyScraper.
"""

import asyncio
import logging
import json
import os
from datetime import datetime
from typing import Dict, Any

from utils.console_ui import ConsoleUI
from scrapers.async_scraper_service import AsyncEtsyScraper


class ApplicationController:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.ui = ConsoleUI()
        self.scraper = AsyncEtsyScraper()
        self.results_dir = "results"
        self._ensure_results_directory()

    def _ensure_results_directory(self):
        """Create results directory if it doesn't exist."""
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
            self.logger.info(f"Created results directory: {self.results_dir}")

    def _save_results(self, result: Dict[str, Any], query: str, countries: list[str]) -> str:
        """Save results to JSON file and return the file path."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        countries_str = "_".join(countries)
        query_safe = "".join(c for c in query if c.isalnum() or c in (' ', '-', '_')).rstrip()
        query_safe = query_safe.replace(' ', '_')
        
        filename = f"etsy_scrape_{query_safe}_{countries_str}_{timestamp}.json"
        filepath = os.path.join(self.results_dir, filename)
        
        # Add metadata to the result
        result_with_metadata = {
            "metadata": {
                "query": query,
                "countries": countries,
                "timestamp": timestamp,
                "scraper": "etsy",
                "version": "1.0.0"
            },
            "results": result
        }
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(result_with_metadata, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Results saved to: {filepath}")
            return filepath
        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")
            return ""

    async def run_async_scraper(self, query: str, countries: list[str], k_limit: int, zyte_api_key: str | None = None) -> Dict[str, Any]:
        try:
            self.ui.print_header("🚀 ASYNC ETSY SCRAPER")
            params = {
                "query": query,
                "countries": countries,
                "k_limit": k_limit,
            }
            self.ui.display_parameters_summary(params)

            result = await self.scraper.scrape_products(
                query=query,
                countries=countries,
                k_limit=k_limit,
                zyte_api_key=zyte_api_key,
            )
            
            # Save results to file
            if result.get("success"):
                filepath = self._save_results(result, query, countries)
                if filepath:
                    result["saved_to"] = filepath
                    print(f"\n✅ Results saved to: {filepath}")
            
            self.ui.display_scraping_results(result)
            return result
        except Exception as e:
            self.logger.error(str(e), exc_info=True)
            return {"success": False, "error": str(e)}

    # CLI loop
    def run_main_menu(self):
        available_countries = AsyncEtsyScraper.AVAILABLE_DOMAINS
        while True:
            self.ui.print_main_menu()
            choice = input("Choose an option (1-2): ").strip()
            if choice == "1":
                params = self.ui.get_scraping_parameters(available_countries)
                if params:
                    asyncio.run(
                        self.run_async_scraper(
                            params["query"],
                            params["countries"],
                            params["k_limit"],
                            None,  # Rely on ZYTE_API_KEY env var
                        )
                    )
                input("\nPress Enter to continue…")
            elif choice == "2":
                self.ui.print_goodbye()
                break
            else:
                print("Invalid choice. Please enter 1 or 2.") 