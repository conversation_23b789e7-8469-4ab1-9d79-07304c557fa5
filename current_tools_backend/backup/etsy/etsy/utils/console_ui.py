"""
Console UI Module - User Interface Layer
Handles all console interactions and display formatting for the Etsy Scraper.
"""

from typing import Dict, Any, Optional, List
import os
import platform


class ConsoleUI:
    """Console User Interface for the Etsy Scraper"""

    def print_header(self, title: str):
        bar = "=" * 60
        print(f"\n{bar}\n{title.center(60)}\n{bar}")

    def print_main_menu(self):
        self.print_header("ETSY SCRAPER")
        print("1. Run async scraper")
        print("2. Exit\n")

    def get_scraping_parameters(self, available_countries: Dict[str, Dict[str, str]]) -> Optional[Dict[str, Any]]:
        """Ask user for query, country list and detail limit."""
        try:
            print("Enter scraping parameters:")
            query = input("Search query [handmade leather jacket]: ").strip() or "handmade leather jacket"

            print("\nAvailable regions/countries:")
            countries_keys = list(available_countries.keys())
            for idx, name in enumerate(countries_keys, 1):
                domain = available_countries[name]["domain"]
                print(f"{idx}. {name:12} ({domain})")

            raw_selection = input("\nSelect countries (e.g., 1,2 or USA,UK or 'all', default is USA): ").strip()
            
            selected: List[str]
            if not raw_selection:
                selected = ["USA"]
            elif raw_selection.lower() == "all":
                selected = countries_keys
            else:
                parts = [p.strip() for p in raw_selection.split(",")]
                selected = []
                for p in parts:
                    if p.isdigit() and 1 <= int(p) <= len(countries_keys):
                        selected.append(countries_keys[int(p) - 1])
                    else:
                        matches = [c for c in countries_keys if c.lower() == p.lower()]
                        if matches:
                            selected.append(matches[0])
                if not selected:
                    print("❌ No valid countries selected. Defaulting to USA.")
                    selected = ["USA"]

            k_input = input("Detailed products per country (K) [5]: ").strip()
            try:
                k_limit = int(k_input) if k_input else 5
                k_limit = max(1, min(20, k_limit))
            except ValueError:
                k_limit = 5

            return {
                "query": query,
                "countries": list(dict.fromkeys(selected)),  # Remove duplicates
                "k_limit": k_limit,
            }
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
            return None

    def display_parameters_summary(self, params: Dict[str, Any]):
        print("\nParameters summary:")
        print(f"Query: {params['query']}")
        print(f"Regions: {', '.join(params['countries'])}")
        print(f"Detailed K per region: {params['k_limit']}")
        print()

    def display_scraping_results(self, result: Dict[str, Any]):
        print("\nScraper results:")
        print("=" * 50)
        if result.get("success"):
            total_listings = len(result.get("listing_products", []))
            total_detailed = result.get("total_found", 0)
            print(f"📊 Scraped {total_detailed} detailed products from {total_listings} total listings found.")
            
            # Show sample results
            products = result.get("products", [])
            if products:
                print("\n🔍 Sample results:")
                for i, prod in enumerate(products[:3], 1):
                    print(f"{i}. [{prod.get('country')}] {prod.get('name')[:50]}{'...' if len(prod.get('name', '')) > 50 else ''}")
                    print(f"   Price: {prod.get('price')} {prod.get('currency')} | Seller: {prod.get('seller_name')}")
                
                if len(products) > 3:
                    print(f"   ... and {len(products) - 3} more products")
            
            # Show save location
            saved_to = result.get("saved_to")
            if saved_to:
                print(f"\n💾 Results saved to: {saved_to}")
            else:
                print("\n⚠️  Results were not saved to file")
        else:
            print(f"❌ Error: {result.get('error')}")

    def clear_screen(self):
        os.system("cls" if platform.system() == "Windows" else "clear")

    def print_goodbye(self):
        print("\nGoodbye! Thanks for using the Etsy scraper.")
        print("📁 Check the 'results' directory for saved scraping results.") 