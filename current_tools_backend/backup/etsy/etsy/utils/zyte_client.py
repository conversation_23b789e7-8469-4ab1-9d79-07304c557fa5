from __future__ import annotations

"""Light wrapper around the Zyte API used by the Etsy scraper."""

import asyncio
import os
import logging
from typing import Optional

import httpx
from dotenv import load_dotenv

_ZYTE_ENDPOINT = "https://api.zyte.com/v1/extract"


class ZyteClient:
    """Tiny async client for the Zyte API."""

    def __init__(self, api_key: str, timeout: float = 60.0):
        self._api_key = api_key
        self._timeout = timeout
        self.logger = logging.getLogger(__name__)

    async def fetch_html(self, url: str, *, retries: int = 3) -> Optional[str]:
        """Return the rendered `browserHtml` for *url* or ``None`` on failure."""
        payload = {"url": url, "browserHtml": True}
        for attempt in range(retries):
            if attempt:
                await asyncio.sleep(2)
            try:
                async with httpx.AsyncClient(timeout=self._timeout) as client:
                    response = await client.post(
                        _ZYTE_ENDPOINT,
                        auth=(self._api_key, ""),
                        json=payload,
                    )
                if response.status_code == 200:
                    return response.json().get("browserHtml")
                else:
                    self.logger.warning(f"Attempt {attempt + 1} failed for {url} with status {response.status_code}")
            except Exception as e:
                self.logger.error(f"Exception on attempt {attempt + 1} for {url}: {e}")
                pass
        return None

    @staticmethod
    def from_env() -> Optional["ZyteClient"]:
        load_dotenv()
        key = os.getenv("ZYTE_API_KEY") or os.getenv("ZYTE_API_USER")
        if not key:
            logging.warning("ZYTE_API_KEY environment variable not found.")
            return None
        return ZyteClient(key) 