.PHONY: build test clean run lint

# Default target
all: lint test build

# Build the application
build:
	@echo "Building application..."
	go build -o bin/amazonscraper ./cmd/amazonscraper

# Run tests with coverage
test:
	@echo "Running tests..."
	go test -v -cover ./...

# Clean build artifacts
clean:
	@echo "Cleaning..."
	rm -rf bin/
	rm -f response.json response_with_descriptions.json scraped_products.md

# Run linting
lint:
	@echo "Linting..."
	go vet ./...
	
# Run the application
run: build
	@echo "Running application..."
	./bin/amazonscraper

# Build Docker image
docker-build:
	@echo "Building Docker image..."
	docker build -t amazonscraper:latest .

# Run application in Docker
docker-run:
	@echo "Running Docker container..."
	docker run --rm \
		-e ZYTE_API_KEY=7dc25ad2313340a2a54223a634c3f2db \
		-v $(PWD)/output:/app/output \
		amazonscraper:latest

# Install dependencies
deps:
	@echo "Installing dependencies..."
	go mod tidy

# Generate test coverage report
coverage:
	@echo "Generating coverage report..."
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	
# Show help
help:
	@echo "Available targets:"
	@echo "  all        - Run lint, test, and build"
	@echo "  build      - Build the application"
	@echo "  test       - Run tests with coverage"
	@echo "  clean      - Clean build artifacts"
	@echo "  lint       - Run linting"
	@echo "  run        - Build and run the application"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run - Run application in Docker"
	@echo "  deps       - Install dependencies"
	@echo "  coverage   - Generate test coverage report"
	@echo "  help       - Show this help" 