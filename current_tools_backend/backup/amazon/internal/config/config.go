package config

import (
	"fmt"
	"os"
	"time"
)

// Config holds application configuration
type Config struct {
	ZyteAPIKey     string
	ZyteExtractURL string
	ZyteProxyURL   string
	SearchURL      string
	MaxConcurrency int
	RequestTimeout time.Duration
	OutputJSONFile string
	OutputMDFile   string
	RateLimit      time.Duration // Time between requests for rate limiting
	RetryAttempts  int
	RetryDelay     time.Duration
}

// LoadConfig loads configuration from environment variables with defaults
func LoadConfig() *Config {
	return &Config{
		ZyteAPIKey:     getEnv("ZYTE_API_KEY", "864598b3f7e9432a8fcc7233b9dbe71d"), // Default should be removed in production
		ZyteExtractURL: getEnv("ZYTE_EXTRACT_URL", "https://api.zyte.com/v1/extract"),
		SearchURL:      getEnv("SEARCH_URL", "https://www.amazon.com/s?k=tv+samsung&crid=239JYXKBVHAW9&sprefix=tv+samsung+%2Caps%2C241&ref=nb_sb_noss_2"),
		MaxConcurrency: getEnvInt("MAX_CONCURRENCY", 20),
		RequestTimeout: getEnvDuration("REQUEST_TIMEOUT", 60*time.Second),
		OutputJSONFile: getEnv("OUTPUT_JSON_FILE", "response_with_descriptions.json"),
		OutputMDFile:   getEnv("OUTPUT_MD_FILE", "scraped_products.md"),
		RateLimit:      getEnvDuration("RATE_LIMIT", 250*time.Millisecond),
		RetryAttempts:  getEnvInt("RETRY_ATTEMPTS", 3),
		RetryDelay:     getEnvDuration("RETRY_DELAY", 500*time.Millisecond),
	}
}

// GetZyteProxyURL returns the formatted Zyte proxy URL
func (c *Config) GetZyteProxyURL() string {
	return "http://" + c.ZyteAPIKey + ":@api.zyte.com:8011"
}

// Helper functions for environment variables
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func getEnvInt(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	intValue := 0
	_, err := fmt.Sscanf(value, "%d", &intValue)
	if err != nil {
		return defaultValue
	}
	return intValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	duration, err := time.ParseDuration(value)
	if err != nil {
		return defaultValue
	}
	return duration
}
