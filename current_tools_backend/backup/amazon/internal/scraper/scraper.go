package scraper

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"github.com/PuerkitoBio/goquery"
	"github.com/wassim/amazonscraper/internal/client"
	"github.com/wassim/amazonscraper/internal/config"
	"github.com/wassim/amazonscraper/internal/types"
)

// Scraper handles product scraping operations
type Scraper struct {
	config     *config.Config
	client     *client.Client
	workersWg  sync.WaitGroup
	jobsChan   chan types.ProductJob
	resultChan chan types.ProductDetails
}

// New creates a new Scraper
func New(cfg *config.Config) *Scraper {
	// Create HTTP client with proxy for product scraping
	httpClient := client.New(cfg, true)

	return &Scraper{
		config:     cfg,
		client:     httpClient,
		jobsChan:   make(chan types.ProductJob, cfg.MaxConcurrency*2),
		resultChan: make(chan types.ProductDetails, cfg.MaxConcurrency*2),
	}
}

// FetchProductList fetches the product list from Zyte API
func (s *Scraper) FetchProductList(ctx context.Context) (map[string]interface{}, error) {
	// Create HTTP client without proxy for API calls
	apiClient := client.New(s.config, false)

	// Prepare request payload
	payload := map[string]interface{}{
		"url":         s.config.SearchURL,
		"productList": true,
		"productListOptions": map[string]interface{}{
			"extractFrom": "httpResponseBody",
		},
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", s.config.ZyteExtractURL, bytes.NewReader(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.SetBasicAuth(s.config.ZyteAPIKey, "")

	// Perform request
	resp, err := apiClient.DoRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("request error: %w", err)
	}
	defer resp.Body.Close()

	// Check for HTTP errors
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		// Use a limited buffer for error messages instead of reading the entire response
		errBuf := new(strings.Builder)
		_, _ = io.CopyN(errBuf, resp.Body, 1024) // Limit error message to 1KB
		return nil, fmt.Errorf("API error: %s\n%s", resp.Status, errBuf.String())
	}

	// Decode JSON response directly from the response body without reading it all into memory
	var result map[string]interface{}
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode JSON: %w", err)
	}

	return result, nil
}

// StartWorkers starts a pool of worker goroutines to process product scraping jobs
func (s *Scraper) StartWorkers(ctx context.Context) {
	for i := 0; i < s.config.MaxConcurrency; i++ {
		s.workersWg.Add(1)
		go s.worker(ctx)
	}
}

// worker processes jobs from the jobs channel
func (s *Scraper) worker(ctx context.Context) {
	defer s.workersWg.Done()

	for {
		select {
		case job, ok := <-s.jobsChan:
			if !ok {
				// Channel closed, exit worker
				return
			}
			// Process the job
			details := s.scrapeProductDetails(ctx, job.URL)
			job.Results <- details
		case <-ctx.Done():
			// Context canceled, exit worker
			return
		}
	}
}

// SubmitJob adds a job to the job channel
func (s *Scraper) SubmitJob(url string) {
	s.jobsChan <- types.ProductJob{
		URL:     url,
		Results: s.resultChan,
	}
}

// CloseJobs closes the jobs channel
func (s *Scraper) CloseJobs() {
	close(s.jobsChan)
}

// Wait waits for all worker goroutines to finish
func (s *Scraper) Wait() {
	s.workersWg.Wait()
	close(s.resultChan)
}

// Results returns the results channel
func (s *Scraper) Results() <-chan types.ProductDetails {
	return s.resultChan
}

// scrapeProductDetails fetches and extracts data from a product page
func (s *Scraper) scrapeProductDetails(ctx context.Context, pageURL string) types.ProductDetails {
	details := types.ProductDetails{
		URL:         pageURL,
		ProductInfo: make(map[string]string),
		Highlights:  make(map[string]string),
		Comments:    []string{},
	}

	// Validate URL
	if !strings.HasPrefix(pageURL, "http") {
		base, _ := url.Parse("https://www.amazon.com")
		rel, err := url.Parse(pageURL)
		if err != nil {
			details.ScrapeError = fmt.Sprintf("invalid relative URL: %v", err)
			return details
		}
		pageURL = base.ResolveReference(rel).String()
	}

	// Create request with context
	req, err := http.NewRequestWithContext(ctx, "GET", pageURL, nil)
	if err != nil {
		details.ScrapeError = fmt.Sprintf("failed to create request: %v", err)
		return details
	}

	// Set headers
	client.SetRandomUserAgent(req)
	client.SetRandomAcceptLanguage(req)

	// Add Amazon-specific headers that make it more likely to get product info
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8")
	req.Header.Set("Referer", "https://www.amazon.com/")

	// Perform request with retries and rate limiting
	resp, err := s.client.DoRequest(ctx, req)
	if err != nil {
		details.ScrapeError = fmt.Sprintf("request error: %v", err)
		return details
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		// Use a limited buffer for error messages instead of reading the entire response
		errBuf := new(strings.Builder)
		_, _ = io.CopyN(errBuf, resp.Body, 1024) // Limit error message to 1KB
		details.ScrapeError = fmt.Sprintf("unexpected status: %d %s - Body: %s",
			resp.StatusCode, resp.Status, errBuf.String())
		return details
	}

	// Parse the HTML document directly from the response body
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		details.ScrapeError = fmt.Sprintf("parse error: %v", err)
		return details
	}

	// Check if we're hitting a CAPTCHA or redirect page
	if doc.Find("form#captchacharacters, input#captchacharacters, .captcha-image").Length() > 0 {
		details.ScrapeError = "Amazon CAPTCHA detected - request blocked"
		return details
	}

	// Extract all product details in a single pass
	s.extractProductDetailsFast(doc, &details)

	return details
}

// extractProductDetailsFast optimizes the extraction process for speed
func (s *Scraper) extractProductDetailsFast(doc *goquery.Document, details *types.ProductDetails) {
	// Extract title with multiple fallback selectors
	titleSelectors := []string{
		"#productTitle",
		".product-title-word-break",
		"h1.a-size-large",
		"span.a-size-large.product-title-word-break",
	}

	for _, selector := range titleSelectors {
		details.Title = strings.TrimSpace(doc.Find(selector).First().Text())
		if details.Title != "" {
			break
		}
	}

	// Last resort - try to find any h1 that might contain a title
	if details.Title == "" {
		doc.Find("h1").Each(func(_ int, s *goquery.Selection) {
			if details.Title == "" {
				possibleTitle := strings.TrimSpace(s.Text())
				if len(possibleTitle) > 10 { // Assume real titles have some minimum length
					details.Title = possibleTitle
				}
			}
		})
	}

	// Fast path for price
	priceSelectors := []string{
		"#corePrice_feature_div .a-price span.a-offscreen",
		".a-price span.a-offscreen",
		"#priceblock_ourprice",
		".a-price .a-offscreen",
	}

	for _, selector := range priceSelectors {
		price := strings.TrimSpace(doc.Find(selector).First().Text())
		if price != "" {
			details.Price = price
			break
		}
	}

	// Process variants in a single pass
	doc.Find(`div[id^="inline-twister-expander"]`).Each(func(_ int, sel *goquery.Selection) {
		sz := strings.TrimSpace(sel.Find("span.inline-twister-dim-title-value").Text())
		pr := ""

		// Try multiple price selectors
		for _, priceSelector := range priceSelectors {
			pr = strings.TrimSpace(sel.Find(priceSelector).First().Text())
			if pr != "" {
				break
			}
		}

		if sz != "" && pr != "" {
			details.Variants = append(details.Variants, types.Variant{Size: sz, Price: pr})
		}
	})

	// Description bullets - quick extraction
	var bullets []string
	doc.Find("#feature-bullets ul li span.a-list-item").Each(func(_ int, sel *goquery.Selection) {
		t := strings.TrimSpace(sel.Text())
		if t != "" {
			bullets = append(bullets, t)
		}
	})
	details.Description = strings.Join(bullets, "\n")

	// Process critical tables in a controlled manner - limit the number of tables we search
	tableCount := 0

	// Product Highlights (from the summary table)
	doc.Find("table.a-normal.a-spacing-micro tr.a-spacing-small").Each(func(i int, row *goquery.Selection) {
		if tableCount > 20 {
			return
		} // Limit iterations
		tableCount++

		key := strings.TrimSpace(row.Find("td.a-span3 span.a-text-bold").Text())
		value := strings.TrimSpace(row.Find("td.a-span9 span.po-break-word").Text())
		if key != "" && value != "" {
			details.Highlights[key] = value
		}
	})

	// Technical Details Table - limit processing to 25 rows
	rowCount := 0
	doc.Find("table#productDetails_techSpec_section_1 tr").Each(func(i int, row *goquery.Selection) {
		if rowCount > 25 {
			return
		} // Limit iterations
		rowCount++

		key := strings.TrimSpace(row.Find("th").Text())
		value := strings.TrimSpace(row.Find("td").Text())
		value = strings.ReplaceAll(value, "\u200e", "") // Remove LRM
		value = strings.TrimSpace(value)
		if key != "" && value != "" {
			details.ProductInfo[key] = value
		}
	})

	// Additional Information Table - limit processing to 25 rows
	rowCount = 0
	doc.Find("table#productDetails_detailBullets_sections1 tr").Each(func(i int, row *goquery.Selection) {
		if rowCount > 25 {
			return
		} // Limit iterations
		rowCount++

		key := strings.TrimSpace(row.Find("th").Text())
		if key == "Customer Reviews" {
			rating := strings.TrimSpace(row.Find("span.a-declarative span.a-size-base.a-color-base").First().Text())
			numRatings := strings.TrimSpace(row.Find("span#acrCustomerReviewText").Text())
			if rating != "" && numRatings != "" {
				details.ProductInfo[key] = fmt.Sprintf("%s out of 5 stars (%s)", rating, numRatings)
			}
		} else {
			value := strings.TrimSpace(row.Find("td").Text())
			if key != "" && value != "" {
				details.ProductInfo[key] = value
			}
		}
	})

	// Comments/Reviews - process all available reviews
	doc.Find(`span[data-hook="review-body"]`).Each(func(i int, s *goquery.Selection) {
		comment := strings.TrimSpace(s.Text())
		comment = strings.TrimSuffix(comment, "Read more")
		comment = strings.TrimSpace(comment)
		if comment != "" {
			details.Comments = append(details.Comments, comment)
		}
	})

	// If title is still empty, try to get it from the URL as last resort
	if details.Title == "" {
		urlParts := strings.Split(details.URL, "/")
		if len(urlParts) > 0 {
			lastPart := urlParts[len(urlParts)-1]
			// Convert URL-friendly format to readable text
			lastPart = strings.ReplaceAll(lastPart, "-", " ")
			lastPart = strings.Title(strings.ToLower(lastPart))
			if len(lastPart) > 5 { // Check if it's substantial enough to be a title
				details.Title = "[Derived from URL] " + lastPart
			}
		}
	}
}
