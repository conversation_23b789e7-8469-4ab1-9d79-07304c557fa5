package filter

import (
	"testing"
)

func TestFilterByProbability(t *testing.T) {
	// Create test data with probability values
	testObjects := []map[string]interface{}{
		{
			"url": "https://example.com/1",
			"metadata": map[string]interface{}{
				"probability": 0.9,
			},
		},
		{
			"url": "https://example.com/2",
			"metadata": map[string]interface{}{
				"probability": 0.8,
			},
		},
		{
			"url": "https://example.com/3",
			"metadata": map[string]interface{}{
				"probability": 0.7,
			},
		},
		{
			"url": "https://example.com/4",
			"metadata": map[string]interface{}{
				"probability": 0.1, // Low probability outlier
			},
		},
	}

	// Create filter with threshold 0.5 standard deviations
	filter := NewStatsFilter(0.5)
	kept, filtered := filter.FilterByProbability(testObjects)

	// Expected outcomes
	if len(kept) != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 kept objects, got %d", len(kept))
	}

	if len(filtered) != 1 {
		t.<PERSON><PERSON><PERSON>("Expected 1 filtered object, got %d", len(filtered))
	}

	// The filtered object should be the low probability one
	if len(filtered) > 0 {
		url, ok := filtered[0]["url"].(string)
		if !ok || url != "https://example.com/4" {
			t.Errorf("Expected filtered object to be the low probability one, got %v", filtered[0])
		}
	}
}

func TestFindProbabilityObjects(t *testing.T) {
	// Create a nested structure similar to the API response
	testData := map[string]interface{}{
		"productList": map[string]interface{}{
			"products": []interface{}{
				map[string]interface{}{
					"url": "https://example.com/1",
					"metadata": map[string]interface{}{
						"probability": 0.9,
					},
				},
				map[string]interface{}{
					"url": "https://example.com/2",
					"metadata": map[string]interface{}{
						"probability": 0.8,
					},
				},
				// Object without url
				map[string]interface{}{
					"metadata": map[string]interface{}{
						"probability": 0.7,
					},
				},
				// Object without probability
				map[string]interface{}{
					"url": "https://example.com/4",
				},
			},
		},
	}

	objects := FindProbabilityObjects(testData)

	// Expected outcome: 2 objects with both URL and probability
	if len(objects) != 2 {
		t.Errorf("Expected 2 objects with probability, got %d", len(objects))
	}

	// Check that the correct objects were found
	expectedURLs := []string{"https://example.com/1", "https://example.com/2"}
	for i, obj := range objects {
		url, ok := obj["url"].(string)
		if !ok || url != expectedURLs[i] {
			t.Errorf("Expected URL %s, got %v", expectedURLs[i], url)
		}
	}
}

func TestCalculateStats(t *testing.T) {
	// Test with normal data
	values := []float64{1.0, 2.0, 3.0, 4.0, 5.0}
	mean, sd := calculateStats(values)

	// Expected: mean=3.0, sd=1.414...
	if mean != 3.0 {
		t.Errorf("Expected mean 3.0, got %f", mean)
	}

	// SD should be close to sqrt(2) = 1.414...
	expectedSD := 1.4142135623730951
	if !almostEqual(sd, expectedSD, 0.0001) {
		t.Errorf("Expected SD close to %f, got %f", expectedSD, sd)
	}

	// Test with empty data
	emptyValues := []float64{}
	mean, sd = calculateStats(emptyValues)
	if mean != 0.0 || sd != 0.0 {
		t.Errorf("Expected mean and SD to be 0.0 for empty data, got %f, %f", mean, sd)
	}
}

// Helper function to compare floating point numbers
func almostEqual(a, b, tolerance float64) bool {
	return a-b < tolerance && b-a < tolerance
}
