package filter

import (
	"math"
)

// StatsFilter provides functions for statistical filtering
type Stats<PERSON>ilter struct {
	ThresholdSigma float64 // Number of standard deviations below mean to use as threshold
}

// NewStatsFilter creates a new statistical filter
func NewStatsFilter(thresholdSigma float64) *StatsFilter {
	return &StatsFilter{
		ThresholdSigma: thresholdSigma,
	}
}

// FilterByProbability filters objects based on their probability values
// using statistical analysis (mean and standard deviation)
func (f *StatsFilter) FilterByProbability(objects []map[string]interface{}) (kept, filtered []map[string]interface{}) {
	if len(objects) == 0 {
		return nil, nil
	}

	// Extract probabilities
	probs := make([]float64, len(objects))
	for i, obj := range objects {
		if metadataVal, ok := obj["metadata"].(map[string]interface{}); ok {
			if p, ok := metadataVal["probability"].(float64); ok {
				probs[i] = p
			} else {
				probs[i] = 0
			}
		} else {
			probs[i] = 0
		}
	}

	// Calculate statistics
	mean, sd := calculateStats(probs)
	threshold := mean - f.ThresholdSigma*sd

	// Filter objects
	for i, obj := range objects {
		if probs[i] < threshold {
			filtered = append(filtered, obj)
		} else {
			kept = append(kept, obj)
		}
	}

	return kept, filtered
}

// FindProbabilityObjects extracts objects that have probability values
// from a nested structure (usually the API response)
func FindProbabilityObjects(v interface{}) []map[string]interface{} {
	var result []map[string]interface{}
	findProbObjectsRecursive(v, &result)
	return result
}

// findProbObjectsRecursive recursively collects objects with probability values
func findProbObjectsRecursive(v interface{}, out *[]map[string]interface{}) {
	switch x := v.(type) {
	case map[string]interface{}:
		// Check if this map contains the 'productList' key
		if productListVal, plOK := x["productList"]; plOK {
			if productListMap, plmOK := productListVal.(map[string]interface{}); plmOK {
				// Check if 'productList' contains the 'products' key (which should be an array)
				if productsVal, pOK := productListMap["products"]; pOK {
					if productsArray, paOK := productsVal.([]interface{}); paOK {
						// Iterate through the products array
						for _, productInterface := range productsArray {
							if productMap, pmOK := productInterface.(map[string]interface{}); pmOK {
								// Check for URL at the top level of the product map
								_, urlOK := productMap["url"].(string)
								// Check for metadata and probability within it
								metadataVal, metaOK := productMap["metadata"]
								probOK := false
								if metaOK {
									if metadataMap, mmOK := metadataVal.(map[string]interface{}); mmOK {
										_, probOK = metadataMap["probability"].(float64)
									}
								}

								// If both URL and probability (within metadata) exist, add the product map
								if urlOK && probOK {
									*out = append(*out, productMap)
								}
							}
						}
						// We found the products array, no need to recurse further down this branch
						return
					}
				}
			}
		}
		// If we didn't find productList/products, continue searching recursively
		for _, child := range x {
			findProbObjectsRecursive(child, out)
		}
	case []interface{}:
		// If the top level is an array, search within its elements
		for _, elem := range x {
			findProbObjectsRecursive(elem, out)
		}
	}
}

// calculateStats computes mean and standard deviation of a slice
func calculateStats(vals []float64) (mean, sd float64) {
	if len(vals) == 0 {
		return 0, 0 // Avoid division by zero
	}

	for _, v := range vals {
		mean += v
	}
	mean /= float64(len(vals))

	var sumSq float64
	for _, v := range vals {
		diff := v - mean
		sumSq += diff * diff
	}
	variance := sumSq / float64(len(vals))
	sd = math.Sqrt(variance)

	return mean, sd
}
