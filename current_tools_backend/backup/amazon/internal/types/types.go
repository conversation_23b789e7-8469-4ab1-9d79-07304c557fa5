package types

// Variant represents a size/price combination for a product
type Variant struct {
	Size  string `json:"size"`
	Price string `json:"price"`
}

// ProductDetails holds all extracted product information
type ProductDetails struct {
	URL         string            `json:"url"`
	Title       string            `json:"title"`
	Price       string            `json:"price,omitempty"` // For single price fallback
	Variants    []Variant         `json:"variants,omitempty"`
	Description string            `json:"description"`
	ProductInfo map[string]string `json:"productInfo"`
	Highlights  map[string]string `json:"highlights"`
	Comments    []string          `json:"comments"`
	ScrapeError string            `json:"scrapeError,omitempty"` // To capture scraping errors
}

// ExtractResponse represents the response from Zyte API
type ExtractResponse struct {
	ProductList map[string]interface{} `json:"productList"`
	URL         string                 `json:"url"`
	// Add more fields as needed
}

// ProductJob represents a job for the worker pool
type ProductJob struct {
	URL     string
	Results chan<- ProductDetails
}

// ScrapingStats holds statistics about the scraping process
type ScrapingStats struct {
	Total         int
	Success       int
	Errors        int
	TotalDuration float64
	AvgDuration   float64
}
