package client

import (
	"context"
	"crypto/tls"
	"math/rand"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"time"

	"github.com/wassim/amazonscraper/internal/config"
)

// Client represents a reusable HTTP client with Zyte proxy support
type Client struct {
	httpClient   *http.Client
	proxyEnabled bool
	rateLimiter  *time.Ticker
	config       *config.Config
}

// New creates a new HTTP client with optional Zyte proxy
func New(cfg *config.Config, useProxy bool) *Client {
	// Create a cookie jar to handle session cookies
	jar, _ := cookiejar.New(nil)

	var transport *http.Transport

	if useProxy {
		proxyURL, _ := url.Parse(cfg.GetZyteProxyURL())
		transport = &http.Transport{
			Proxy:           http.ProxyURL(proxyURL),
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		}
	} else {
		transport = &http.Transport{}
	}

	// Set optimized connection parameters for performance
	transport.MaxIdleConns = 100
	transport.MaxConnsPerHost = 30
	transport.MaxIdleConnsPerHost = 30
	transport.IdleConnTimeout = 90 * time.Second
	transport.TLSHandshakeTimeout = 10 * time.Second
	transport.ExpectContinueTimeout = 1 * time.Second

	// Enable keepalives for connection reuse
	transport.DisableKeepAlives = false

	httpClient := &http.Client{
		Transport: transport,
		Timeout:   cfg.RequestTimeout,
		Jar:       jar, // Add cookie jar for session handling
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// Allow up to 10 redirects
			if len(via) >= 10 {
				return http.ErrUseLastResponse
			}
			// Preserve headers across redirects
			for key, val := range via[0].Header {
				if _, ok := req.Header[key]; !ok {
					req.Header[key] = val
				}
			}
			return nil
		},
	}

	return &Client{
		httpClient:   httpClient,
		proxyEnabled: useProxy,
		rateLimiter:  time.NewTicker(cfg.RateLimit),
		config:       cfg,
	}
}

// DoRequest performs an HTTP request with rate limiting and retries
func (c *Client) DoRequest(ctx context.Context, req *http.Request) (*http.Response, error) {
	// Wait for rate limiter
	select {
	case <-c.rateLimiter.C:
		// Proceed with request
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	var resp *http.Response
	var err error

	// Optimized retry logic
	for attempt := 0; attempt <= c.config.RetryAttempts; attempt++ {
		// Clone the request to ensure it can be reused safely
		reqClone := req.Clone(ctx)

		// Add cache-control header to look more like browser request
		reqClone.Header.Set("Cache-Control", "max-age=0")

		// Add conventional accept header
		if reqClone.Header.Get("Accept") == "" {
			reqClone.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
		}

		// Execute the request
		resp, err = c.httpClient.Do(reqClone)

		// If successful or context canceled, return immediately
		if err == nil || ctx.Err() != nil {
			break
		}

		// Wait before retrying, but respect context cancellation
		select {
		case <-time.After(c.config.RetryDelay):
			// Continue to retry
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	return resp, err
}

// SetRandomUserAgent sets a random user agent on the request
func SetRandomUserAgent(req *http.Request) {
	req.Header.Set("User-Agent", randomUserAgent())
}

// SetRandomAcceptLanguage sets a random accept language on the request
func SetRandomAcceptLanguage(req *http.Request) {
	req.Header.Set("Accept-Language", randomAcceptLanguage())
}

// randomUserAgent returns a random browser User-Agent
func randomUserAgent() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	agents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Safari/605.1.15",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0",
	}
	return agents[r.Intn(len(agents))]
}

// randomAcceptLanguage returns a random Accept-Language header
func randomAcceptLanguage() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	langs := []string{"en-US,en;q=0.9", "en-GB,en;q=0.8"}
	return langs[r.Intn(len(langs))]
}
