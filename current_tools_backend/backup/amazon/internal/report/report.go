package report

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"strings"
	"time"

	"github.com/wassim/amazonscraper/internal/types"
)

// Generator handles report generation
type Generator struct {
	jsonOutputPath string
	mdOutputPath   string
}

// New creates a new report generator
func New(jsonPath, mdPath string) *Generator {
	return &Generator{
		jsonOutputPath: jsonPath,
		mdOutputPath:   mdPath,
	}
}

// SaveJSON saves a JSON report file
func (g *Generator) SaveJSON(data interface{}) error {
	jsonBytes, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	if err := ioutil.WriteFile(g.jsonOutputPath, jsonBytes, 0644); err != nil {
		return fmt.Errorf("failed to write JSON file: %w", err)
	}

	return nil
}

// SaveMarkdown generates and saves a Markdown report from product details
func (g *Generator) SaveMarkdown(products []types.ProductDetails, stats types.ScrapingStats) error {
	var mdBuilder strings.Builder

	// Header
	mdBuilder.WriteString(fmt.Sprintf("# Scraped Product Details (%s)\n\n", time.Now().Format(time.RFC1123)))
	mdBuilder.WriteString(fmt.Sprintf("Successfully scraped: %d | Errors: %d\n\n", stats.Success, stats.Errors))

	if stats.Total > 0 {
		mdBuilder.WriteString(fmt.Sprintf("Average scrape time: %.2f seconds\n\n", stats.AvgDuration))
	}

	// Products
	for _, details := range products {
		if details.ScrapeError != "" {
			mdBuilder.WriteString(fmt.Sprintf("## ERROR Scraping: %s\n", details.URL))
			mdBuilder.WriteString(fmt.Sprintf("**Error:** `%s`\n\n---\n\n", details.ScrapeError))
			continue
		}

		mdBuilder.WriteString(fmt.Sprintf("## %s\n\n", details.Title))
		mdBuilder.WriteString(fmt.Sprintf("**URL:** [%s](%s)\n\n", details.URL, details.URL))

		if details.Price != "" {
			mdBuilder.WriteString(fmt.Sprintf("**Price:** %s\n\n", details.Price))
		}

		if len(details.Variants) > 0 {
			mdBuilder.WriteString("**Variants:**\n")
			for _, v := range details.Variants {
				mdBuilder.WriteString(fmt.Sprintf("- Size: %s, Price: %s\n", v.Size, v.Price))
			}
			mdBuilder.WriteString("\n")
		}

		mdBuilder.WriteString("**Description:**\n")
		mdBuilder.WriteString(fmt.Sprintf("```\n%s\n```\n\n", details.Description))

		if len(details.Highlights) > 0 {
			mdBuilder.WriteString("**Highlights:**\n")
			for k, v := range details.Highlights {
				mdBuilder.WriteString(fmt.Sprintf("- **%s:** %s\n", k, v))
			}
			mdBuilder.WriteString("\n")
		}

		if len(details.ProductInfo) > 0 {
			mdBuilder.WriteString("**Product Information:**\n")
			for k, v := range details.ProductInfo {
				mdBuilder.WriteString(fmt.Sprintf("- **%s:** %s\n", k, v))
			}
			mdBuilder.WriteString("\n")
		}

		mdBuilder.WriteString("**Comments:**\n")
		if len(details.Comments) > 0 {
			for i, comment := range details.Comments {
				mdBuilder.WriteString(fmt.Sprintf("%d. ```\n%s\n```\n\n", i+1, comment))
			}
		} else {
			mdBuilder.WriteString("_(No comments found)_\n\n")
		}

		mdBuilder.WriteString("---\n\n") // Separator between products
	}

	if err := ioutil.WriteFile(g.mdOutputPath, []byte(mdBuilder.String()), 0644); err != nil {
		return fmt.Errorf("failed to write Markdown file: %w", err)
	}

	return nil
}
