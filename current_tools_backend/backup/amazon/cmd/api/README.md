# Amazon Product Scraper API

This is a RESTful API for the Amazon Product Scraper, allowing you to search for products on Amazon and retrieve detailed information through a simple HTTP interface.

## Features

- Search for products on Amazon by keyword
- Retrieve detailed product information including pricing, variants, descriptions, and highlights
- Configure maximum number of results to return
- Set custom Zyte API key for web scraping
- Control the statistical threshold for filtering products
- Multiple API access methods (POST with JSON body or GET with URL parameters)

## Installation

### Prerequisites

- Go 1.16 or higher
- Gin web framework

```bash
go get -u github.com/gin-gonic/gin
```

### Setup

You'll need to update the Go module dependencies:

```bash
cd /path/to/amazon_search_final
go mod tidy
```

## Running the API Server

To run the API server:

```bash
cd /path/to/amazon_search_final
go run cmd/api/main.go
```

The server will start on port 8080 by default. You can set a custom port using the `PORT` environment variable.

## API Endpoints

### GET /

Root endpoint that returns basic API information.

**Example Response:**
```json
{
  "message": "Amazon Product Scraper API",
  "endpoints": [
    "/scrape",
    "/search"
  ]
}
```

### POST /scrape

Performs a product search on Amazon and returns the results. Accepts a JSON body with parameters.

**Request Parameters:**
- `query` (string, required): The search query for Amazon products
- `max_results` (integer, optional): Maximum number of results to return (default: 10)
- `zyte_api_key` (string, optional): API key for Zyte proxy service
- `stats_threshold` (float, optional): Statistical threshold for filtering products (default: 1.0)

**Notes:**
- The API key can also be provided as a query parameter: `/scrape?zyte_api_key=your_key`
- Query parameters take precedence over the JSON body

**Example Request:**
```json
{
  "query": "samsung tv 4k 55 inch",
  "max_results": 5,
  "zyte_api_key": "your_zyte_api_key",
  "stats_threshold": 1.0
}
```

### GET /search

A more convenient endpoint that allows searching with URL parameters only.

**Request Parameters (all as URL query parameters):**
- `query` (string, required): The search query for Amazon products
- `max_results` (integer, optional): Maximum number of results to return (default: 10)
- `zyte_api_key` (string, optional): API key for Zyte proxy service
- `stats_threshold` (float, optional): Statistical threshold for filtering products (default: 1.0)

**Example URL:**
```
/search?query=samsung+tv+4k+55+inch&max_results=5&zyte_api_key=your_key
```

### Response Format

Both endpoints return the same JSON response structure:

```json
{
  "query": "samsung tv 4k 55 inch",
  "products": [
    {
      "url": "https://www.amazon.com/...",
      "title": "SAMSUNG 55-Inch Class Crystal UHD AU8000 Series...",
      "price": "$447.99",
      "variants": [
        {
          "size": "50-Inch",
          "price": "$377.99"
        },
        {
          "size": "55-Inch",
          "price": "$447.99"
        }
      ],
      "description": "Crystal Processor 4K...",
      "productInfo": {
        "Brand": "SAMSUNG",
        "Screen Size": "55 Inches",
        "Resolution": "4K"
      },
      "highlights": {
        "About this item": "CRYSTAL PROCESSOR 4K: Intelligent, powerful..."
      },
      "comments": [
        "Free returns",
        "For Prime members"
      ]
    }
  ],
  "stats": {
    "Total": 5,
    "Success": 5,
    "Errors": 0,
    "TotalDuration": 15.34,
    "AvgDuration": 3.07
  }
}
```

## Testing the API

A Python test script is included to test the API endpoints. To run it:

```bash
# Make sure the API server is running in a separate terminal

# Test with POST method (default)
python cmd/api/test_api.py

# Test with GET method
python cmd/api/test_api.py --method get

# Custom query and API key
python cmd/api/test_api.py --query "iphone 14 pro" --api-key "your_key" --max-results 3
```

### Using Environment Variables

You can set your Zyte API key as an environment variable before running the test:

```bash
export ZYTE_API_KEY="your_zyte_api_key"
python cmd/api/test_api.py
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

- 400 Bad Request: Invalid request parameters
- 500 Internal Server Error: Server-side errors

## Example Usage with cURL

```bash
# Test the root endpoint
curl http://localhost:8080

# POST method with JSON body
curl -X POST http://localhost:8080/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "query": "samsung tv 4k 55 inch",
    "max_results": 5
  }'

# POST method with API key in URL
curl -X POST http://localhost:8080/scrape?zyte_api_key=your_key \
  -H "Content-Type: application/json" \
  -d '{
    "query": "samsung tv 4k 55 inch",
    "max_results": 5
  }'

# GET method with all parameters in URL
curl "http://localhost:8080/search?query=samsung+tv+4k+55+inch&max_results=5&zyte_api_key=your_key"
```

## Notes

- The API requires a Zyte API key for web scraping. You can provide your own key via:
  - JSON body in POST requests
  - URL query parameter in both POST and GET requests
  - Environment variable in the server
- Scraping may take some time depending on the number of products and network conditions.
- The `stats_threshold` parameter controls how strict the filtering is (higher values = stricter filtering). 