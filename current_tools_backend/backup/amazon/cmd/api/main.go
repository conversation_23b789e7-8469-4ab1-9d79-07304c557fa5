package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/wassim/amazonscraper/internal/config"
	"github.com/wassim/amazonscraper/internal/filter"
	"github.com/wassim/amazonscraper/internal/report"
	"github.com/wassim/amazonscraper/internal/scraper"
	"github.com/wassim/amazonscraper/internal/types"
)

// ScraperRequest defines the structure for the API request
type ScraperRequest struct {
	Query          string  `json:"query" binding:"required"`
	MaxResults     int     `json:"max_results"`
	ZyteAPIKey     string  `json:"zyte_api_key"`
	StatsThreshold float64 `json:"stats_threshold"`
}

// ScraperResponse defines the structure for the API response
type ScraperResponse struct {
	Query    string                 `json:"query"`
	Products []types.ProductDetails `json:"products"`
	Stats    types.ScrapingStats    `json:"stats"`
}

func main() {
	// Set up logging
	logFile, err := os.OpenFile("api.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		log.SetOutput(logFile)
	}
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("Starting Amazon Scraper API")

	// Setup Gin router
	router := gin.Default()

	// Add CORS middleware
	router.Use(func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// Define routes
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Amazon Product Scraper API",
			"endpoints": []string{
				"/scrape",
				"/search",
			},
			"payload_formats": gin.H{
				"/scrape": gin.H{
					"method": "POST",
					"content_type": "application/json",
					"payload": gin.H{
						"query": "string (required) - The search query for Amazon products",
						"max_results": "integer (optional) - Maximum number of results to return (default: 10)",
						"zyte_api_key": "string (optional) - API key for Zyte proxy service",
						"stats_threshold": "float (optional) - Statistical threshold for filtering products (default: 1.0)",
					},
					"example": gin.H{
						"query": "samsung tv 4k 55 inch",
						"max_results": 5,
					},
				},
				"/search": gin.H{
					"method": "GET",
					"query_parameters": gin.H{
						"query": "string (required) - The search query for Amazon products",
						"max_results": "integer (optional) - Maximum number of results to return (default: 10)",
						"zyte_api_key": "string (optional) - API key for Zyte proxy service",
						"stats_threshold": "float (optional) - Statistical threshold for filtering products (default: 1.0)",
					},
					"example": "/search?query=samsung+tv+4k+55+inch&max_results=5",
				},
			},
		})
	})

	// Scraper endpoint
	router.POST("/scrape", func(c *gin.Context) {
		var req ScraperRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		// Check for API key in query parameters (takes precedence over body)
		queryAPIKey := c.Query("zyte_api_key")
		if queryAPIKey != "" {
			req.ZyteAPIKey = queryAPIKey
			log.Println("Using Zyte API key from query parameter")
		}

		// Validate query
		if strings.TrimSpace(req.Query) == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Search query cannot be empty"})
			return
		}

		// Apply default values if needed
		if req.MaxResults <= 0 {
			req.MaxResults = 10
		}

		statsThreshold := 1.0
		if req.StatsThreshold > 0 {
			statsThreshold = req.StatsThreshold
		}

		// Create a cancelable context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 90*time.Second)
		defer cancel()

		// Run the scraper with the request parameters
		result, err := runScraper(ctx, req.Query, req.ZyteAPIKey, req.MaxResults, statsThreshold)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	})

	// GET search endpoint with query parameters
	router.GET("/search", func(c *gin.Context) {
		// Extract parameters from URL query
		query := c.Query("query")
		maxResultsStr := c.DefaultQuery("max_results", "10")
		zyteAPIKey := c.Query("zyte_api_key")
		statsThresholdStr := c.DefaultQuery("stats_threshold", "1.0")

		// Validate query
		if strings.TrimSpace(query) == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Search query cannot be empty (use ?query=your+search+terms)"})
			return
		}

		// Parse integer parameters
		maxResults := 10
		fmt.Sscanf(maxResultsStr, "%d", &maxResults)
		if maxResults <= 0 {
			maxResults = 10
		}

		// Parse float parameters
		statsThreshold := 1.0
		fmt.Sscanf(statsThresholdStr, "%f", &statsThreshold)
		if statsThreshold <= 0 {
			statsThreshold = 1.0
		}

		log.Printf("GET search request: query=%s, max_results=%d, stats_threshold=%.1f",
			query, maxResults, statsThreshold)

		// Create a cancelable context
		ctx, cancel := context.WithTimeout(c.Request.Context(), 90*time.Second)
		defer cancel()

		// Run the scraper with the parameters
		result, err := runScraper(ctx, query, zyteAPIKey, maxResults, statsThreshold)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, result)
	})

	// Start the server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	// Create a server with graceful shutdown
	srv := &http.Server{
		Addr:    ":" + port,
		Handler: router,
	}

	// Handle graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		log.Printf("Server starting on port %s\n", port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("listen: %s\n", err)
		}
	}()

	// Wait for interrupt signal
	<-quit
	log.Println("Shutting down server...")

	// Create shutdown context with 5 second timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := srv.Shutdown(shutdownCtx); err != nil {
		log.Fatal("Server forced to shutdown:", err)
	}

	log.Println("Server exiting")
}

// runScraper runs the Amazon scraper with the given parameters
func runScraper(ctx context.Context, searchQuery, zyteAPIKey string, maxResults int, statsThreshold float64) (*ScraperResponse, error) {
	// Set search query in environment
	encodedQuery := url.QueryEscape(searchQuery)
	baseURL := "https://www.amazon.com/s?k="
	os.Setenv("SEARCH_URL", baseURL+encodedQuery)
	log.Printf("Search URL set to: %s", baseURL+encodedQuery)

	// Set custom API key if provided
	if zyteAPIKey != "" {
		os.Setenv("ZYTE_API_KEY", zyteAPIKey)
		log.Println("Using custom Zyte API key")
	}

	// Set optimized settings
	os.Setenv("MAX_CONCURRENCY", "15")
	os.Setenv("RATE_LIMIT", "300ms")
	os.Setenv("RETRY_DELAY", "250ms")

	// Load configuration
	cfg := config.LoadConfig()
	log.Printf("Configuration loaded. Max concurrency: %d", cfg.MaxConcurrency)

	// Create scraper
	amazonScraper := scraper.New(cfg)

	// Fetch product list from Zyte API
	log.Println("Fetching product list from Zyte API...")
	apiResponse, err := amazonScraper.FetchProductList(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch product list: %w", err)
	}
	log.Println("Product list fetched successfully")

	// Find objects with probability values
	probObjects := filter.FindProbabilityObjects(apiResponse)
	log.Printf("Found %d objects with probability values", len(probObjects))

	if len(probObjects) == 0 {
		return nil, fmt.Errorf("no objects with probability values found, nothing to scrape")
	}

	// Filter objects based on probability
	statsFilter := filter.NewStatsFilter(statsThreshold)
	kept, filtered := statsFilter.FilterByProbability(probObjects)
	log.Printf("Kept %d objects, filtered out %d objects", len(kept), len(filtered))

	if len(kept) == 0 {
		return nil, fmt.Errorf("no objects passed the probability filter, nothing to scrape")
	}

	// Limit the number of products to scrape
	if maxResults > 0 && len(kept) > maxResults {
		kept = kept[:maxResults]
		log.Printf("Limited to %d products as requested", maxResults)
	}

	// --- Scrape Products ---
	log.Printf("Starting parallel scraping of %d products with %d workers...", len(kept), cfg.MaxConcurrency)

	// Start worker pool
	amazonScraper.StartWorkers(ctx)

	// Submit all jobs quickly
	for _, obj := range kept {
		productURL, ok := obj["url"].(string)
		if !ok {
			log.Printf("Skipping object without a valid URL: %v", obj)
			continue
		}
		amazonScraper.SubmitJob(productURL)
	}

	// Signal that no more jobs will be submitted
	amazonScraper.CloseJobs()

	// --- Collect Results ---
	var scrapedProducts []types.ProductDetails
	var stats types.ScrapingStats

	stats.Total = len(kept)
	startTime := time.Now()

	// Create a WaitGroup to wait for the result collection to complete
	var wg sync.WaitGroup
	wg.Add(1)

	// Collect results in a separate goroutine
	go func() {
		defer wg.Done()

		// Process each scraped product as it completes
		for details := range amazonScraper.Results() {
			if details.ScrapeError != "" {
				log.Printf("Error scraping %s: %s", details.URL, details.ScrapeError)
				stats.Errors++
			} else {
				log.Printf("Successfully scraped: %s", details.Title)
				stats.Success++
			}

			scrapedProducts = append(scrapedProducts, details)
		}
	}()

	// Wait for all products to be scraped and processed
	amazonScraper.Wait()
	wg.Wait()

	// Calculate timing statistics
	totalTime := time.Since(startTime).Seconds()
	stats.TotalDuration = totalTime
	if stats.Total > 0 {
		stats.AvgDuration = totalTime / float64(stats.Total)
	}

	log.Printf("Scraping completed. Success: %d, Errors: %d", stats.Success, stats.Errors)
	log.Printf("Total time: %.2f seconds, Average time per product: %.2f seconds",
		stats.TotalDuration, stats.AvgDuration)

	// Save JSON data for debugging (optional)
	reporter := report.New("api_response.json", "api_products.md")
	if err := reporter.SaveJSON(apiResponse); err != nil {
		log.Printf("Failed to save JSON: %v", err)
	}

	// Create response
	response := &ScraperResponse{
		Query:    searchQuery,
		Products: scrapedProducts,
		Stats:    stats,
	}

	return response, nil
}
