#!/usr/bin/env python3
"""
Python client for testing the Amazon Product Scraper API.
"""

import os
import json
import time
import requests
from datetime import datetime
import argparse

def main():
    """Test the Amazon Product Scraper API"""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test the Amazon Product Scraper API')
    parser.add_argument('--method', choices=['post', 'get'], default='post',
                      help='HTTP method to use (post or get)')
    parser.add_argument('--query', default='samsung tv 4k 55 inch',
                      help='Search query for Amazon products')
    parser.add_argument('--max-results', type=int, default=5,
                      help='Maximum number of results to return')
    parser.add_argument('--api-key', default='',
                      help='Zyte API key (overrides environment variable)')
    args = parser.parse_args()
    
    print("Amazon Product Scraper API Test")
    print("===============================")

    # Get API key from environment variable or command line
    zyte_api_key = args.api_key or os.environ.get("ZYTE_API_KEY", "")
    
    if zyte_api_key:
        print("Using Zyte API key")

    # Test endpoints
    api_url = "http://localhost:8080"

    # First test the root endpoint to check if API is up
    print("\nTesting root endpoint...")
    try:
        response = requests.get(api_url)
        response.raise_for_status()
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure the API server is running with:")
        print("go run cmd/api/main.go")
        return

    print(f"Status: {response.status_code} {response.reason}")
    print(f"Response: {response.text}")

    # Test scraper endpoint using selected method
    print(f"\nTesting scraper endpoint using {args.method.upper()} method...")
    
    if args.method == 'post':
        # POST method using JSON body
        request_data = {
            "query": args.query,
            "max_results": args.max_results,
            "zyte_api_key": zyte_api_key,
            "stats_threshold": 1.0
        }

        print(f"Sending search request for: {request_data['query']}")
        print("This may take a minute or two to complete...")
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{api_url}/scrape",
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=120  # Set a long timeout as scraping takes time
            )
            response.raise_for_status()
        except Exception as e:
            print(f"Error: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Error response: {e.response.text}")
            return
    else:
        # GET method using URL parameters
        print(f"Sending search request for: {args.query}")
        print("This may take a minute or two to complete...")
        
        params = {
            "query": args.query,
            "max_results": args.max_results,
            "stats_threshold": 1.0
        }
        
        # Add API key to parameters if provided
        if zyte_api_key:
            params["zyte_api_key"] = zyte_api_key
            
        start_time = time.time()
        try:
            response = requests.get(
                f"{api_url}/search",
                params=params,
                timeout=120  # Set a long timeout as scraping takes time
            )
            response.raise_for_status()
        except Exception as e:
            print(f"Error: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"Error response: {e.response.text}")
            return

    elapsed_time = time.time() - start_time
    print(f"Scraping completed in {elapsed_time:.2f} seconds")
    print(f"Status: {response.status_code} {response.reason}")

    # Pretty print the JSON response
    try:
        response_json = response.json()
        
        # Print summary
        print(f"\nScraping results summary:")
        print(f"Query: {response_json.get('query', 'N/A')}")
        products = response_json.get('products', [])
        print(f"Products found: {len(products)}")
        
        stats = response_json.get('stats', {})
        print(f"Success: {stats.get('Success', 0)}")
        print(f"Errors: {stats.get('Errors', 0)}")
        print(f"Total time: {stats.get('TotalDuration', 0):.2f} seconds")
        
        # Print first product details as sample
        if products:
            print("\nSample product:")
            sample = products[0]
            print(f"Title: {sample.get('title', 'N/A')}")
            print(f"Price: {sample.get('price', 'N/A')}")
            print(f"URL: {sample.get('url', 'N/A')}")
            
            # Print variants if available
            variants = sample.get('variants', [])
            if variants:
                print(f"Variants: {len(variants)}")
                for i, v in enumerate(variants[:3]):  # Show first 3 variants
                    print(f"  {i+1}. Size: {v.get('size', 'N/A')}, Price: {v.get('price', 'N/A')}")
                if len(variants) > 3:
                    print(f"  ... and {len(variants) - 3} more")
            
            # Print highlights if available
            highlights = sample.get('highlights', {})
            if highlights:
                print("Highlights:")
                for k, v in list(highlights.items())[:3]:  # Show first 3 highlights
                    print(f"  {k}: {v}")
                if len(highlights) > 3:
                    print(f"  ... and {len(highlights) - 3} more")
        
        # Ask if user wants to see full JSON
        choice = input("\nDo you want to see the full JSON response? (y/n): ")
        if choice.lower() == 'y':
            pretty_json = json.dumps(response_json, indent=2)
            print(f"\nFull Response:\n{pretty_json}")
            
    except json.JSONDecodeError:
        print(f"Error formatting JSON. Raw response: {response.text}")

if __name__ == "__main__":
    main() 