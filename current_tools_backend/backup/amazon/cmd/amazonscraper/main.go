/*
Amazon Product Scraper
----------------------
This tool is designed for educational purposes to demonstrate web scraping techniques.
It collects product information from Amazon search results in a respectful manner.
*/

package main

import (
	"bufio"
	"context"
	"fmt"
	"log"
	"net/url"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/wassim/amazonscraper/internal/config"
	"github.com/wassim/amazonscraper/internal/filter"
	"github.com/wassim/amazonscraper/internal/report"
	"github.com/wassim/amazonscraper/internal/scraper"
	"github.com/wassim/amazonscraper/internal/types"
)

const (
	// Statistical filter threshold (number of standard deviations below mean)
	statsFilterThreshold = 1
)

func main() {
	// Set up logging to file instead of just console
	logFile, err := os.OpenFile("scraper.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err == nil {
		log.SetOutput(logFile)
	}
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("Starting Amazon Scraper")

	// Prompt for search query
	fmt.Print("Enter search query for Amazon: ")
	reader := bufio.NewReader(os.Stdin)
	searchQuery, err := reader.ReadString('\n')
	if err != nil {
		log.Fatalf("Error reading input: %v", err)
	}

	// Clean the search query and prepare it for URL
	searchQuery = strings.TrimSpace(searchQuery)
	if searchQuery == "" {
		log.Println("Empty search query provided, using default search.")
	} else {
		// Set the search query as an environment variable for the config to use
		encodedQuery := url.QueryEscape(searchQuery)
		baseURL := "https://www.amazon.com/s?k="
		os.Setenv("SEARCH_URL", baseURL+encodedQuery)
		log.Printf("Search URL set to: %s", baseURL+encodedQuery)
	}

	// Create a cancelable context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	setupGracefulShutdown(cancel)

	// Load configuration with optimized settings
	os.Setenv("MAX_CONCURRENCY", "15") // Higher concurrency for speed
	os.Setenv("RATE_LIMIT", "300ms")   // Faster rate limit
	os.Setenv("RETRY_DELAY", "250ms")  // Shorter retry delay
	cfg := config.LoadConfig()
	log.Printf("Configuration loaded. Max concurrency: %d", cfg.MaxConcurrency)

	// Create scraper
	amazonScraper := scraper.New(cfg)

	// Fetch product list from Zyte API
	log.Println("Fetching product list from Zyte API...")
	apiResponse, err := amazonScraper.FetchProductList(ctx)
	if err != nil {
		log.Fatalf("Failed to fetch product list: %v", err)
	}
	log.Println("Product list fetched successfully")

	// Find objects with probability values
	probObjects := filter.FindProbabilityObjects(apiResponse)
	log.Printf("Found %d objects with probability values", len(probObjects))

	if len(probObjects) == 0 {
		log.Println("No objects with probability values found. Nothing to scrape.")
		return
	}

	// Filter objects based on probability
	statsFilter := filter.NewStatsFilter(statsFilterThreshold)
	kept, filtered := statsFilter.FilterByProbability(probObjects)
	log.Printf("Kept %d objects, filtered out %d objects", len(kept), len(filtered))

	if len(kept) == 0 {
		log.Println("No objects passed the probability filter. Nothing to scrape.")
		return
	}

	// --- Scrape Products ---
	log.Printf("Starting parallel scraping of %d products with %d workers...", len(kept), cfg.MaxConcurrency)

	// Start worker pool
	amazonScraper.StartWorkers(ctx)

	// Submit all jobs quickly
	for _, obj := range kept {
		productURL, ok := obj["url"].(string)
		if !ok {
			log.Printf("Skipping object without a valid URL: %v", obj)
			continue
		}
		amazonScraper.SubmitJob(productURL)
	}

	// Signal that no more jobs will be submitted
	amazonScraper.CloseJobs()

	// --- Collect Results ---
	var scrapedProducts []types.ProductDetails
	var stats types.ScrapingStats

	stats.Total = len(kept)
	startTime := time.Now()

	// Create a WaitGroup to wait for the result collection to complete
	var wg sync.WaitGroup
	wg.Add(1)

	// Collect results in a separate goroutine
	go func() {
		defer wg.Done()

		// Process each scraped product as it completes
		for details := range amazonScraper.Results() {
			if details.ScrapeError != "" {
				log.Printf("Error scraping %s: %s", details.URL, details.ScrapeError)
				stats.Errors++
			} else {
				log.Printf("Successfully scraped: %s", details.Title)
				stats.Success++
			}

			scrapedProducts = append(scrapedProducts, details)
		}
	}()

	// Wait for all products to be scraped and processed
	amazonScraper.Wait()
	wg.Wait()

	// Calculate timing statistics
	totalTime := time.Since(startTime).Seconds()
	stats.TotalDuration = totalTime
	if stats.Total > 0 {
		stats.AvgDuration = totalTime / float64(stats.Total)
	}

	log.Printf("Scraping completed. Success: %d, Errors: %d", stats.Success, stats.Errors)
	log.Printf("Total time: %.2f seconds, Average time per product: %.2f seconds",
		stats.TotalDuration, stats.AvgDuration)

	// --- Generate Reports ---
	reporter := report.New(cfg.OutputJSONFile, cfg.OutputMDFile)

	// Save scraped data as JSON
	if err := reporter.SaveJSON(apiResponse); err != nil {
		log.Printf("Failed to save JSON: %v", err)
	} else {
		log.Printf("API response saved to %s", cfg.OutputJSONFile)
	}

	// Save scraped data as Markdown
	if err := reporter.SaveMarkdown(scrapedProducts, stats); err != nil {
		log.Printf("Failed to save Markdown: %v", err)
	} else {
		log.Printf("Scraped data saved to %s", cfg.OutputMDFile)
	}

	fmt.Println("Amazon Scraper completed successfully. Results saved to:", cfg.OutputMDFile)
}

// setupGracefulShutdown configures signal handling for graceful shutdown
func setupGracefulShutdown(cancel context.CancelFunc) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		fmt.Println("Received termination signal. Shutting down gracefully...")
		cancel() // Cancel the context to tell all operations to finish
	}()
}
