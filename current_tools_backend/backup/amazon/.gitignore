# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
*.work

# Environment variables file
.env

# Build directory
/build/

# The executable file often generated by go build in the root
webscraper
webscraper.exe 