# Amazon Product Scraper

A high-performance, concurrent web scraper for Amazon product pages, built with Go best practices.

## Features

- **Concurrent Scraping**: Uses goroutines and worker pools for parallel scraping
- **Rate Limiting**: Prevents overloading servers and blocking
- **Statistical Filtering**: Filters product listings by probability score
- **Robust Error Handling**: Graceful handling of network errors and parsing failures
- **Configurable**: Environment variable configuration for easy customization
- **Well-Structured**: Follows Go best practices for maintainable code
- **Docker Support**: Containerized for easy deployment

## Architecture

The application is organized into several packages:

- `cmd/amazonscraper`: Main application entry point
- `internal/config`: Configuration handling
- `internal/client`: HTTP client with rate limiting and retries
- `internal/filter`: Statistical filtering for product listings
- `internal/scraper`: Product scraping logic
- `internal/report`: Report generation
- `internal/types`: Shared data structures

## Prerequisites

- Go 1.16 or higher
- Zyte API credentials (for proxy and extraction API)

## Installation

Clone the repository:

```bash
git clone https://github.com/wassim/amazonscraper.git
cd amazonscraper
```

Install dependencies:

```bash
make deps
```

## Configuration

The application can be configured using environment variables:

| Environment Variable | Description | Default |
|----------------------|-------------|---------|
| `ZYTE_API_KEY` | API key for Zyte | *default key* |
| `ZYTE_EXTRACT_URL` | URL for Zyte extraction API | https://api.zyte.com/v1/extract |
| `SEARCH_URL` | Amazon search URL to scrape | *default URL* |
| `MAX_CONCURRENCY` | Max number of concurrent scraping workers | 10 |
| `REQUEST_TIMEOUT` | Timeout for HTTP requests | 60s |
| `OUTPUT_JSON_FILE` | Path to save JSON output | response_with_descriptions.json |
| `OUTPUT_MD_FILE` | Path to save Markdown output | scraped_products.md |
| `RATE_LIMIT` | Time between requests (rate limiting) | 500ms |
| `RETRY_ATTEMPTS` | Number of retry attempts for failed requests | 3 |
| `RETRY_DELAY` | Delay between retry attempts | 1s |

## Usage

### Building and Running

Build and run the application:

```bash
make run
```

Run with custom environment variables:

```bash
ZYTE_API_KEY=your_api_key MAX_CONCURRENCY=20 make run
```

### Other Commands

- `make build`: Build the application
- `make test`: Run tests
- `make lint`: Run linter
- `make clean`: Clean build artifacts
- `make coverage`: Generate test coverage report
- `make docker-build`: Build Docker image
- `make docker-run`: Run in Docker container
- `make help`: Show available commands

## Docker Usage

Build the Docker image:

```bash
make docker-build
```

Run with Docker:

```bash
docker run --rm \
  -e ZYTE_API_KEY=your_api_key \
  -v $(pwd)/output:/app/output \
  amazonscraper:latest
```

## Output

The application generates two output files:

1. **JSON** (`response_with_descriptions.json`): Raw data from the API with descriptions
2. **Markdown** (`scraped_products.md`): Formatted report with product details

## Testing

Run tests with coverage:

```bash
make test
```

Generate and view coverage report:

```bash
make coverage
open coverage.html
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/your-feature`)
3. Commit your changes (`git commit -am 'Add some feature'`)
4. Push to the branch (`git push origin feature/your-feature`)
5. Create a new Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details. 