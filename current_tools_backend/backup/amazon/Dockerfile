FROM golang:1.22-alpine AS builder

# Set GOTO<PERSON><PERSON>AIN to auto to allow using a newer version specified in go.mod
ENV GOTOOLCHAIN=auto

WORKDIR /src

# Install build dependencies
RUN apk add --no-cache git

# Copy go.mod and go.sum files and download dependencies
COPY go.mod go.sum ./
RUN go mod download

# Copy the source code
COPY . .

# Build the API application with optimizations
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o /bin/amazon-api ./cmd/api

# Create a minimal runtime container
FROM alpine:3.18

# Install CA certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

WORKDIR /app

# Copy the binary from the builder stage
COPY --from=builder /bin/amazon-api /app/amazon-api

# Create output directory
RUN mkdir -p /app/output

# Set environment variables
ENV OUTPUT_JSON_FILE=/app/output/response.json \
    OUTPUT_MD_FILE=/app/output/scraped_products.md

# Run as non-root user for security
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
RUN chown -R appuser:appgroup /app
USER appuser

# Expose the port
EXPOSE 8080

# Command to run the application
CMD ["/app/amazon-api"]