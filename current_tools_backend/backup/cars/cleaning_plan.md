# AutoScout24 Codebase – Cleaning Plan

> This document outlines the proposed refactor/clean-up steps to simplify the project, remove AI-generated bloat, and make the codebase production-ready.

---

## 0. Guiding Principles
1. **KISS** – keep the implementation as small as possible while still covering the feature set.
2. **Single-Responsibility** – every module/class should do one thing.
3. **Fail fast** – avoid broad `try/except` that silently swallow problems; let exceptions bubble unless we can handle them meaningfully.
4. **Deterministic** – remove random or LLM-based behaviour from core scraping logic. LLM integration, if desired, should be an optional plug-in layer.
5. **Stateless services** – enforce pure functions or clearly delimited stateful services.

---

## 1. Remove / Consolidate Top-Level Entry Points
| Item | Action | Rationale |
|------|--------|-----------|
| `main.py` interactive CLI | Delete or move to `examples/` | Duplicates API functionality; not needed in production.
| `run_api.py` | ~~Merge into `api.py` or remove~~ **[x] removed** | Duplicate launch script deleted.
| `example_usage.py` | ~~Delete~~ **[x] removed** | Skeleton file deleted.

---

## 2. Configuration Cleanup
1. **`cars/config.py`** **[x] refactored**
   * ~~Replace class Config with BaseSettings~~
   * ~~Remove validate_credentials~~ (replaced with module helper)
2. Drop unused constants: `AUTOSCOUT24_COUNTRY`, `DEFAULT_LIMIT` **[x] removed**

---

## 3. Scraper Layer (`cars/platforms`)
1. **`BaseSearcher`**  **[x] removed**
   * ~~Delete the class – only `AutoScout24` is implemented and it does not inherit from it.~~
2. **`AutoScout24Searcher`** – heavy pruning:
   * Remove colourised prints → switch to `logging` with levels. **[x] completed**
   * Collapse the two URL-builders (`_build_search_url` & `_build_search_url_from_params`) into one. **[~] created unified public method; internal builder retained but centralised use**
   * Delete `_perform_direct_search`; just call `search` with explicit params when needed. **[x] removed and replaced by `search_by_params`**
   * Drop `_fallback_query_parsing` and `_extract_fallback_data`; encourages brittle scraping.
   * Replace gigantic list of CSS selectors with a small, tested set – keep at most 3.
   * Raise exceptions instead of returning `{'status':'error', ...}` dictionaries.
   * Add dataclass `CarListing` to hold extracted data → clearer typing.
3. Move Zyte request into standalone helper `fetch_html(url:str)->str` for reuse/testing.

---

## 4. LLM Integration (`cars/llm_service.py`, prompts)
* Mark the entire module **optional** behind `if Config.OPENAI_API_KEY` import; core search must not require it.
* Delete:
  * ~~`analyze_results`, `generate_fallback_query`, `optimize_query_with_retry`~~ **[x] removed**
* Keep only `parse_query(query:str)-> dict`

---

## 5. API Layer (`api.py`)
1. Replace custom response model with `pydantic` dataclasses using `ConfigDict` for orm_mode.
2. Drop duplicated code constructing search parameters – rely on a single function in searcher.
3. Return clear HTTP codes/exceptions – 4xx for user errors, 5xx for our faults.
4. Inject `AutoScout24Searcher` via FastAPI dependency to allow mocking in tests.

---

## 6. Results Persistence (`result_manager.py`)
* This module mixes file IO, statistics, and formatting.
  * Extract **Markdown report generation** into `reports/markdown.py`.
  * Extract **CSV/JSON saving** into `storage/filesystem.py`.
* Remove CSV column union logic – write all dict keys automatically.
* Replace string‐based sanitisation with `pathlib` and `slugify` util.

---

## 7. Logging Strategy  **[x] done**
* Replaced all `print`/`colorama` with structured logging; `colorama` dependency removed from code.

---

## 8. Exception Handling
* **Delete** broad `except Exception as e` blocks that just wrap and raise same info.
* Let unexpected errors propagate to FastAPI which already returns 500.
* Only catch:
  1. `requests.Timeout`, `requests.HTTPError` in network layer – wrap in custom `FetchError`.
  2. `json.JSONDecodeError` when parsing external JSON – return 502.

---

## 9. Testing & CI
* Replace `test_docker_api.py` (manual integration test) with pytest suite.
  * Unit tests for URL builder, HTML parsing.
  * Integration test mocks Zyte with recorded HTML.
* Add GitHub Actions:
  * lint (ruff/black)
  * tests (pytest)

---

## 10. Docker & Deployment
* Remove unnecessary packages (`gcc`) from Dockerfile; switch to `python:3.12-slim-bookworm`.
* Use multi-stage build if binary deps required.
* Rely on environment variables; do **not** mount `.env` inside container.

---

## 11. Timeline & Milestones
1. **Phase 1 (Day 0–1)** – remove dead files, set up logging, basic config refactor.
2. **Phase 2 (Day 2–4)** – refactor `AutoScout24Searcher` (URL builder, fetcher, parsing dataclass).
3. **Phase 3 (Day 5–6)** – API layer simplification & dependency-injection.
4. **Phase 4 (Day 7)** – Results persistence modularisation.
5. **Phase 5 (Day 8)** – Testing suite & CI pipeline.
6. **Phase 6 (Day 9)** – Final Docker slim-down & documentation update.

---

## 12. Deliverables
* Refactored, typed, and documented codebase.
* 100% passing pytest suite.
* Updated README with usage & contribution guidelines.
* Docker image size < 200 MB.

---

*Created on {{DATE}} – please update timeline after kickoff.*