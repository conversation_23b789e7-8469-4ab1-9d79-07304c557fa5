"""Markdown report generation for AutoScout24 results."""
from __future__ import annotations

import json
from datetime import datetime
from typing import Any, Dict, List


def generate_markdown_report(
    original_query: str,
    optimized_query: str,
    parameters: Dict[str, Any],
    items: List[Dict[str, Any]],
    total_results: int,
    status: str,
    analysis: str,
    timestamp: str | None = None,
) -> str:
    """Return a full markdown report string."""
    timestamp = timestamp or datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    md = [
        "# AutoScout24 Search Results Report",
        "",
        "## Search Information",
        f"- **Date**: {timestamp}",
        f"- **Original Query**: {original_query}",
        f"- **Optimized Query**: {optimized_query}",
        f"- **Search Status**: {status.upper()}",
        f"- **Total Results**: {total_results}",
        "",
        "## Search Parameters Used",
        "```json",
        json.dumps(parameters, indent=2),
        "```",
        "",
        "## LLM Analysis",
        analysis,
        "",
        f"## Car Results ({len(items)} cars shown)",
    ]

    for idx, item in enumerate(items, 1):
        md.append(f"\n### Car {idx}: {item.get('title', 'No title')}")
        md.append(f"- **Car ID**: {item.get('id', 'N/A')}")
        md.append(f"- **Price**: {item.get('price', 'N/A')} {item.get('currency', 'EUR')}")
        md.append(f"- **Year**: {item.get('year', 'N/A')}")
        md.append(f"- **Mileage**: {item.get('mileage', 'N/A')}")
        md.append(f"- **Fuel Type**: {item.get('fuel_type', 'N/A')}")
        md.append(f"- **Transmission**: {item.get('transmission', 'N/A')}")
        md.append(f"- **Location**: {item.get('location', 'N/A')}")
        link = item.get("link")
        if link:
            md.append(f"- **AutoScout24 Link**: [View Car]({link})")

    # Raw data section (truncate to first 3)
    md.extend(
        [
            "",
            "## Raw Search Data",
            "<details><summary>Click to expand raw JSON data</summary>",
            "",
            "```json",
            json.dumps({"cars": items[:3]}, indent=2),
            "```",
            "</details>",
            "",
            "---",
            f"*Report generated at {timestamp}*",
        ]
    )

    return "\n".join(md) 