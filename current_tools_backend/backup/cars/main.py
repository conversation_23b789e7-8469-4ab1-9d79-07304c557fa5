#!/usr/bin/env python3
"""
AutoScout24 Search with LLM Optimization
Uses OpenAI to optimize car search queries and analyze results
"""

import argparse
from typing import Dict, Any
from colorama import init, Fore, Style

from cars.platforms.autoscout24 import AutoScout24Searcher
from cars.llm_service import LLMQueryOptimizer
from cars.result_manager import ResultManager
from cars.config import Config

init()

class AutoScout24SearchApp:
    """Main application for AutoScout24 car search with LLM optimization"""

    def __init__(self):
        self.car_searcher = AutoScout24Searcher()
        self.llm_optimizer = LLMQueryOptimizer()
        self.result_manager = ResultManager()

    def validate_setup(self) -> bool:
        """Validate that all services are properly configured"""
        from cars.config import missing_credentials as _missing
        missing_credentials = _missing()

        if missing_credentials:
            print(f"\n{Fore.YELLOW}⚠️  Missing API credentials for: {', '.join(missing_credentials)}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}💡 Please update your config.py with valid API credentials.{Style.RESET_ALL}")
            if "OpenAI" in missing_credentials:
                print(f"{Fore.YELLOW}⚠️  OpenAI credentials missing - LLM optimization disabled{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.GREEN}✅ All API credentials configured{Style.RESET_ALL}")

        print(f"{Fore.GREEN}✅ AutoScout24 search ready{Style.RESET_ALL}")
        return True
    
    def perform_search(self, user_query: str, save_results: bool = True) -> Dict[str, Any]:
        """Perform car search and save results"""
        print(f"\n{Fore.CYAN}🚗 Starting AutoScout24 car search...{Style.RESET_ALL}")
        print(f"Query: {user_query}")

        search_results = self.car_searcher.search(user_query, limit=15)

        if search_results.get('status') != 'success':
            print(f"{Fore.RED}❌ Search failed: {search_results.get('error', 'Unknown error')}{Style.RESET_ALL}")
            return search_results

        data = search_results.get('data', {})
        total_results = data.get('total_found', 0)
        items_shown = len(data.get('listings', []))

        if total_results > 0:
            print(f"{Fore.GREEN}✅ Car search successful!{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}⚠️ Search completed but found 0 cars{Style.RESET_ALL}")

        print(f"   📊 Total cars found: {total_results}")
        print(f"   🚗 Cars retrieved: {items_shown}")

        if save_results and items_shown > 0:
            print(f"\n{Fore.GREEN}💾 Saving car search results...{Style.RESET_ALL}")

            json_file = self.result_manager.save_comprehensive_results(user_query, search_results)
            csv_file = self.result_manager.save_csv_results(user_query, search_results)

            search_results['saved_files'] = {'json': json_file, 'csv': csv_file}
            print(f"   📄 JSON saved: {json_file}")
            print(f"   📊 CSV saved: {csv_file}")

        return search_results
    
    def display_quick_summary(self, results: Dict[str, Any]):
        """Display a summary of car search results"""
        if results.get('status') != 'success':
            return

        print(f"\n{Fore.CYAN}🚗 AutoScout24 Search Results{Style.RESET_ALL}")
        print("=" * 70)

        items = results.get('data', {}).get('listings', [])

        if not items:
            print(f"{Fore.YELLOW}No cars found{Style.RESET_ALL}")
            return

        for i, car in enumerate(items[:10], 1):
            title = car.get('title', 'Unknown Car')
            print(f"\n{Fore.GREEN}🚙 {i}. {title}{Style.RESET_ALL}")

            price = car.get('price', 'N/A')
            price_display = f"€{int(price):,}" if price and price.isdigit() else f"€{price}" if price != 'N/A' else "Price N/A"

            year = car.get('year', 'N/A')
            mileage = car.get('mileage', 'N/A')
            fuel = car.get('fuel_type', 'N/A')
            transmission = car.get('transmission', 'N/A')

            print(f"   💰 {price_display} | 📅 {year} | 🛣️  {mileage} | ⛽ {fuel} | 🔧 {transmission}")

            power_hp = car.get('power_hp', '')
            power_kw = car.get('power_kw', '')
            if power_hp or power_kw:
                power_display = f"{power_hp} HP" if power_hp else f"{power_kw} kW"
                print(f"   🏁 Power: {power_display}", end="")

                co2 = car.get('co2_emissions', '')
                consumption = car.get('fuel_consumption', '')
                if co2 or consumption:
                    print(f" | 🌱 {co2 if co2 else 'N/A'}", end="")
                    if consumption:
                        print(f" | ⛽ {consumption}", end="")
                print()

            seller_name = car.get('seller_name', '')
            seller_type = car.get('seller_type', '')
            seller_phone = car.get('seller_phone', '')

            if seller_name or seller_type:
                seller_info = f"👤 {seller_type}" if seller_type else "👤 Seller"
                if seller_name:
                    seller_info += f": {seller_name[:30]}..."
                if seller_phone:
                    seller_info += f" | 📞 {seller_phone}"
                print(f"   {seller_info}")

            features = car.get('features', [])
            if features:
                features_display = ", ".join(features[:3])
                if len(features) > 3:
                    features_display += f" (+{len(features) - 3} more)"
                print(f"   ✨ {features_display}")

            link = car.get('link', '')
            images = car.get('images', [])
            if link or images:
                print(f"   🔗 Link: {link[:50]}..." if link else "   🔗 No link")
                if images:
                    print(f"   📷 Images: {len(images)} available")

        if len(items) > 10:
            print(f"\n   ... and {len(items) - 10} more cars (check saved files for complete list)")

        metadata = results.get('metadata', {})
        if metadata:
            print(f"\n{Fore.BLUE}📊 Search Stats:{Style.RESET_ALL}")
            print(f"   🕒 Search time: {metadata.get('search_timestamp', 'N/A')}")
            print(f"   📄 Page size: {metadata.get('page_size', 0):,} bytes")
            print(f"   🚗 Cars extracted: {metadata.get('cars_extracted', 0)}")

        saved_files = results.get('saved_files', {})
        if saved_files:
            print(f"\n{Fore.GREEN}💾 Results saved to:{Style.RESET_ALL}")
            for file_type, filename in saved_files.items():
                print(f"   📄 {file_type.upper()}: {filename}")
    
    def interactive_mode(self):
        """Interactive car search mode"""
        print(f"{Fore.CYAN}{'='*60}")
        print(f"🚗 AUTOSCOUT24 CAR SEARCH WITH LLM OPTIMIZATION")
        print(f"{'='*60}{Style.RESET_ALL}")
        print(f"Powered by OpenAI {Config.OPENAI_MODEL} and AutoScout24 Web Scraping")

        self.validate_setup()

        while True:
            print(f"\n{Fore.GREEN}🔍 Car Search Options:{Style.RESET_ALL}")
            print("1. 🚗 Search AutoScout24")
            print("2. 📁 View saved results")
            print("3. ⚙️  Validate credentials")
            print("4. 🚪 Exit")

            choice = input(f"\n{Fore.CYAN}Enter your choice (1-4): {Style.RESET_ALL}").strip()

            if choice == "4":
                print(f"\n{Fore.GREEN}👋 Thank you for using AutoScout24 Car Search!{Style.RESET_ALL}")
                break

            elif choice == "1":
                query = input(f"\n{Fore.CYAN}🚗 Enter your car search query: {Style.RESET_ALL}").strip()
                if not query:
                    print(f"{Fore.RED}❌ Car search query cannot be empty.{Style.RESET_ALL}")
                    continue

                results = self.perform_search(query)
                self.display_quick_summary(results)

            elif choice == "2":
                saved_files = self.result_manager.list_saved_results()
                if not saved_files:
                    print(f"\n{Fore.YELLOW}📁 No saved results found.{Style.RESET_ALL}")
                else:
                    print(f"\n{Fore.GREEN}📁 Saved Results ({len(saved_files)} files):{Style.RESET_ALL}")
                    for i, filename in enumerate(saved_files[:10], 1):
                        print(f"   {i}. {filename}")

                    if len(saved_files) > 10:
                        print(f"   ... and {len(saved_files) - 10} more files")

                    print(f"\n{Fore.CYAN}💡 Results saved in '{Config.RESULTS_FOLDER}' folder{Style.RESET_ALL}")

            elif choice == "3":
                self.validate_setup()

            else:
                print(f"{Fore.RED}❌ Invalid choice. Please enter 1, 2, 3, or 4.{Style.RESET_ALL}")

def main():
    """Main function with command-line argument parsing"""
    parser = argparse.ArgumentParser(
        description="AutoScout24 Car Search with LLM Optimization",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --query "BMW X5" --interactive
  python main.py --query "family car under 20000" --no-save
  python main.py --validate
        """
    )

    parser.add_argument("--query", "-q", type=str, help="Car search query")
    parser.add_argument("--interactive", "-i", action="store_true",
                       help="Run in interactive mode")
    parser.add_argument("--no-save", action="store_true",
                       help="Don't save results to files")
    parser.add_argument("--validate", "-v", action="store_true",
                       help="Validate API credentials and exit")
    parser.add_argument("--summary-only", "-s", action="store_true",
                       help="Show only quick summary")

    args = parser.parse_args()

    app = AutoScout24SearchApp()

    # Validation mode
    if args.validate:
        app.validate_setup()
        return

    # Interactive mode
    if args.interactive or not args.query:
        app.interactive_mode()
        return

    # Command-line mode
    query = args.query
    save_results = not args.no_save

    print(f"\n{Fore.YELLOW}🚗 Searching for cars: '{query}'{Style.RESET_ALL}")

    # Perform search
    results = app.perform_search(query, save_results)

    # Display results
    if results.get('status') == 'success':
        app.display_quick_summary(results)
    else:
        print(f"{Fore.RED}❌ Car search failed: {results.get('error', 'Unknown error')}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()