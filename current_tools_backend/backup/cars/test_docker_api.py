#!/usr/bin/env python3
"""
Comprehensive test script for the FastAPI endpoints running in Docker
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
API_BASE_URL = "http://localhost:8000"

def print_separator(title):
    """Print a nice separator"""
    print("\n" + "=" * 60)
    print(f"🚗 {title}")
    print("=" * 60)

def test_api_availability():
    """Test if the API is available"""
    print_separator("API AVAILABILITY TEST")
    
    try:
        response = requests.get(f"{API_BASE_URL}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API is available and responding")
            print(f"   Message: {data.get('message', 'N/A')}")
            print(f"   Version: {data.get('version', 'N/A')}")
            print(f"   Endpoints: {list(data.get('endpoints', {}).keys())}")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to API: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint"""
    print_separator("HEALTH ENDPOINT TEST")
    
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   Status: {data['status']}")
            print(f"   Timestamp: {data['timestamp']}")
            print("   Services:")
            for service, status in data['services'].items():
                print(f"     - {service}: {status}")
            
            if data.get('missing_credentials'):
                print(f"   ⚠️  Missing credentials: {data['missing_credentials']}")
            else:
                print("   ✅ All credentials configured")
            
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_examples_endpoint():
    """Test the examples endpoint"""
    print_separator("EXAMPLES ENDPOINT TEST")
    
    try:
        response = requests.get(f"{API_BASE_URL}/search/examples", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Examples endpoint working")
            
            direct_examples = data.get('direct_search_examples', {})
            llm_examples = data.get('llm_search_examples', [])
            
            print(f"   Direct search examples: {len(direct_examples)}")
            for name, params in list(direct_examples.items())[:2]:
                print(f"     - {name}: {params}")
            
            print(f"   LLM search examples: {len(llm_examples)}")
            for example in llm_examples[:3]:
                print(f"     - '{example}'")
            
            return True
        else:
            print(f"❌ Examples endpoint failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Examples endpoint error: {e}")
        return False

def test_direct_search_endpoint():
    """Test the direct search endpoint"""
    print_separator("DIRECT SEARCH ENDPOINT TEST")
    
    test_params = {
        "make": "bmw",
        "model": "x5", 
        "price_to": 50000,
        "limit": 2
    }
    
    print(f"Testing with parameters: {json.dumps(test_params, indent=2)}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/search", 
            json=test_params,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Direct search endpoint working!")
            print(f"   Status: {data['status']}")
            print(f"   Query: {data['query']}")
            print(f"   Total found: {data['total_found']}")
            print(f"   Cars returned: {len(data['cars'])}")
            
            if data.get('extracted_parameters'):
                print("   Extracted parameters:")
                for key, value in data['extracted_parameters'].items():
                    if value is not None:
                        print(f"     {key}: {value}")
            
            # Show first car if available
            if data['cars']:
                car = data['cars'][0]
                print(f"   First car: {car.get('title', 'N/A')}")
                print(f"   Price: €{car.get('price', 'N/A')}")
                print(f"   Year: {car.get('year', 'N/A')}")
            
            return True
        else:
            print(f"❌ Direct search failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Direct search error: {e}")
        return False

def test_llm_search_endpoint():
    """Test the LLM search endpoint"""
    print_separator("LLM SEARCH ENDPOINT TEST")
    
    test_query = {
        "query": "family car under 20000",
        "limit": 2
    }
    
    print(f"Testing with query: '{test_query['query']}'")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/search/llm", 
            json=test_query,
            timeout=60
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ LLM search endpoint working!")
            print(f"   Status: {data['status']}")
            print(f"   Query: {data['query']}")
            print(f"   Total found: {data['total_found']}")
            print(f"   Cars returned: {len(data['cars'])}")
            
            if data.get('extracted_parameters'):
                print("   🧠 LLM extracted parameters:")
                for key, value in data['extracted_parameters'].items():
                    if value is not None:
                        print(f"     {key}: {value}")
            
            # Show first car if available
            if data['cars']:
                car = data['cars'][0]
                print(f"   First car: {car.get('title', 'N/A')}")
                print(f"   Price: €{car.get('price', 'N/A')}")
                print(f"   Year: {car.get('year', 'N/A')}")
            
            return True
        else:
            print(f"❌ LLM search failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            except:
                print(f"   Raw response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ LLM search error: {e}")
        return False

def test_api_documentation():
    """Test if API documentation is available"""
    print_separator("API DOCUMENTATION TEST")
    
    endpoints_to_test = [
        ("/docs", "Swagger UI"),
        ("/redoc", "ReDoc"),
        ("/openapi.json", "OpenAPI Schema")
    ]
    
    results = []
    for endpoint, name in endpoints_to_test:
        try:
            response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name} available at {API_BASE_URL}{endpoint}")
                results.append(True)
            else:
                print(f"❌ {name} failed: {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"❌ {name} error: {e}")
            results.append(False)
    
    return all(results)

def main():
    """Run comprehensive API tests"""
    print("🧪 COMPREHENSIVE FASTAPI DOCKER TEST")
    print(f"Testing API at: {API_BASE_URL}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("API Availability", test_api_availability),
        ("Health Endpoint", test_health_endpoint),
        ("Examples Endpoint", test_examples_endpoint),
        ("API Documentation", test_api_documentation),
        ("Direct Search", test_direct_search_endpoint),
        ("LLM Search", test_llm_search_endpoint),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n⏳ Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print_separator("TEST RESULTS SUMMARY")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 Overall Results: {passed}/{total} tests passed")
    print()
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
    
    if passed == total:
        print(f"\n🎉 All tests passed! The FastAPI is working perfectly in Docker!")
        print(f"🌐 Access the API at: {API_BASE_URL}")
        print(f"📖 Documentation at: {API_BASE_URL}/docs")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Check the details above.")
        if passed >= 4:  # If basic endpoints work
            print("💡 Basic API functionality is working, search endpoints may have async issues.")
    
    print(f"\n🔗 Quick Links:")
    print(f"   - API Base: {API_BASE_URL}")
    print(f"   - Health Check: {API_BASE_URL}/health")
    print(f"   - Examples: {API_BASE_URL}/search/examples")
    print(f"   - Swagger UI: {API_BASE_URL}/docs")
    print(f"   - ReDoc: {API_BASE_URL}/redoc")

if __name__ == "__main__":
    main()
