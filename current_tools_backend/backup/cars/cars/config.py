"""Project-wide configuration handled by Pydantic `BaseSettings`.

Environment variables are automatically loaded from a ``.env`` file (if present)
and from the process environment.
"""

from __future__ import annotations

from pathlib import Path
from typing import Any, Dict

from dotenv import load_dotenv
from pydantic import BaseSettings, Field

# Load .env early (if it exists)
load_dotenv()


class _Settings(BaseSettings):
    # --- External APIs -----------------------------------------------------
    OPENAI_API_KEY: str | None = Field(default=None, env="OPENAI_API_KEY")
    OPENAI_MODEL: str = Field(default="o3-mini", env="OPENAI_MODEL")

    ZYTE_API_KEY: str | None = Field(default=None, env="ZYTE_API_KEY")

    # --- Scraper settings --------------------------------------------------
    AUTOSCOUT24_BASE_URL: str = "https://www.autoscout24.com"

    # --- Runtime -----------------------------------------------------------
    REQUEST_TIMEOUT: int = 30  # seconds
    RESULTS_FOLDER: str = "results"

    # Pydantic configuration
    model_config = {
        "env_file": ".env",
        "extra": "ignore",
    }

    # Convenience helpers ---------------------------------------------------
    def missing_credentials(self) -> list[str]:
        """Return list of services without configured credentials."""
        missing: list[str] = []
        if not self.OPENAI_API_KEY:
            missing.append("OpenAI")
        if not self.ZYTE_API_KEY:
            missing.append("Zyte")
        return missing

    def ensure_results_folder(self) -> Path:
        """Create results folder if it doesn't exist and return ``Path``."""
        path = Path(self.RESULTS_FOLDER)
        path.mkdir(parents=True, exist_ok=True)
        return path

    def platform_config(self, platform: str) -> Dict[str, Any] | None:
        if platform.lower() == "autoscout24":
            return {
                "base_url": self.AUTOSCOUT24_BASE_URL,
                "requires_api_key": False,
            }
        return None


# Instantiate once and expose legacy-style interface -----------------------

_settings = _Settings()  # type: ignore[arg-type]


class Config:  # pylint: disable=too-few-public-methods
    """Backwards-compatibility shim.

    Existing code imports attributes like ``Config.ZYTE_API_KEY``. After we
    create the ``_settings`` instance, copy its values onto this dummy class so
    those imports continue to work without code changes.
    """


# Copy all fields from _settings onto Config as UPPERCASE attributes
for _k, _v in _settings.model_dump().items():
    setattr(Config, _k, _v)


# Provide updated helper accessors -----------------------------------------

def missing_credentials() -> list[str]:  # noqa: D401 – simple helper
    """Return list of services without configured credentials."""
    return _settings.missing_credentials()


# What other modules should import
__all__ = ["Config", "_settings", "missing_credentials"]