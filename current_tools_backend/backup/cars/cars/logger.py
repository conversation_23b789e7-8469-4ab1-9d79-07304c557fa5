"""Central logging configuration for the project.

Import this module once at application start (e.g. via `import cars.logger`) to
ensure a consistent logging format across the code-base.
"""

from __future__ import annotations

import logging
import os
import sys


def _setup_logging() -> None:
    """Configure root logger only once.

    The log level can be controlled via the *LOG_LEVEL* env variable. Defaults
    to *INFO*. The format is terse and colour-agnostic to keep container logs
    clean.
    """
    level_name = os.getenv("LOG_LEVEL", "INFO").upper()
    level = getattr(logging, level_name, logging.INFO)

    # Avoid duplicate handlers when reloading (e.g. in uvicorn --reload)
    if logging.getLogger().handlers:
        return

    logging.basicConfig(
        level=level,
        format="%(asctime)s | %(levelname)s | %(name)s: %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        stream=sys.stdout,
        force=True,  # override any basicConfig from dependencies
    )


_setup_logging()

# Convenience alias so modules can simply do `from cars.logger import logger`.
logger = logging.getLogger("autoscout24") 