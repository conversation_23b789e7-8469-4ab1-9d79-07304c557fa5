import json
from typing import Dict, <PERSON>, <PERSON><PERSON>
from openai import OpenAI

from cars.logger import logger
from .config import Config
from .prompts import AutoScout24SearchPrompts


class LLMQueryOptimizer:
    """LLM service for optimizing AutoScout24 car search queries using OpenAI API"""

    def __init__(self, api_key: str | None = None):
        """Create optimizer.

        If *api_key* is provided it overrides the value from environment/config.
        """
        self.client = None
        self.platform = "autoscout24"
        self._init_client(api_key)

    def _init_client(self, explicit_key: str | None = None):
        """Initialize OpenAI client"""
        api_key = explicit_key or Config.OPENAI_API_KEY
        if not api_key:
            logger.warning("OpenAI API key not configured – LLM features disabled")
            return

        self.client = OpenAI(api_key=api_key)
        logger.info("OpenAI client initialised (key override=%s)", bool(explicit_key))
    
    def optimize_query(self, user_query: str) -> Tuple[str, Dict[str, Any]]:
        """Use LLM to optimize the user query and generate search parameters"""
        if not self.client:
            logger.debug("LLM not available – returning original query")
            return user_query, {}

        response = self.client.responses.create(
            model="o3",
            input=[{
                "type": "message",
                "role": "user",
                "content": AutoScout24SearchPrompts.get_optimization_prompt(user_query)
            }],
            text={"format": {"type": "text"}}
        )

        content = self._extract_response_content(response)

        if not content:
            logger.warning("LLM returned empty response – using original query")
            return user_query, {}

        parsed = json.loads(content)
        optimized_query = parsed.get("optimized_query", user_query)
        parameters = parsed.get("parameters", {})

        logger.info("LLM optimization complete | original='%s' | optimized='%s' | params=%s", user_query, optimized_query, parameters)

        return optimized_query, parameters

    def _extract_response_content(self, response) -> str:
        """Extract content from OpenAI API response"""
        content = ""
        if hasattr(response, 'output') and response.output:
            for output_item in response.output:
                if hasattr(output_item, 'type') and output_item.type == 'message':
                    if hasattr(output_item, 'content') and output_item.content:
                        for content_item in output_item.content:
                            if hasattr(content_item, 'text'):
                                content = content_item.text.strip()
                                break
                    if content:
                        break

        if not content:
            content = str(response).strip()

        return content

    def analyze_results(self, query: str, results: Dict[str, Any]) -> str:
        """Use LLM to analyze search results and provide insights"""
        if not self.client:
            return self._generate_basic_analysis(query, results)

        items = results.get('data', {}).get('listings', [])
        total_results = results.get('data', {}).get('total', len(items))

        items_summary = []
        for item in items[:5]:
            item_info = {
                "title": item.get("title", ""),
                "price": item.get("price", ""),
                "currency": item.get("currency", "EUR"),
                "year": item.get("year", ""),
                "mileage": item.get("mileage", ""),
                "fuel": item.get("fuel", ""),
                "transmission": item.get("transmission", ""),
                "location": item.get("location", "")
            }
            items_summary.append(item_info)

        analysis_prompt = AutoScout24SearchPrompts.get_analysis_prompt(query, total_results, items_summary)

        response = self.client.responses.create(
            model="o3",
            input=[{
                "type": "message",
                "role": "user",
                "content": analysis_prompt
            }],
            text={"format": {"type": "text"}}
        )

        content = self._extract_response_content(response)

        if not content:
            print(f"{Fore.YELLOW}LLM returned empty response, using basic analysis{Style.RESET_ALL}")
            return self._generate_basic_analysis(query, results)

        return content
    
    def _generate_basic_analysis(self, query: str, results: Dict[str, Any]) -> str:
        """Generate basic analysis when LLM is not available"""
        items = results.get('data', {}).get('listings', [])
        total_results = results.get('data', {}).get('total', len(items))

        analysis = f"""# Car Search Results Analysis

## Query Information
- **Search Query**: {query}
- **Platform**: AutoScout24
- **Total Results**: {total_results}
- **Displayed Cars**: {len(items)}

## Basic Statistics
"""

        if items:
            prices = []
            years = []
            fuel_types = {}

            for item in items:
                price_str = str(item.get('price', '')).replace(',', '').replace('.', '')
                if price_str.isdigit():
                    prices.append(int(price_str))

                year_str = str(item.get('year', ''))
                if year_str.isdigit() and len(year_str) == 4:
                    years.append(int(year_str))

                fuel = item.get('fuel', 'Unknown')
                fuel_types[fuel] = fuel_types.get(fuel, 0) + 1

            if prices:
                analysis += f"""
- **Price Range**: €{min(prices):,} - €{max(prices):,}
- **Average Price**: €{sum(prices)//len(prices):,}
"""

            if years:
                analysis += f"""
- **Year Range**: {min(years)} - {max(years)}
- **Average Year**: {sum(years)//len(years)}
"""

            if fuel_types:
                analysis += "\n- **Fuel Types**:\n"
                for fuel, count in fuel_types.items():
                    analysis += f"  - {fuel}: {count} cars\n"

        analysis += "\n## Sample Cars\n"
        for i, item in enumerate(items[:3], 1):
            title = item.get('title', 'No title')
            price = item.get('price', 'N/A')
            year = item.get('year', 'N/A')
            fuel = item.get('fuel', 'N/A')

            analysis += f"""
**{i}. {title}**
- Price: €{price}
- Year: {year}
- Fuel: {fuel}
"""

        return analysis

    def generate_fallback_query(self, current_query: str, attempt: int) -> str:
        """Generate a fallback query when current search returns no results"""
        if not self.client:
            return self._generate_basic_fallback(current_query, attempt)

        response = self.client.responses.create(
            model="o3",
            input=[{
                "type": "message",
                "role": "user",
                "content": AutoScout24SearchPrompts.get_fallback_prompt(current_query, attempt)
            }],
            text={"format": {"type": "text"}}
        )

        content = self._extract_response_content(response)

        if not content:
            print(f"{Fore.YELLOW}LLM returned empty fallback, using basic fallback{Style.RESET_ALL}")
            return self._generate_basic_fallback(current_query, attempt)

        fallback_query = content.strip().strip('"').strip("'").strip()

        print(f"{Fore.CYAN}Generated fallback query (attempt {attempt}): {fallback_query}{Style.RESET_ALL}")
        return fallback_query
    
    def _generate_basic_fallback(self, current_query: str, attempt: int) -> str:
        """Generate basic fallback when LLM is not available"""
        query_words = current_query.lower().split()
        car_brands = ['bmw', 'audi', 'mercedes', 'volkswagen', 'vw', 'ford', 'opel', 'toyota', 'honda', 'nissan']

        for word in query_words:
            if word in car_brands:
                return word

        if attempt == 1:
            return ' '.join(query_words[:2]) if len(query_words) > 1 else current_query
        elif attempt == 2:
            return query_words[0] if query_words else "car"

        return "car"

    def optimize_query_with_retry(self, user_query: str, search_function, max_attempts: int = 4) -> Tuple[str, Dict[str, Any], Dict[str, Any], int]:
        """Optimize query and search with automatic retry mechanism for empty results"""
        print(f"{Fore.CYAN}🔄 Starting search with retry mechanism (max {max_attempts} attempts){Style.RESET_ALL}")

        current_query, parameters = self.optimize_query(user_query)

        for attempt in range(1, max_attempts + 1):
            print(f"\n{Fore.YELLOW}🔍 Attempt {attempt}/{max_attempts}: '{current_query}'{Style.RESET_ALL}")

            search_results = search_function(current_query, parameters)

            if search_results.get('status') == 'success':
                items = search_results.get('data', {}).get('listings', [])
                total = search_results.get('data', {}).get('total', len(items))

                if items or (total and total > 0):
                    print(f"{Fore.GREEN}✅ Success! Found {len(items)} cars (total: {total}){Style.RESET_ALL}")
                    return current_query, parameters, search_results, attempt

                print(f"{Fore.YELLOW}⚠️  Search succeeded but returned 0 results{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ Search failed: {search_results.get('error', 'Unknown error')}{Style.RESET_ALL}")

            if attempt < max_attempts:
                print(f"{Fore.CYAN}🔄 Generating fallback query for attempt {attempt + 1}...{Style.RESET_ALL}")
                current_query = self.generate_fallback_query(current_query, attempt)
                parameters = {}

        print(f"{Fore.RED}💔 All {max_attempts} attempts completed. No results found.{Style.RESET_ALL}")
        return current_query, parameters, search_results, max_attempts