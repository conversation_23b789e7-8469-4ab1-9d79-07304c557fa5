"""AutoScout24 Car Search LLM Prompts"""


class AutoScout24SearchPrompts:
    """Container for all AutoScout24 car search LLM prompts"""
    
    @staticmethod
    def get_optimization_prompt(user_query: str) -> str:
        """Generate the prompt for car search query optimization"""
        return f"""You are an AutoScout24 car search optimizer. Create effective car search queries.

**CAR SEARCH OPTIMIZATION RULES:**

1. **IDENTIFY CAR DETAILS** - Extract make, model, year, budget, preferences
2. **USE STANDARD NAMES** - Convert to official car make/model names
3. **ADD SEARCH PARAMETERS** - Include relevant filters for better results
4. **CONSIDER ALTERNATIVES** - Suggest similar cars if specific model is too narrow

**CAR SEARCH STRUCTURE:**
[Car Make] [Model] [optional: year/generation]

**OPTIMIZATION EXAMPLES:**
- "cheap BMW" → "BMW 3 Series"
- "family car under 20000" → "Volkswagen Golf"
- "sports car" → "BMW M3"
- "electric car" → "Tesla Model 3"
- "SUV for family" → "Volkswagen Tiguan"
- "luxury sedan" → "Mercedes E-Class"

**KEY PRINCIPLES:**
- Use official car brand names (BMW, Mercedes-Benz, Audi, etc.)
- Include most popular/available models
- Consider budget constraints in model selection
- Add relevant parameters for filtering

**AVAILABLE PARAMETERS:**
- price_min, price_max (in EUR)
- year_min, year_max
- km_max (mileage limit)
- fuel_type (diesel, petrol, electric, hybrid)
- transmission (automatic, manual)
- power_min, power_max (HP)

**USER QUERY:** {user_query}

Optimize for car search and suggest parameters. Return JSON:
{{
    "optimized_query": "CarMake Model",
    "parameters": {{
        "price_max": 25000,
        "year_min": 2015,
        "fuel_type": "diesel"
    }},
    "reasoning": "Why this optimization works"
}}"""

    @staticmethod
    def get_fallback_prompt(current_query: str, attempt: int) -> str:
        """Generate the prompt for car search fallback query generation"""
        return f"""The car query "{current_query}" found 0 results. Make it broader for more results.

**CAR SEARCH FALLBACK - Attempt #{attempt}/4:**

{"**VERY BROAD** - " if attempt > 2 else "**BROADER** - "}Expand to more common/available cars.

**CAR FALLBACK STRATEGY:**
- Move from specific model to car brand
- Remove year/generation restrictions
- Expand to similar car categories
- Use most popular car models

**FALLBACK EXAMPLES:**
- "BMW M5 2020" → "BMW 5 Series"
- "Audi RS6" → "Audi A6"
- "Mercedes AMG" → "Mercedes C-Class"
- "sports car" → "BMW"
- "luxury SUV" → "SUV"

**CURRENT:** {current_query}

Make it {"very broad (just brand or category)" if attempt > 2 else "broader but still specific"}. Return only the simplified query."""

    @staticmethod
    def get_analysis_prompt(query: str, total_results: int, cars_summary: list) -> str:
        """Generate the prompt for car search results analysis"""
        import json
        
        return f"""Analyze these AutoScout24 car search results and provide comprehensive automotive insights.

**CAR SEARCH ANALYSIS REQUEST:**
- **Original Query:** {query}
- **Total Cars Found:** {total_results}
- **Sample Cars (top 5):** {json.dumps(cars_summary, indent=2)}

**PROVIDE DETAILED AUTOMOTIVE ANALYSIS:**

## 🚗 **Search Results Quality**
- How well do results match the car search intent?
- Relevance of car models found vs. original query
- Search precision for automotive needs (1-10)

## 💰 **Car Price Analysis & Market Value**
- Price range analysis across different car years/models
- Market value assessment: Are prices competitive?
- Price per kilometer analysis
- Best value cars in the results
- Depreciation insights

## 🔧 **Vehicle Condition & Technical Analysis**
- Mileage distribution and what's considered reasonable
- Age vs. condition analysis
- Fuel type distribution (diesel, petrol, electric, hybrid)
- Transmission preferences (automatic vs manual)
- Engine power analysis

## 🏁 **Car Model & Brand Insights**
- Most popular car models in results
- Brand reputation and reliability insights
- Model-specific pros and cons
- Generation differences if applicable

## 🔍 **Search Optimization for Cars**
- Suggest improved car search terms
- Additional filters to narrow down results
- Alternative car models to consider
- Regional availability insights

## 🛒 **Car Buying Strategy & Recommendations**
- Best cars to consider from the results
- What to look for in car history/condition
- Negotiation tips based on market analysis
- Inspection priorities for specific models

## 📊 **Automotive Market Intelligence**
- Supply/demand trends for these car models
- Seasonal market patterns
- Resale value predictions
- Popular configurations/features

## ⚠️ **Car Buying Risk Assessment**
- Red flags in car listings (high mileage, accident history)
- Price red flags (too good to be true, overpriced)
- Model-specific issues to watch for
- Financing and insurance considerations

## 🌱 **Environmental & Efficiency Insights**
- Fuel efficiency comparison
- Environmental impact assessment
- Electric/hybrid alternatives if relevant
- Long-term cost analysis (fuel, maintenance, insurance)

Use automotive terminology and provide actionable car buying advice with clear markdown formatting."""

