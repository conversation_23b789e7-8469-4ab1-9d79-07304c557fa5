import json
import re
import time
import base64
import logging
from dataclasses import asdict, dataclass
from typing import Any, Dict, List
from urllib.parse import urlencode

import requests
import httpx
from bs4 import BeautifulSoup

from cars.logger import logger  # Project-wide logger
from ..llm_service import LLMQueryOptimizer
from ..config import Config
from cars.exceptions import FetchError


# ---------------------------------------------------------------------------
# Helpers


def fetch_html(url: str, zyte_api_key: str, timeout: int = 60) -> bytes:
    """Retrieve raw HTML via Zyte API.

    Raises ``requests.HTTPError`` on failure.
    """
    logger.debug("Fetching HTML via Zyte API: %s", url)

    response = requests.post(
        "https://api.zyte.com/v1/extract",
        auth=(zyte_api_key, ""),
        json={"url": url, "httpResponseBody": True, "followRedirect": True},
        timeout=timeout,
    )
    response.raise_for_status()

    html_content_b64: str = response.json()["httpResponseBody"]
    return base64.b64decode(html_content_b64)


# ---------------------------------------------------------------------------
# Typed model


@dataclass
class CarListing:
    id: str
    title: str
    make: str = ""
    model: str = ""
    price: str = ""
    currency: str = "EUR"
    year: str = ""
    mileage: str = ""
    fuel_type: str = ""
    transmission: str = ""
    power_hp: str = ""
    power_kw: str = ""
    body_type: str = ""
    color: str = ""
    doors: str = ""
    seats: str = ""
    location: str = ""
    seller_name: str = ""
    seller_type: str = ""
    seller_phone: str = ""
    seller_email: str = ""
    description: str = ""
    features: List[str] = None  # type: ignore[assignment]
    link: str = ""
    images: List[str] = None  # type: ignore[assignment]
    condition: str = "Used"
    first_registration: str = ""
    previous_owners: str = ""
    emission_class: str = ""
    co2_emissions: str = ""
    fuel_consumption: str = ""

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class AutoScout24Searcher:
    """AutoScout24 car search engine using web scraping"""

    def __init__(self, zyte_api_key: str | None = None, openai_api_key: str | None = None):
        self.base_url = Config.AUTOSCOUT24_BASE_URL
        self.zyte_api_key = zyte_api_key or Config.ZYTE_API_KEY or ""

        if not self.zyte_api_key:
            logger.warning("Zyte API key not configured – real requests will fail")

        # LLM optimizer may be unused for direct search but initialise with possible override
        self.llm_optimizer = LLMQueryOptimizer(api_key=openai_api_key)
        self._last_llm_params: Dict[str, Any] | None = None

    # ------------------------------------------------------------------
    # URL building helpers

    def _build_url(self, params: Dict[str, Any], limit: int) -> str:
        base_search_url = f"{self.base_url}/lst"

        url_path = ""
        if params.get("make"):
            url_path = f"/{params['make']}"
            if params.get("model"):
                url_path += f"/{params['model']}"

        search_url = f"{base_search_url}{url_path}" if url_path else base_search_url

        url_params: Dict[str, Any] = {
            "sort": params.get("sort", "price"),
            "desc": "0",
            "ustate": "N,U",
            "size": min(limit, 20),
            "page": "1",
            "atype": "C",
            "cy": "D",
            "ac": "0",
        }

        # Optional filters ------------------------------------------------
        mapping_simple = {
            "price_from": "pricefrom",
            "price_to": "priceto",
            "year_from": "fregfrom",
            "year_to": "fregto",
            "mileage_to": "kmto",
        }
        for src, dest in mapping_simple.items():
            if params.get(src):
                url_params[dest] = str(params[src])

        if params.get("fuel_type"):
            fuel_code = {"petrol": "B", "diesel": "D", "electric": "E", "hybrid": "H"}.get(
                params["fuel_type"].lower()
            )
            if fuel_code:
                url_params["fuel"] = fuel_code

        if params.get("transmission"):
            trans_code = {"manual": "M", "automatic": "A"}.get(params["transmission"].lower())
            if trans_code:
                url_params["gear"] = trans_code

        if params.get("condition"):
            url_params["ustate"] = "N" if params["condition"].lower() == "new" else "U"

        if not url_path and params.get("make"):
            search_terms = [params["make"]]
            if params.get("model"):
                search_terms.append(params["model"])
            url_params["q"] = " ".join(search_terms)

        logger.debug("Generated URL parameters: %s", url_params)
        return f"{search_url}?{urlencode(url_params)}"

    def _get_request_headers(self) -> Dict[str, str]:
        """Return realistic headers to avoid bot detection"""
        return {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,de;q=0.8,fr;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }
    
    def _parse_query_with_llm(self, user_query: str) -> Dict[str, Any]:
        """Use LLM to parse user query and extract search parameters"""
        prompt = f"""Parse this car search query and extract AutoScout24 search parameters. Return ONLY a valid JSON object with these fields:

        {{
            "make": "brand name (bmw, mercedes-benz, audi, volkswagen, etc.)",
            "model": "model name (3-series, x5, a4, golf, etc.)",
            "url_path": "URL path for AutoScout24 (/bmw/3-series or empty string)",
            "price_from": "minimum price in euros (number or null)",
            "price_to": "maximum price in euros (number or null)",
            "year_from": "minimum year (number or null)",
            "year_to": "maximum year (number or null)",
            "mileage_to": "maximum mileage in km (number or null)",
            "fuel_type": "fuel type (petrol, diesel, electric, hybrid, or null)",
            "transmission": "transmission type (manual, automatic, or null)",
            "body_type": "body type (sedan, suv, hatchback, coupe, convertible, wagon, or null)",
            "condition": "condition (new, used, or null)",
            "sort": "sort order (price, year, mileage, or price)"
        }}

        User Query: "{user_query}"
        """

        if not self.llm_optimizer.client:
            logger.debug("LLM not available – returning empty param set")
            return {}

        logger.debug("Using LLM to parse search query")
        response = self.llm_optimizer.client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are an expert at parsing car search queries. Return only valid JSON, no explanations."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,
            max_tokens=500
        )

        llm_response = response.choices[0].message.content.strip()
        logger.debug("LLM parsed query successfully")

        if "```json" in llm_response:
            llm_response = llm_response.split("```json")[1].split("```")[0].strip()
        elif "```" in llm_response:
            llm_response = llm_response.split("```")[1].strip()

        parsed_params = json.loads(llm_response)
        logger.debug("Extracted parameters: %s", parsed_params)
        self._last_llm_params = parsed_params  # Store for API access
        return parsed_params
    
    def _extract_car_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract car data from AutoScout24 page"""
        cars = []

        listing_selectors = [
            'article[data-testid*="listing"]',
            'div[data-testid*="listing"]',
            '.as24-listing',
        ]

        listings = []
        for selector in listing_selectors:
            listings = soup.select(selector)
            if listings and len(listings) > 2:
                logger.debug(f"✅ Found {len(listings)} listings using selector: {selector}")
                break

        if not listings:
            logger.warning("⚠️ No listings found with standard selectors, trying fallback...")
            potential_listings = soup.find_all(['article', 'div'],
                                               class_=re.compile(r'(listing|item|card|vehicle)', re.I))
            if potential_listings:
                listings = potential_listings[:10]
                logger.debug(f"📦 Fallback found {len(listings)} potential listings")

        if not listings:
            logger.error("❌ No car listings found on page")
            return []

        for idx, listing in enumerate(listings[:15]):
            car_data = self._parse_comprehensive_listing(listing, idx + 1)
            if car_data and self._is_valid_car_listing(car_data):
                cars.append(car_data)
                if len(cars) >= 10:
                    break

        logger.debug("Successfully extracted %d valid car listings", len(cars))
        return cars
    
    def _parse_comprehensive_listing(self, listing, index: int) -> Dict[str, Any]:
        """Parse a single car listing with data extraction"""
        car = {
            'id': f'autoscout24_{index}',
            'title': '',
            'make': '',
            'model': '',
            'price': '',
            'currency': 'EUR',
            'year': '',
            'mileage': '',
            'fuel_type': '',
            'transmission': '',
            'power_hp': '',
            'power_kw': '',
            'body_type': '',
            'color': '',
            'doors': '',
            'seats': '',
            'location': '',
            'seller_name': '',
            'seller_type': '',
            'seller_phone': '',
            'seller_email': '',
            'description': '',
            'features': [],
            'link': '',
            'images': [],
            'condition': 'Used',
            'first_registration': '',
            'previous_owners': '',
            'inspection_valid': '',
            'emission_class': '',
            'co2_emissions': '',
            'fuel_consumption': '',
        }

        listing_text = listing.get_text(separator=' ', strip=True)

        title_selectors = [
            'h2[data-testid*="title"]',
            'h2.cldt-summary-makemodel',
            'h2.ListItem_title__znV2I',
            'h3[data-testid*="title"]',
            '.cldt-summary-makemodel',
            'h2', 'h3', 'h4',
            '[data-testid*="title"] a',
            'a[data-testid*="title"]'
        ]

        title_elem = None
        for selector in title_selectors:
            title_elem = listing.select_one(selector)
            if title_elem and title_elem.get_text(strip=True):
                break

        if title_elem:
            car['title'] = title_elem.get_text(strip=True)
            title_parts = car['title'].split()
            if len(title_parts) >= 2:
                car['make'] = title_parts[0]
                car['model'] = title_parts[1]
        
        price_selectors = [
            '[data-testid*="price"]',
            '.cldt-price',
            '.Price_price__WZayw',
            '.PriceInfo_price__XU3vw',
            '[class*="price"]',
            '.as24-price',
            '[data-testid="regular-price"]',
            '.price-block',
            '.listing-price'
        ]

        for selector in price_selectors:
            price_elem = listing.select_one(selector)
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price_match = re.search(r'€\s*([\d,.\s]+)', price_text)
                if not price_match:
                    price_match = re.search(r'([\d,.\s]+)\s*€', price_text)
                if not price_match:
                    price_match = re.search(r'([\d,.\s]+)', price_text)

                if price_match:
                    price_clean = price_match.group(1).replace(',', '').replace('.', '').replace(' ', '')
                    if price_clean.isdigit() and len(price_clean) >= 3:
                        car['price'] = price_clean
                        break

        if not car['price']:
            listing_text = listing.get_text()
            price_patterns = [
                r'€\s*([\d,.]+\.?\d*)',
                r'([\d,.]+\.?\d*)\s*€',
                r'(\d{1,3}(?:[,.]\d{3})*(?:[,.]\d{2})?)',
            ]

            for pattern in price_patterns:
                matches = re.findall(pattern, listing_text)
                for match in matches:
                    price_clean = match.replace(',', '').replace('.', '')
                    if price_clean.isdigit() and 1000 <= int(price_clean) <= 500000:
                        car['price'] = price_clean
                        break
                if car['price']:
                    break
        
        link_elem = listing.find('a', href=True)
        if link_elem:
            href = link_elem.get('href')
            if href:
                if href.startswith('/'):
                    car['link'] = f"{self.base_url}{href}"
                elif href.startswith('http'):
                    car['link'] = href
                else:
                    car['link'] = f"{self.base_url}/{href}"

        img_elements = listing.find_all('img')
        for img in img_elements:
            src = img.get('src') or img.get('data-src') or img.get('data-lazy-src')
            if src and 'autoscout24' in src and 'logo' not in src.lower():
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = self.base_url + src
                car['images'].append(src)

        self._extract_technical_details(listing_text, car)
        self._extract_seller_info(listing, car)
        self._extract_description_and_features(listing, car)

        return car
    
    def _extract_technical_details(self, text: str, car: Dict[str, Any]):
        """Extract technical details from listing text"""
        text_lower = text.lower()

        year_patterns = [r'(\d{4})', r'ez\s*(\d{2}/\d{4})', r'(\d{1,2})/(\d{4})']

        for pattern in year_patterns:
            match = re.search(pattern, text)
            if match:
                if len(match.groups()) == 1:
                    year = int(match.group(1))
                    if 1990 <= year <= 2025:
                        car['year'] = str(year)
                        break
                else:
                    year = int(match.group(2))
                    if 1990 <= year <= 2025:
                        car['year'] = str(year)
                        car['first_registration'] = f"{match.group(1)}/{match.group(2)}"
                        break

        mileage_patterns = [r'(\d{1,3}(?:[.,]\d{3})*)\s*km', r'(\d+[.,]?\d*)\s*km', r'km:\s*(\d+[.,]?\d*)']

        for pattern in mileage_patterns:
            match = re.search(pattern, text_lower)
            if match:
                mileage = match.group(1).replace(',', '').replace('.', '')
                if mileage.isdigit() and int(mileage) > 0:
                    car['mileage'] = f"{mileage} km"
                    break

        fuel_types = {
            'diesel': 'Diesel', 'benzin': 'Petrol', 'petrol': 'Petrol', 'gasoline': 'Petrol',
            'electric': 'Electric', 'elektro': 'Electric', 'hybrid': 'Hybrid',
            'plug-in': 'Plug-in Hybrid', 'lpg': 'LPG', 'cng': 'CNG', 'gas': 'Gas'
        }

        for fuel_key, fuel_value in fuel_types.items():
            if fuel_key in text_lower:
                car['fuel_type'] = fuel_value
                break

        if any(word in text_lower for word in ['automatic', 'automatik', 'auto', 'tiptronic', 'dsg', 's-tronic']):
            car['transmission'] = 'Automatic'
        elif any(word in text_lower for word in ['manual', 'schaltgetriebe', 'schalt', 'handschaltung']):
            car['transmission'] = 'Manual'

        power_patterns = [r'(\d+)\s*ps', r'(\d+)\s*hp', r'(\d+)\s*kw', r'(\d+)\s*ch']

        for pattern in power_patterns:
            match = re.search(pattern, text_lower)
            if match:
                power = int(match.group(1))
                if 'kw' in pattern:
                    car['power_kw'] = str(power)
                    car['power_hp'] = str(int(power * 1.36))
                else:
                    car['power_hp'] = str(power)
                    car['power_kw'] = str(int(power / 1.36))
                break

        doors_match = re.search(r'(\d+)\s*türen', text_lower)
        if doors_match:
            car['doors'] = doors_match.group(1)

        co2_match = re.search(r'(\d+)\s*g/km', text_lower)
        if co2_match:
            car['co2_emissions'] = f"{co2_match.group(1)} g/km"

        consumption_match = re.search(r'(\d+[.,]\d+)\s*l/100km', text_lower)
        if consumption_match:
            car['fuel_consumption'] = f"{consumption_match.group(1)} l/100km"
    
    def _extract_seller_info(self, listing, car: Dict[str, Any]):
        """Extract seller information"""
        seller_selectors = [
            '[data-testid*="seller"]',
            '.seller-info',
            '.dealer-info',
            '[class*="seller"]',
            '[class*="dealer"]'
        ]

        for selector in seller_selectors:
            seller_elem = listing.select_one(selector)
            if seller_elem:
                seller_text = seller_elem.get_text(strip=True)
                car['seller_name'] = seller_text[:100]
                break

        listing_text = listing.get_text().lower()
        if any(word in listing_text for word in ['händler', 'dealer', 'autohaus', 'gmbh', 'ag']):
            car['seller_type'] = 'Dealer'
        else:
            car['seller_type'] = 'Private'

        phone_pattern = r'(?:\+49|0049|0)\s*\d{2,4}[\s\-]?\d{6,10}'
        phone_match = re.search(phone_pattern, listing.get_text())
        if phone_match:
            car['seller_phone'] = phone_match.group(0)

        email_pattern = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
        email_match = re.search(email_pattern, listing.get_text())
        if email_match:
            car['seller_email'] = email_match.group(0)
    
    def _extract_description_and_features(self, listing, car: Dict[str, Any]):
        """Extract description and features"""
        desc_selectors = [
            '[data-testid*="description"]',
            '.description',
            '.vehicle-description',
            '[class*="description"]'
        ]

        for selector in desc_selectors:
            desc_elem = listing.select_one(selector)
            if desc_elem:
                car['description'] = desc_elem.get_text(strip=True)[:500]
                break

        features_selectors = [
            '[data-testid*="feature"]',
            '.features',
            '.equipment',
            '[class*="feature"]',
            '[class*="equipment"]'
        ]

        for selector in features_selectors:
            features_elem = listing.select_one(selector)
            if features_elem:
                features_text = features_elem.get_text()
                features = re.split(r'[,;•\n]', features_text)
                car['features'] = [f.strip() for f in features if f.strip()][:10]
                break
    
    def _is_valid_car_listing(self, car: Dict[str, Any]) -> bool:
        """Check if a car listing has minimum required data"""
        if not car.get('title'):
            return False

        title_lower = car['title'].lower()
        car_indicators = ['bmw', 'mercedes', 'audi', 'volkswagen', 'ford', 'opel', 'toyota', 'honda', 'nissan', 'mazda', 'hyundai', 'kia', 'skoda', 'seat', 'peugeot', 'citroen', 'renault', 'fiat']

        if not any(indicator in title_lower for indicator in car_indicators):
            return False

        exclude_words = ['cookies', 'privacy', 'navigation', 'footer', 'menu', 'login', 'register']
        if any(word in title_lower for word in exclude_words):
            return False

        return True
    
    def search(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Search for cars on AutoScout24"""
        logger.info("Searching AutoScout24 for: %s", query)

        search_url = self._build_url(self._parse_query_with_llm(query), limit)
        logger.debug("Search URL: %s", search_url)

        # Fetch page content using synchronous Zyte API call
        fetch_result = self._fetch_page_with_zyte(search_url)

        if fetch_result['status'] != 'success':
            return {
                'status': 'error',
                'error': fetch_result.get('error', 'Failed to fetch page'),
                'data': {'listings': []}
            }

        html_content = fetch_result['html_content']
        page_size = fetch_result['page_size']

        soup = BeautifulSoup(html_content, 'html.parser')

        # Persist scraped HTML for troubleshooting only when DEBUG log level is enabled
        if logger.isEnabledFor(logging.DEBUG):
            with open('debug_autoscout24.html', 'w', encoding='utf-8') as f:
                f.write(soup.prettify())
            logger.debug("Debug HTML saved to debug_autoscout24.html")

        cars = self._extract_car_data(soup)

        if not cars:
            logger.warning("No valid cars found, attempting alternative extraction...")
            cars = self._extract_fallback_data(soup)

        result = {
            'status': 'success',
            'query': query,
            'total_found': len(cars),
            'data': {
                'listings': cars,
                'search_url': search_url,
                'extraction_method': 'comprehensive'
            },
            'metadata': {
                'search_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                'page_size': page_size,
                'cars_extracted': len(cars)
            }
        }

        logger.info("Extraction complete: %d cars found", len(cars))
        return result
    
    def _extract_fallback_data(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Fallback extraction method"""
        cars = []
        text_content = soup.get_text()

        car_patterns = [
            r'((?:BMW|Mercedes|Audi|VW|Volkswagen|Ford|Opel|Toyota|Honda|Nissan)\s+[\w\d\s-]+).*?(\d{1,3}[.,]?\d{0,3})\s*€',
            r'((?:BMW|Mercedes|Audi|VW|Volkswagen|Ford|Opel|Toyota|Honda|Nissan)\s+[\w\d\s-]+).*?(\d{4}).*?(\d+)\s*km'
        ]

        for pattern in car_patterns:
            matches = re.finditer(pattern, text_content, re.IGNORECASE)
            for match in matches:
                if len(cars) >= 5:
                    break

                car = {
                    'id': f'fallback_{len(cars) + 1}',
                    'title': match.group(1).strip(),
                    'price': match.group(2) if len(match.groups()) > 1 else 'N/A',
                    'year': '',
                    'mileage': '',
                    'fuel_type': '',
                    'transmission': '',
                    'link': self.base_url,
                    'seller_type': 'Unknown',
                    'description': 'Extracted from page content',
                    'images': [],
                    'features': []
                }

                if len(match.groups()) > 2:
                    car['year'] = match.group(3) if match.group(3).isdigit() else ''
                    if len(match.groups()) > 3:
                        car['mileage'] = f"{match.group(4)} km"

                cars.append(car)

        return cars

    def _fetch_page_with_zyte(self, url: str) -> Dict[str, Any]:
        """Fetch page content using Zyte API with synchronous HTTP"""
        if not self.zyte_api_key:
            raise FetchError("Zyte API key not configured. Please set ZYTE_API_KEY in your .env file.")

        logger.debug("Making request with Zyte API: %s", url)

        try:
            response = requests.post(
                "https://api.zyte.com/v1/extract",
                auth=(self.zyte_api_key, ""),
                json={
                    "url": url,
                    "httpResponseBody": True,
                    "followRedirect": True,
                },
                timeout=60,
            )
            response.raise_for_status()

            html_content_b64 = response.json()["httpResponseBody"]
            html_content = base64.b64decode(html_content_b64)
            logger.debug("Page fetched via Zyte (bytes=%d)", len(html_content))
            return {
                "html_content": html_content,
                "page_size": len(html_content),
                "status": "success",
            }
        except requests.exceptions.RequestException as exc:
            raise FetchError(f"Zyte request failed: {exc}") from exc

    # ------------------------------------------------------------------
    # Async helpers (httpx + asyncio)

    async def _fetch_page_with_zyte_async(self, url: str) -> Dict[str, Any]:
        """Asynchronous variant of Zyte fetch using httpx.AsyncClient"""
        if not self.zyte_api_key:
            raise FetchError("Zyte API key not configured. Please set ZYTE_API_KEY in your .env file.")

        logger.debug("[async] Making request with Zyte API: %s", url)

        try:
            async with httpx.AsyncClient(timeout=60) as client:
                response = await client.post(
                    "https://api.zyte.com/v1/extract",
                    auth=(self.zyte_api_key, ""),
                    json={
                        "url": url,
                        "httpResponseBody": True,
                        "followRedirect": True,
                    },
                )

            response.raise_for_status()

            html_content_b64 = response.json()["httpResponseBody"]
            html_content = base64.b64decode(html_content_b64)
            logger.debug("[async] Page fetched via Zyte (bytes=%d)", len(html_content))
            return {
                "html_content": html_content,
                "page_size": len(html_content),
                "status": "success",
            }
        except httpx.HTTPError as exc:
            raise FetchError(f"Zyte async request failed: {exc}") from exc

    # ------------------------------------------------------------------
    # Async public APIs

    async def search_by_params_async(self, params: Dict[str, Any], limit: int | None = None) -> Dict[str, Any]:
        """Async version of search_by_params using httpx + asyncio."""

        params = {k: v for k, v in params.items() if v is not None}
        limit = limit or int(params.pop("limit", 10))

        logger.info("[Direct] AutoScout24 search params=%s limit=%d", params, limit)

        search_url = self._build_url(params, limit)
        logger.debug("[async] Search URL: %s", search_url)

        fetch_result = await self._fetch_page_with_zyte_async(search_url)

        html_content = fetch_result["html_content"]
        page_size = fetch_result["page_size"]

        soup = BeautifulSoup(html_content, "html.parser")

        if logger.isEnabledFor(logging.DEBUG):
            with open("debug_autoscout24_direct_async.html", "w", encoding="utf-8") as fh:
                fh.write(soup.prettify())
            logger.debug("[async] Debug HTML saved to debug_autoscout24_direct_async.html")

        cars = self._extract_car_data(soup)
        if not cars:
            logger.warning("[async] No cars via primary method – running fallback")
            cars = self._extract_fallback_data(soup)

        return {
            "status": "success",
            "query": "direct_search",
            "total_found": len(cars),
            "data": {
                "listings": cars,
                "search_url": search_url,
                "extraction_method": "direct",
            },
            "metadata": {
                "search_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "page_size": page_size,
                "cars_extracted": len(cars),
            },
        }

    async def search_async(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """Async variant of LLM-powered search."""
        logger.info("[async] Searching AutoScout24 for: %s", query)

        search_url = self._build_url(self._parse_query_with_llm(query), limit)
        logger.debug("[async] Search URL: %s", search_url)

        fetch_result = await self._fetch_page_with_zyte_async(search_url)

        html_content = fetch_result["html_content"]
        page_size = fetch_result["page_size"]

        soup = BeautifulSoup(html_content, "html.parser")

        if logger.isEnabledFor(logging.DEBUG):
            with open("debug_autoscout24_async.html", "w", encoding="utf-8") as f:
                f.write(soup.prettify())
            logger.debug("[async] Debug HTML saved to debug_autoscout24_async.html")

        cars = self._extract_car_data(soup)
        if not cars:
            logger.warning("[async] No valid cars found, attempting alternative extraction…")
            cars = self._extract_fallback_data(soup)

        result = {
            "status": "success",
            "query": query,
            "total_found": len(cars),
            "data": {
                "listings": cars,
                "search_url": search_url,
                "extraction_method": "comprehensive",
            },
            "metadata": {
                "search_timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "page_size": page_size,
                "cars_extracted": len(cars),
            },
        }

        logger.info("[async] Extraction complete: %d cars found", len(cars))
        return result