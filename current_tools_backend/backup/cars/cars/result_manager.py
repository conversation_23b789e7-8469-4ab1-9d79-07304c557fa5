from pathlib import Path
from datetime import datetime
from typing import Any, Dict, List

from cars.logger import logger
from cars.config import _settings as cfg
from reports.markdown import generate_markdown_report
from storage.filesystem import ensure_dir, safe_filename, save_json, save_csv


class ResultManager:
    """Manages storage and retrieval of search results"""

    def __init__(self):
        self.results_folder: Path = ensure_dir(Path(cfg.RESULTS_FOLDER))

    def _ensure_results_folder(self) -> None:
        """Create results folder if it doesn't exist"""
        pass

    def save_search_results(self, original_query: str, optimized_query: str,
                           parameters: Dict[str, Any], results: Dict[str, Any],
                           analysis: str) -> str:
        """Save search results and analysis to a markdown file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"search_{timestamp}_{safe_filename(original_query)}.md"
        filepath = self.results_folder / filename

        items = results.get('data', {}).get('listings', [])
        total_results = results.get('data', {}).get('total', len(items))
        status = results.get('status', 'unknown')

        markdown_content = generate_markdown_report(
            original_query, optimized_query, parameters, items,
            total_results, status, analysis, timestamp
        )

        filepath.write_text(markdown_content, encoding="utf-8")
        logger.info("Markdown saved: %s", filepath)
        return str(filepath)
    
    def save_raw_json(self, query: str, results: Dict[str, Any]) -> str:
        """Save raw JSON results for debugging"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"raw_{timestamp}_{safe_filename(query)}.json"
        filepath = self.results_folder / filename

        save_json(filepath, results)
        return str(filepath)
    
    def save_comprehensive_results(self, query: str, results: Dict[str, Any]) -> str:
        """Save comprehensive AutoScout24 search results to JSON"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"autoscout24_{timestamp}_{safe_filename(query)}.json"
        filepath = self.results_folder / filename

        save_data = {
            'search_info': {
                'query': query,
                'timestamp': datetime.now().isoformat(),
                'total_found': results.get('data', {}).get('total_found', 0),
                'status': results.get('status', 'unknown'),
                'search_url': results.get('data', {}).get('search_url', '')
            },
            'metadata': results.get('metadata', {}),
            'cars': results.get('data', {}).get('listings', [])
        }

        save_json(filepath, save_data)
        return str(filepath)
    
    def save_csv_results(self, query: str, results: Dict[str, Any]) -> str:
        """Save car search results to CSV for easy viewing"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cars_{timestamp}_{safe_filename(query)}.csv"
        filepath = self.results_folder / filename

        cars: List[Dict[str, Any]] = results.get("data", {}).get("listings", [])
        if not cars:
            logger.warning("No cars to save to CSV")
            return ""

        save_csv(filepath, cars)
        return str(filepath)