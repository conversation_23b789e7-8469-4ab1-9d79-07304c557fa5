#!/usr/bin/env python3
"""
FastAPI application for AutoScout24 car search
Provides two endpoints: direct search with parameters and LLM-powered search
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query, Depends
from fastapi.responses import JSONResponse
from functools import lru_cache
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any, List
import uvicorn
from datetime import datetime
import logging

from cars.platforms.autoscout24 import AutoScout24Searcher
from cars.llm_service import LLMQueryOptimizer
from cars.config import Config
from cars.exceptions import FetchError

# Initialise project-wide logging configuration early
import cars.logger  # noqa: F401 – side-effect: configures logging

logger = logging.getLogger(__name__)

app = FastAPI(
    title="AutoScout24 Car Search API",
    description="Search for cars on AutoScout24 with direct parameters or LLM-powered natural language queries",
    version="1.0.0"
)

# Dependency injection helpers ------------------------------------------------


@lru_cache
def get_searcher() -> "AutoScout24Searcher":  # noqa: quotes for forward ref
    from cars.platforms.autoscout24 import AutoScout24Searcher
    return AutoScout24Searcher()


@lru_cache
def get_llm() -> "LLMQueryOptimizer":
    from cars.llm_service import LLMQueryOptimizer
    return LLMQueryOptimizer()


from cars.config import missing_credentials as _missing_credentials  # after env load

# note: instances are provided via dependency injection


# ---------------------------------------------------------------------------
# Pydantic models


class SearchParams(BaseModel):
    """Direct search parameters for AutoScout24"""
    model_config = ConfigDict(populate_by_name=True)
    make: Optional[str] = Field(None, description="Car make (e.g., bmw, audi, mercedes)")
    model: Optional[str] = Field(None, description="Car model (e.g., x5, a4, c-class)")
    price_from: Optional[int] = Field(None, description="Minimum price in EUR", ge=0)
    price_to: Optional[int] = Field(None, description="Maximum price in EUR", ge=0)
    year_from: Optional[int] = Field(None, description="Minimum year", ge=1990, le=2025)
    year_to: Optional[int] = Field(None, description="Maximum year", ge=1990, le=2025)
    mileage_to: Optional[int] = Field(None, description="Maximum mileage in km", ge=0)
    fuel_type: Optional[str] = Field(None, description="Fuel type: petrol, diesel, electric, hybrid")
    transmission: Optional[str] = Field(None, description="Transmission: manual, automatic")
    body_type: Optional[str] = Field(None, description="Body type: sedan, suv, hatchback, coupe, convertible, wagon")
    condition: Optional[str] = Field(None, description="Condition: new, used")
    sort: Optional[str] = Field("price", description="Sort order: price, year, mileage")
    limit: Optional[int] = Field(10, description="Number of results to return", ge=1, le=20)
    zyte_api_key: Optional[str] = Field(None, description="Override Zyte API key for this request")
    openai_api_key: Optional[str] = Field(None, description="Override OpenAI API key for this request")


class LLMSearchRequest(BaseModel):
    """LLM-powered search request"""
    query: str = Field(..., description="Natural language search query (e.g., 'family car under 20000')")
    limit: Optional[int] = Field(10, description="Number of results to return", ge=1, le=20)
    zyte_api_key: Optional[str] = Field(None, description="Override Zyte API key for this request")
    openai_api_key: Optional[str] = Field(None, description="Override OpenAI API key for this request")


class SearchResponse(BaseModel):
    """Search response model"""
    model_config = ConfigDict(from_attributes=True)
    status: str
    query: str
    total_found: int
    cars: List[Dict[str, Any]]
    search_url: str
    metadata: Dict[str, Any]
    extracted_parameters: Optional[Dict[str, Any]] = None


# ---------------------------------------------------------------------------
# Routes


@app.get("/")
async def root():
    """Root endpoint – returns human- and machine-readable overview of the API.

    Useful for LLM agents that need a JSON contract describing what they can call.
    """

    # Helper to pull parameter descriptions from Pydantic models ----------------
    def _field_map(model_cls):
        return {
            name: (field.description or "")
            for name, field in model_cls.model_fields.items()
        }

    return {
        "message": "AutoScout24 Car Search API",
        "version": "1.0.0",
        "usage": "Search AutoScout24 either via explicit parameters or natural-language queries processed by an LLM.",
        "endpoints": {
            "/search": {
                "method": "POST",
                "description": "Direct search with explicit AutoScout24 parameters (no LLM processing).",
                "body_parameters": _field_map(SearchParams),
            },
            "/search/llm": {
                "method": "POST",
                "description": "Natural-language search routed through an LLM to extract search parameters before scraping.",
                "body_parameters": _field_map(LLMSearchRequest),
            },
            "/health": {
                "method": "GET",
                "description": "Basic health probe plus credential availability information.",
                "query_parameters": {},
            },
            "/docs": {
                "method": "GET",
                "description": "Swagger UI automatically generated by FastAPI.",
            },
            "/openapi.json": {
                "method": "GET",
                "description": "Machine-readable OpenAPI schema.",
            },
        },
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    missing_credentials = _missing_credentials()
    
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "autoscout24": "available",
            "zyte_api": "configured" if Config.ZYTE_API_KEY else "not_configured",
            "openai": "configured" if not missing_credentials else "not_configured"
        },
        "missing_credentials": missing_credentials
    }


@app.post("/search", response_model=SearchResponse)
async def search_with_parameters(
    params: SearchParams,
    car_searcher=Depends(get_searcher),
):
    """
    Search for cars using direct parameters
    
    This endpoint allows you to search with specific parameters without LLM processing.
    Useful when you already know the exact search criteria.
    """
    try:
        payload = params.model_dump(exclude_none=True)

        zyte_key = payload.pop("zyte_api_key", None)
        openai_key = payload.pop("openai_api_key", None)

        # Instantiate searcher with overrides if provided; otherwise use DI cached instance
        search_engine = (
            AutoScout24Searcher(zyte_key, openai_key)
            if (zyte_key or openai_key)
            else car_searcher
        )

        # Prefer async path when available
        if hasattr(search_engine, "search_by_params_async"):
            result = await search_engine.search_by_params_async(payload)
        else:
            # Fallback to sync (shouldn't happen)
            result = search_engine.search_by_params(payload)

        return SearchResponse(
            status=result["status"],
            query="direct_search",
            total_found=result.get("total_found", 0),
            cars=result.get("data", {}).get("listings", []),
            search_url=result.get("data", {}).get("search_url", ""),
            metadata=result.get("metadata", {}),
            extracted_parameters=params.model_dump(exclude_none=True),
        )
    except FetchError as fe:
        # Upstream fetch failed – surface as Bad Gateway
        logger.warning("Fetch error while searching: %s", fe)
        raise HTTPException(status_code=502, detail="Upstream fetch failed")
    except ValueError as ve:
        logger.warning("Validation error during search params: %s", ve)
        raise HTTPException(status_code=422, detail=str(ve))


@app.post("/search/llm", response_model=SearchResponse)
async def search_with_llm(
    request: LLMSearchRequest,
    car_searcher=Depends(get_searcher),
):
    """
    Search for cars using natural language query with LLM processing
    
    This endpoint processes natural language queries using LLM to extract
    search parameters automatically. Perfect for user-friendly searches.
    
    Examples:
    - "family car under 20000"
    - "BMW X5 from 2018 diesel automatic"
    - "Mercedes C-Class between 15000 and 30000"
    """
    try:
        zyte_key = request.zyte_api_key
        openai_key = request.openai_api_key

        search_engine = (
            AutoScout24Searcher(zyte_key, openai_key)
            if (zyte_key or openai_key)
            else car_searcher
        )

        # Prefer async path
        if hasattr(search_engine, "search_async"):
            result = await search_engine.search_async(request.query, limit=request.limit)
        else:
            result = search_engine.search(request.query, limit=request.limit)
        
        if result.get('status') != 'success':
            raise HTTPException(status_code=500, detail=result.get('error', 'Search failed'))
        
        # Extract LLM parameters if available
        extracted_params = None
        if hasattr(search_engine, '_last_llm_params'):
            extracted_params = search_engine._last_llm_params
        
        return SearchResponse(
            status=result['status'],
            query=request.query,
            total_found=result.get('total_found', 0),
            cars=result.get('data', {}).get('listings', []),
            search_url=result.get('data', {}).get('search_url', ''),
            metadata=result.get('metadata', {}),
            extracted_parameters=extracted_params
        )
        
    except FetchError as fe:
        logger.warning("Fetch error during LLM search: %s", fe)
        raise HTTPException(status_code=502, detail="Upstream fetch failed")


@app.get("/search/examples")
async def get_search_examples():
    """Get example queries and parameters for both endpoints"""
    return {
        "direct_search_examples": {
            "bmw_x5": {
                "make": "bmw",
                "model": "x5",
                "price_to": 50000,
                "year_from": 2015
            },
            "family_car": {
                "body_type": "suv",
                "price_to": 25000,
                "fuel_type": "diesel",
                "transmission": "automatic"
            },
            "luxury_sedan": {
                "body_type": "sedan",
                "price_from": 30000,
                "year_from": 2018,
                "fuel_type": "petrol"
            }
        },
        "llm_search_examples": [
            "family car under 20000",
            "BMW X5 from 2018 diesel automatic",
            "Mercedes C-Class between 15000 and 30000",
            "Audi A4 diesel under 25000",
            "electric car under 40000",
            "sports car BMW",
            "reliable family SUV under 30000 euros"
        ]
    }


if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
