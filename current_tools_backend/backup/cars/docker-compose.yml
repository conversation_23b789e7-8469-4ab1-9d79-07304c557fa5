version: '3.8'

services:
  autoscout24-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ZYTE_API_KEY=${ZYTE_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-o3-mini}
    volumes:
      - ./results:/app/results
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
