"""Filesystem utilities for saving search results."""
from __future__ import annotations

import json
import csv
import logging
from pathlib import Path
from typing import Any, Dict, List

from slugify import slugify  # python-slugify

logger = logging.getLogger(__name__)


def ensure_dir(path: Path) -> Path:
    path.mkdir(parents=True, exist_ok=True)
    return path


def safe_filename(name: str, max_len: int = 30) -> str:
    return slugify(name)[:max_len]


def save_json(path: Path, data: Any) -> None:
    path.write_text(json.dumps(data, indent=2, ensure_ascii=False), encoding="utf-8")
    logger.debug("JSON saved: %s", path)


def save_csv(path: Path, rows: List[Dict[str, Any]]) -> None:
    if not rows:
        logger.warning("No rows to write to CSV: %s", path)
        return

    header = rows[0].keys()
    with path.open("w", newline="", encoding="utf-8") as fh:
        writer = csv.DictWriter(fh, fieldnames=header)
        writer.writeheader()
        writer.writerows(rows)
    logger.debug("CSV saved: %s", path) 