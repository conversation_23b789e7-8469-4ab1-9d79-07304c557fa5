# 🚗 AutoScout24 Car Search with LLM Optimization

A powerful car search application that uses OpenAI's o3 model to optimize search queries and analyze AutoScout24 car listings results.

## ✨ Features

- **🤖 LLM Query Optimization**: Uses OpenAI o3-mini to optimize car search queries for better results
- **🚗 AutoScout24 Integration**: Searches Europe's largest car marketplace through web scraping
- **🔄 Smart Retry Mechanism**: Automatically retries with broader queries if no results found
- **📈 Intelligent Analysis**: AI-powered analysis of car search results with market insights
- **💾 Result Management**: Saves search results in markdown and JSON formats
- **🎨 Beautiful CLI**: Colorful command-line interface with emojis and clear formatting
- **🌍 Multi-platform**: Works on Windows, macOS, and Linux

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd seconde-hand
   ```

2. **Create virtual environment**
```bash
python -m venv ven
source ven/bin/activate  # On Windows: ven\Scripts\activate
```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure OpenAI API (Optional)**
Update the `OPENAI_API_KEY` in `config.py` or create a `.env` file:
```env
OPENAI_API_KEY=your_openai_api_key_here
```

## 🚀 Usage

### 🌐 FastAPI REST API (Recommended)

The project includes a FastAPI-based REST API with two powerful endpoints:

#### Start the API Server
```bash
python run_api.py
```

The API will be available at:
- **API Base**: http://localhost:8000
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

#### 1. Direct Parameter Search
```bash
curl -X POST http://localhost:8000/search \
  -H "Content-Type: application/json" \
  -d '{"make": "bmw", "model": "x5", "price_to": 50000, "limit": 5}'
```

#### 2. LLM-Powered Natural Language Search
```bash
curl -X POST http://localhost:8000/search/llm \
  -H "Content-Type: application/json" \
  -d '{"query": "family car under 20000", "limit": 5}'
```

#### Example Usage Script
```bash
python example_usage.py
```

📖 **See [API_README.md](API_README.md) for complete API documentation.**

### 🖥️ Interactive Command Line Mode

```bash
python main.py --interactive
```

### Command Line Mode
```bash
# Search for a specific car
python main.py --query "BMW X5"

# Family car search
python main.py --query "family car under 20000"

# Sports car search
python main.py --query "Porsche 911"

# Search without saving results
python main.py --query "Audi A4" --no-save

# Show only summary
python main.py --query "Mercedes C-Class" --summary-only
```

### Validate Setup
```bash
python main.py --validate
```

## 🎯 Search Examples

The system understands various car search intents:

- **By Brand & Model**: "BMW X5", "Audi A4", "Mercedes C-Class"
- **By Budget**: "car under 15000", "luxury car under 50000"
- **By Type**: "family car", "sports car", "SUV", "electric car"
- **By Fuel**: "diesel BMW", "electric Audi", "hybrid Toyota"
- **Complex Queries**: "reliable family SUV under 25000 euros"

## 📊 LLM Optimization Features

### Query Enhancement
- Converts natural language to optimal car search terms
- Suggests appropriate filters (price, year, fuel type, etc.)
- Handles typos and informal language

### Smart Fallback
- Automatically broadens search if no results found
- Uses car-specific fallback strategies
- Maintains search intent while increasing scope

### Market Analysis
- Price analysis and market trends
- Car model recommendations
- Risk assessment for potential purchases
- Fuel efficiency and environmental insights

## 🏗️ Architecture

```
├── main.py                 # Main CLI application entry point
├── api.py                  # FastAPI REST API server
├── run_api.py             # API startup script
├── example_usage.py       # API usage examples
├── cars/
│   ├── config.py          # Configuration and settings
│   ├── llm_service.py     # OpenAI LLM integration
│   ├── prompts.py         # LLM prompts for car search
│   ├── result_manager.py  # Result saving and management
│   └── platforms/
│       ├── base.py        # Base searcher class
│       └── autoscout24.py # AutoScout24 web scraper
├── results/               # Saved search results
├── Dockerfile             # Docker container setup
├── docker-compose.yml     # Docker Compose configuration
└── API_README.md          # Complete API documentation
```

## 🔧 Configuration

### AutoScout24 Settings
- **Base URL**: https://www.autoscout24.com
- **Country**: Germany (DE) - can be changed in config
- **No API Key Required**: Uses web scraping

### OpenAI Settings
- **Model**: o3-mini (configurable)
- **Optional**: Works without OpenAI for basic search

### Search Parameters
- **Default Limit**: 10 cars per search
- **Timeout**: 30 seconds
- **Results Folder**: `results/`

## 🌟 Advanced Features

### LLM-Powered Car Analysis
- Market value assessment
- Price-per-kilometer analysis
- Fuel type distribution
- Brand reliability insights
- Model-specific recommendations

### Smart Parameter Extraction
Automatically detects and applies:
- Price ranges from natural language
- Year preferences
- Fuel type requirements
- Transmission preferences
- Mileage limits

### Retry Logic
- **Attempt 1**: Optimized LLM query
- **Attempt 2**: Broader car search
- **Attempt 3**: Brand-only search
- **Attempt 4**: Generic "car" search

## 📝 Sample Output

```
🚗 AUTOSCOUT24 CAR SEARCH WITH LLM OPTIMIZATION
============================================================
Powered by OpenAI o3-mini and AutoScout24 Web Scraping

🚗 Enter your car search query: BMW X5

🤖 Optimizing car query with LLM (with retry fallback)...
LLM Optimization for autoscout24:
  Original: BMW X5
  Optimized: BMW X5
  Parameters: {'price_max': 50000, 'year_min': 2015}

✅ Car search successful after 1 attempt(s)!
   📊 Total cars found: 156
   🚗 Cars retrieved: 10
   🔄 Attempts used: 1/4

🚗 Quick Car Results Summary
==================================================
1. BMW X5 xDrive30d M Sport
   💰 €45,900 | 📅 2020 | 🛣️ 89,000 km | ⛽ Diesel
```

## 🔍 Web Scraping Details

The AutoScout24 scraper:
- Uses realistic browser headers
- Implements random delays to avoid detection
- Handles dynamic HTML structures
- Extracts comprehensive car information
- Provides fallback text extraction methods

## 🛡️ Anti-Bot Protection

If you encounter blocking:
1. Use VPN or proxy services
2. Reduce search frequency
3. Consider using residential proxies
4. Add longer delays between requests

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## ⚠️ Disclaimer

This tool is for educational and research purposes. Always respect AutoScout24's terms of service and use responsibly. Consider rate limiting and respectful scraping practices. 