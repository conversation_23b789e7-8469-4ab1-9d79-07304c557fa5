from fastapi import FastAP<PERSON>, Query, HTTPException
from fastapi.responses import JSONResponse
from typing import Optional, List
from dataclasses import asdict, is_dataclass
import asyncio

from scrapers.async_scraper_service import AsyncCostcoScraper

app = FastAPI(
    title="Costco Search Scraper API",
    description=(
        "Tool-style API intended for LLM agents. "
        "It performs a live Costco search and returns JSON with (1) all listing products "
        "and (2) detailed information for the top K products."
    ),
    version="0.1.0",
)


@app.on_event("startup")
async def startup_event():
    # dict[str, AsyncCostcoScraper]
    app.state.scraper_cache = {}
    app.state.cache_lock = asyncio.Lock()

@app.on_event("shutdown")
async def shutdown_event():
    # Nothing to close in AsyncCostcoScraper (no persistent connections),
    # but we clear the cache for completeness.
    app.state.scraper_cache.clear()


@app.get("/", tags=["meta"], summary="API manual for the LLM tool")
async def root_manual():
    """Return a machine-readable manual describing the tool and its parameters."""
    manual = {
        "name": "Costco Search Scraper API",
        "description": (
            "Search Costco and obtain product information. "
            "The /search endpoint executes the flow that you would normally trigger via main.py."
        ),
        "endpoints": {
            "/search": {
                "method": "GET",
                "description": "Run a Costco search and return JSON with listing + detailed data.",
                "parameters": {
                    "query": "string – required – search keywords",
                    "max_pages": "integer – optional – number of search pages to crawl (1-10). Default 3",
                    "limit": "integer – optional – number of top products to parse in detail (1-50). Default 5",
                    "scrapingbee_api_key": "string – required – your ScrapingBee API key",
                },
                "response": {
                    "success": "bool",
                    "total_found": "int – number of detailed products returned (== limit unless some pages failed)",
                    "total_listing": "int – number of products in the listing (max 20)",
                    "search_url": "string – Costco search URL used",
                    "products": "array – list of detailed product objects (see ProductInfo dataclass)",
                    "listing_products": "array – basic info for top 20 products in the search page",
                },
            }
        },
    }
    return manual


@app.get("/search", tags=["search"], summary="Search Costco and fetch product data")
async def search_products(
    query: str = Query(..., description="Search keyword(s)", min_length=1),
    scrapingbee_api_key: str = Query(..., description="Your ScrapingBee API key"),
    max_pages: int = Query(3, ge=1, le=10, description="Number of search pages to crawl (1-10)"),
    limit: int = Query(5, ge=1, le=50, description="Number of products to parse in detail (1-50)"),
):
    # Re-use scraper from cache if available
    async with app.state.cache_lock:
        scraper = app.state.scraper_cache.get(scrapingbee_api_key)
        if scraper is None:
            scraper = AsyncCostcoScraper(scrapingbee_api_key=scrapingbee_api_key)
            app.state.scraper_cache[scrapingbee_api_key] = scraper
    
    result = await scraper.scrape_products(query=query, max_pages=max_pages, limit=limit)

    # Convert dataclass instances to dictionaries for JSON serialisation
    if result.get("success") and "products" in result:
        detailed_products = result["products"]
        result["products"] = [asdict(p) if is_dataclass(p) else p for p in detailed_products]

    return JSONResponse(content=result) 