from __future__ import annotations

"""Data models for the Costco scraper.

Lightweight models focused on the fields that are currently populated by the scrapers.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List


@dataclass
class Product:
    """Lightweight representation of a Costco product."""

    name: str
    sku: str
    price: str
    product_url: str

    # Optional marketing / context fields
    model: str = ""
    average_rating: str = ""
    total_ratings: str = ""
    main_image: str = ""
    
    # Additional product details
    features: List[str] = field(default_factory=list)
    feature_highlights: List[str] = field(default_factory=list)
    thumbnail_images: List[str] = field(default_factory=list)
    
    # Delivery information
    delivery_date: str = ""
    delivery_zip: str = ""
    delivery_heading: str = ""
    delivery_suffix: str = ""
    delivery_disclaimer: str = ""
    
    # Raw scraped data for additional processing
    raw_extracted_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchQuery:
    """Wrapper around a query the user wants to run on Costco."""

    query: str
    max_pages: int = 3
    limit: int = 5  # Number of products to scrape in detail

    def to_url_fragment(self) -> str:
        """Return URL parameters suitable for Costco search URLs."""
        return self.query.replace(" ", "%20") 