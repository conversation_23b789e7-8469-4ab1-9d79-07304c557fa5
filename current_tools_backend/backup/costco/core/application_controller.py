"""
Application Controller - Business Logic Layer
Handles the coordination between UI and scrapers
"""

import asyncio
import logging
from typing import Dict, Any, Optional
import os

from scrapers.async_scraper_service import Async<PERSON>ostcoScraper
from utils.console_ui import ConsoleUI


class ApplicationController:
    """Main application controller for Costco scraper"""
    
    def __init__(self):
        """Initialize the application controller."""
        self.logger = logging.getLogger(__name__)
        self.ui = ConsoleUI()
        
        # Get ScrapingBee API key from environment
        scrapingbee_key = os.getenv('SCRAPINGBEE_API_KEY')
        
        # Initialize async scraper
        self.async_scraper = AsyncCostcoScraper(scrapingbee_key)
    
    async def run_async_scraper(self, query: str, max_pages: int, limit: int) -> Dict[str, Any]:
        """
        Run the async scraper with the provided parameters.
        
        Args:
            query: Search query
            max_pages: Maximum number of pages to crawl
            limit: Maximum number of products to scrape in detail
            
        Returns:
            Dictionary containing scraping results
        """
        try:
            self.ui.print_header("🚀 ASYNC SCRAPER")
            
            # Get user input
            query_params = {
                'query': query,
                'max_pages': max_pages,
                'limit': limit
            }
            
            # Display parameters
            self.ui.display_parameters_summary(query_params)
            
            print("🚀 Initializing Async Scraper...")
            
            # Run scraper directly
            result = await self.async_scraper.scrape_products(
                query=query,
                max_pages=max_pages,
                limit=limit
            )
            
            # Display results
            self.ui.display_scraping_results(result)
            
            return result
                
        except Exception as e:
            error_msg = f"Async scraper error: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            print(f"❌ Async Scraper Error: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "products": [],
                "listing_products": []
            }

    def run_main_menu(self):
        """Run the main application menu"""
        while True:
            self.ui.print_main_menu()

            try:
                choice = input("Choose an option (1-2): ").strip()
            except KeyboardInterrupt:
                self.ui.print_goodbye()
                break

            if choice == "1":
                params = self.ui.get_scraping_parameters()
                if params:
                    asyncio.run(
                        self.run_async_scraper(
                            params["query"],
                            params["max_pages"],
                            params["limit"],
                        )
                    )
                self.ui.wait_for_user()
            elif choice == "2":
                self.ui.print_goodbye()
                break
            else:
                self.ui.display_warning("Invalid choice. Please select 1 or 2.") 