from __future__ import annotations

"""Light wrapper around the ScrapingBee API used by the scraper.

Usage:
    client = ScrapingBeeClient.from_env()
    html = await client.fetch_html("https://example.com")
"""

import asyncio
import os
from typing import Optional, Dict, Any

from scrapingbee import ScrapingBeeClient as SBClient
from dotenv import load_dotenv


class ScrapingBeeClient:
    """Async wrapper for the ScrapingBee API."""

    def __init__(self, api_key: str):
        self._api_key = api_key
        self._client = SBClient(api_key=api_key)

    async def fetch_html(self, url: str, *, retries: int = 3, js_scenario: Optional[Dict] = None, extract_rules: Optional[Dict] = None) -> Optional[str]:
        """Return the rendered HTML for *url* or ``None`` on failure."""

        params = {
            'stealth_proxy': True,
            'country_code': 'us',
        }
        
        if js_scenario:
            params['js_scenario'] = js_scenario
            
        if extract_rules:
            params['extract_rules'] = extract_rules

        for attempt in range(retries):
            if attempt:
                await asyncio.sleep(2)
            try:
                # ScrapingBee client is sync, so we run it in a thread
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None, 
                    lambda: self._client.get(url, params=params)
                )
                
                if response.status_code == 200:
                    return response.text
            except Exception:
                # swallow, will retry or return None
                pass
        return None

    async def fetch_with_extraction(self, url: str, extract_rules: Dict[str, Any], *, retries: int = 3, js_scenario: Optional[Dict] = None) -> Optional[Dict]:
        """Return the extracted data for *url* or ``None`` on failure."""

        params = {
            'stealth_proxy': True,
            'country_code': 'us',
            'extract_rules': extract_rules
        }
        
        if js_scenario:
            params['js_scenario'] = js_scenario

        for attempt in range(retries):
            if attempt:
                await asyncio.sleep(2)
            try:
                # ScrapingBee client is sync, so we run it in a thread
                loop = asyncio.get_event_loop()
                response = await loop.run_in_executor(
                    None, 
                    lambda: self._client.get(url, params=params)
                )
                
                if response.status_code == 200:
                    # Return both extracted data and response object for status info
                    return {
                        'extracted_data': response.json() if hasattr(response, 'json') else {},
                        'html': response.text,
                        'status_code': response.status_code
                    }
            except Exception:
                # swallow, will retry or return None
                pass
        return None

    # ---------------------------------------------------------------------
    # Helpers
    # ---------------------------------------------------------------------
    @staticmethod
    def from_env() -> Optional["ScrapingBeeClient"]:
        # Ensure .env is loaded so keys become available in os.environ
        load_dotenv()
        key = os.getenv("SCRAPINGBEE_API_KEY")
        return ScrapingBeeClient(key) if key else None 