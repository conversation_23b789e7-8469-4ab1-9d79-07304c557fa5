"""
Logger Configuration Utility
Centralized logging setup for the application
"""

import logging
import sys
from pathlib import Path
from typing import Optional

def setup_logging(level: int = logging.INFO, logfile: str | None = None) -> logging.Logger:
    """Simple logging setup used by the application."""

    fmt = "%(asctime)s %(levelname)s: %(message)s"
    logging.basicConfig(level=level, format=fmt, filename=logfile)
    return logging.getLogger(__name__)

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with the given name"""
    return logging.getLogger(f'costco_scraper.{name}') 