"""
Console UI Module - User Interface Layer
Handles all console interactions and display formatting
"""

from typing import Dict, Any, Optional, List
import sys
import os
import platform

class ConsoleUI:
    """Console User Interface for the Costco Scraper"""
    
    def print_header(self, title: str):
        """Print a simple header"""
        bar = "=" * 60
        print(f"\n{bar}\n{title.center(60)}\n{bar}")
    
    def print_main_menu(self):
        """Print the main menu"""
        self.print_header("COSTCO SCRAPER")
        print("1. Run async scraper")
        print("2. Exit\n")
    
    def get_scraping_parameters(self) -> Optional[Dict[str, Any]]:
        """Get scraping parameters from user input"""
        try:
            print("Enter scraping parameters:")
            print("   💡 What products to search for on Costco")
            
            # Get search query
            query = input("Search query [tv]: ").strip()
            if not query:
                query = "tv"
            
            # Get max pages
            max_pages_input = input("Max pages to crawl (1-10, default: 3): ").strip()
            try:
                max_pages = int(max_pages_input) if max_pages_input else 3
                max_pages = max(1, min(10, max_pages))  # Clamp between 1 and 10
            except ValueError:
                max_pages = 3
            
            # Get limit
            limit_input = input("Product limit for detailed scraping 1-50 [5]: ").strip()
            try:
                limit = int(limit_input) if limit_input else 5
                limit = max(1, min(50, limit))  # Clamp between 1 and 50
            except ValueError:
                limit = 5
            
            return {
                'query': query,
                'max_pages': max_pages,
                'limit': limit
            }
            
        except KeyboardInterrupt:
            print("\n❌ Operation cancelled by user")
            return None
        except Exception as e:
            print(f"❌ Error getting parameters: {e}")
            return None
    
    def display_parameters_summary(self, params: Dict[str, Any]):
        """Display parameter summary"""
        print("\nParameters summary:")
        print(f"Query: {params['query']}")
        print(f"Max Pages: {params['max_pages']}")
        print(f"Detail Limit: {params['limit']}")
        print()
    
    def display_scraping_results(self, result: Dict[str, Any]):
        """Display scraping results"""
        print("\nScraper results:")
        print("="*50)
        
        if result.get('success', False):
            products = result.get('products', [])
            listing_products = result.get('listing_products', [])
            total_found = result.get('total_found', 0)
            search_url = result.get('search_url', 'N/A')
            
            print(f"Found {len(listing_products)} products total")
            print(f"Scraped {total_found} products in detail")
            print(f"URL: {search_url}")
            print()
            
            if products:
                for i, product in enumerate(products, 1):
                    self.display_single_product(i, product)
            else:
                print("❌ No detailed products found")
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ {error}")
            if 'search_url' in result:
                print(f"   🔗 Attempted URL: {result['search_url']}")
    
    def display_single_product(self, index: int, product: Dict[str, Any]):
        """Display a single product"""
        print(f"Product {index}:")
        
        # Handle both old and new product structures
        if isinstance(product, dict):
            name = product.get('name', 'N/A')
            sku = product.get('sku', 'N/A')
            price = product.get('price', 'N/A')
            model = product.get('model', '')
            average_rating = product.get('average_rating', '')
            total_ratings = product.get('total_ratings', '')
            product_url = product.get('product_url', 'N/A')
            main_image = product.get('main_image', 'N/A')
            
            print(f"  Name: {name}")
            print(f"  SKU: {sku}")
            print(f"  Model: {model}")
            
            # Price display
            if price != 'N/A':
                print(f"  Price: ${price}")
            else:
                print("   💰 Price: Not available")
            
            # Rating display
            if average_rating:
                rating_display = average_rating
                if total_ratings:
                    rating_display = f"{average_rating} ({total_ratings})"
                print(f"  Rating: {rating_display}")
            
            print(f"  Image: {main_image}")
            print(f"  URL: {product_url}")
        else:
            print(f"   📦 Product: {str(product)}")
        
        print("  " + "-"*50)
    
    def wait_for_user(self):
        """Non-blocking placeholder."""
        print()
    
    def display_error(self, error_message: str):
        """Display error message"""
        print(f"Error: {error_message}")
    
    def display_success(self, message: str):
        """Display success message"""
        print(message)
    
    def display_warning(self, message: str):
        """Display a warning message"""
        print(f"Warning: {message}")
    
    def clear_screen(self):
        """Clear the console screen"""
        os.system('cls' if platform.system() == 'Windows' else 'clear')
    
    def print_separator(self):
        """Print a separator line"""
        print("-" * 80)
    
    def print_goodbye(self):
        """Print goodbye message"""
        print("\nGoodbye! Thanks for using Costco scraper.") 