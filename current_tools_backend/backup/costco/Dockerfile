FROM python:3.12-slim

# Install system deps (just in case)
RUN apt-get update && apt-get install -y --no-install-recommends build-essential && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy requirement files and install
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port for uvicorn
EXPOSE 8000

# Run the API with gunicorn + uvicorn worker for production concurrency
ENV WEB_CONCURRENCY=4

# Use shell form so that $WEB_CONCURRENCY gets expanded at runtime
CMD gunicorn api:app -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --workers $WEB_CONCURRENCY 