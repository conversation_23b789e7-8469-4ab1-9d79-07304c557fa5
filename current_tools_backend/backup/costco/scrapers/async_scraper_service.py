#!/usr/bin/env python3
"""
Async Costco Scraper Service
Enhanced scraper with search and product detail extraction using ScrapingBee
"""

import asyncio
import json
import time
import re
import os
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from urllib.parse import quote_plus

from utils.scrapingbee_client import ScrapingBeeClient

@dataclass
class ProductInfo:
    name: str
    sku: str
    price: str
    product_url: str
    model: str = ""
    average_rating: str = ""
    total_ratings: str = ""
    main_image: str = ""
    features: List[str] = None
    feature_highlights: List[str] = None
    thumbnail_images: List[str] = None
    delivery_date: str = ""
    delivery_zip: str = ""
    delivery_heading: str = ""
    delivery_suffix: str = ""
    delivery_disclaimer: str = ""
    raw_extracted_data: Dict[str, Any] = None

    def __post_init__(self):
        # Initialize lists if None
        if self.features is None:
            self.features = []
        if self.feature_highlights is None:
            self.feature_highlights = []
        if self.thumbnail_images is None:
            self.thumbnail_images = []
        if self.raw_extracted_data is None:
            self.raw_extracted_data = {}

class AsyncCostcoScraper:
    def __init__(self, scrapingbee_api_key: str | None = None):
        # Initialize ScrapingBee client
        self._client = ScrapingBeeClient(scrapingbee_api_key) if scrapingbee_api_key else ScrapingBeeClient.from_env()
        self.base_search_url = "https://www.costco.com/CatalogSearch"
        
    async def scrape_products(self, query: str, max_pages: int = 3, limit: int = 5) -> Dict:
        """Main scraping method that searches and then scrapes detailed product info."""
        try:
            print(f"🔍 Starting Costco search for: '{query}'")
            
            # Step 1: Search for products and get all URLs
            search_results = await self._search_products(query, max_pages)
            
            if not search_results.get('success', False):
                return {"success": False, "error": "Search failed", "search_results": search_results}
            
            all_products = search_results.get('product_links', [])
            
            if not all_products:
                return {"success": False, "error": "No products found in search results"}
            
            print(f"✅ Found {len(all_products)} products total")
            
            # Take top 20 for listing, and top `limit` for detailed scraping
            listing_products = all_products[:20]  # Top 20 for listing
            products_to_scrape = all_products[:limit]  # Top K for detailed scraping
            
            print(f"📦 Scraping detailed info for top {len(products_to_scrape)} products...")
            
            # Step 2: Scrape detailed product information in parallel
            detailed_products = await self._extract_detailed_product_info(products_to_scrape)
            
            # Step 3: Save results to JSON
            await self._save_to_json(detailed_products, query, search_results['search_url'])
            
            return {
                "success": True,
                "products": detailed_products,
                "listing_products": listing_products,
                "search_url": search_results['search_url'],
                "total_found": len(detailed_products),
                "total_listing": len(listing_products)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _search_products(self, keyword: str, max_pages: int = 3) -> Dict:
        """Search for products on Costco and extract all product links"""
        print(f"🔍 Searching Costco for: '{keyword}' (max {max_pages} pages)")
        
        all_products = []
        encoded_keyword = quote_plus(keyword)
        
        search_data = {
            "keyword": keyword,
            "search_url": f"{self.base_search_url}?dept=All&keyword={encoded_keyword}",
            "crawled_at": datetime.now().isoformat(),
            "pages_crawled": 0,
            "total_products": 0,
            "product_links": [],
            "errors": [],
            "success": False
        }
        
        for page in range(1, max_pages + 1):
            print(f"📄 Crawling page {page}...")
            
            # Build search URL with pagination
            if page == 1:
                search_url = f"{self.base_search_url}?dept=All&keyword={encoded_keyword}"
            else:
                search_url = f"{self.base_search_url}?dept=All&keyword={encoded_keyword}&currentPage={page}"
            
            try:
                # JavaScript scenario for page loading
                js_scenario = {
                    "instructions": [
                        {"wait": 3000},
                        {"evaluate": "window.scrollTo(0, document.body.scrollHeight / 2);"},
                        {"wait": 2000}
                    ]
                }
                
                # Fetch page HTML
                html_content = await self._client.fetch_html(
                    search_url, 
                    js_scenario=js_scenario,
                    retries=3
                )
                
                if html_content:
                    print(f"✅ Page {page} loaded successfully")
                    
                    # Parse HTML to find product links
                    page_products = self._extract_product_links(html_content, page)
                    
                    # Remove duplicates
                    unique_products = []
                    seen_urls = set()
                    for product in page_products:
                        if product['url'] not in seen_urls:
                            unique_products.append(product)
                            seen_urls.add(product['url'])
                    
                    all_products.extend(unique_products)
                    search_data['pages_crawled'] = page
                    
                    print(f"   Found {len(unique_products)} products on page {page}")
                    
                    # If no products found on this page and we've crawled at least 1 page, stop
                    if len(unique_products) == 0 and page > 1:
                        print(f"🏁 No products found on page {page}, stopping")
                        break
                        
                else:
                    error_msg = f"❌ Failed to load page {page}"
                    print(error_msg)
                    search_data['errors'].append(error_msg)
                    
            except Exception as e:
                error_msg = f"❌ Error on page {page}: {str(e)}"
                print(error_msg)
                search_data['errors'].append(error_msg)
            
            # Small delay between pages
            await asyncio.sleep(1)
        
        # Update final results
        search_data['total_products'] = len(all_products)
        search_data['product_links'] = all_products
        search_data['success'] = len(all_products) > 0
        
        return search_data

    def _extract_product_links(self, html_content: str, page_num: int) -> List[Dict[str, Any]]:
        """Extract product links from HTML content using regex - PRODUCTS ONLY"""
        products = []
        
        # Regex pattern to find product URLs
        product_pattern = r'https://www\.costco\.com/[^"]*\.product\.\d+\.html'
        
        # Find all product URLs
        product_urls = re.findall(product_pattern, html_content)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in product_urls:
            if url not in seen:
                unique_urls.append(url)
                seen.add(url)
        
        # Filter to get ONLY actual products (exclude services, gift cards, etc.)
        filtered_urls = self._filter_actual_products(unique_urls)
        
        # Extract product names and create product info
        for url in filtered_urls:
            # Extract product name from URL (basic approach)
            name_match = re.search(r'/([^/]+)\.product\.\d+\.html', url)
            if name_match:
                # Convert URL slug to readable name
                name = name_match.group(1).replace('-', ' ').title()
            else:
                name = "Unknown Product"
            
            product_info = {
                "url": url,
                "name": name,
                "price": "N/A",  # Will be filled by detailed scraping
                "found_on_page": page_num
            }
            products.append(product_info)
        
        return products

    def _filter_actual_products(self, urls: List[str]) -> List[str]:
        """Filter URLs to keep only actual products, exclude services and non-products"""
        actual_products = []
        
        # Keywords that indicate non-product items
        exclude_keywords = [
            'handy', 'service', 'installation', 'mounting', 'egift', 'gift-card',
            'warranty', 'protection-plan', 'membership', 'delivery-service',
            'setup-service', 'haul-away', 'concierge', 'tech-support',
            'uber', 'airbnb', 'amazon', 'google-play', 'itunes', 'netflix',
            'spotify', 'starbucks', 'restaurant-gift'
        ]
        
        for url in urls:
            url_lower = url.lower()
            
            # Check if URL contains any exclude keywords
            is_excluded = any(keyword in url_lower for keyword in exclude_keywords)
            
            # Additional checks
            if not is_excluded:
                if ('gift' in url_lower and 'card' in url_lower):
                    is_excluded = True
                elif ('service' in url_lower and any(word in url_lower for word in ['basic', 'premier', 'installation', 'mounting'])):
                    is_excluded = True
            
            # Only add if not excluded
            if not is_excluded:
                actual_products.append(url)
        
        return actual_products

    async def _extract_detailed_product_info(self, product_urls: List[Dict[str, Any]]) -> List[ProductInfo]:
        """Extract detailed product information concurrently."""
        products: List[ProductInfo] = []
        
        overall_start = time.perf_counter()
        
        # Concurrency limit for ScrapingBee (adjust based on API limits)
        concurrency_limit = min(5, len(product_urls))
        semaphore = asyncio.Semaphore(concurrency_limit)
        
        async def _worker(idx: int, product_data: Dict[str, Any]):
            nonlocal products
            async with semaphore:
                start_ts = time.perf_counter()
                print(f"📦 Processing product {idx+1}/{len(product_urls)}: {product_data['name']}")
                
                try:
                    # Extract detailed product info
                    extract_rules = {
                        "name": "h1[itemprop=name]",
                        "sku": "span[data-sku]",
                        "model": "span[data-model-number]",
                        "average_rating": ".bv_avgRating_component_container",
                        "total_ratings": ".bv_numReviews_text",
                        "price": "#pull-right-price > span",
                        "features": {
                            "selector": ".product-info-specs > .row > div:not(.spec-name)", 
                            "type": "list"
                        },
                        "feature_highlights": {
                            "selector": ".pdp-features li",
                            "type": "list"
                        },
                        "main_image": "#heroImage_zoom@src",
                        "thumbnail_images": {
                            "selector": "#imageBrowserWrapper .thumbnail-image@src",
                            "type": "list"
                        },
                        "delivery_date": "#estimatedDeliveryDate",
                        "delivery_zip": "#edd-postal-code",
                        "delivery_heading": "#edd-heading-text",
                        "delivery_suffix": "#edd-heading-text-suffix",
                        "delivery_disclaimer": "#edd-disclaimer-text"
                    }
                    
                    js_scenario = {
                        "instructions": [
                            {"evaluate": "window.location.reload();"},
                            {"wait": 5000}
                        ]
                    }
                    
                    result = await self._client.fetch_with_extraction(
                        product_data['url'],
                        extract_rules,
                        js_scenario=js_scenario,
                        retries=3
                    )
                    
                    if result and result.get('status_code') == 200:
                        extracted_data = result.get('extracted_data', {})
                        
                        # Create ProductInfo object
                        info = ProductInfo(
                            name=extracted_data.get('name', product_data['name']),
                            sku=extracted_data.get('sku', ''),
                            price=extracted_data.get('price', ''),
                            product_url=product_data['url'],
                            model=extracted_data.get('model', ''),
                            average_rating=extracted_data.get('average_rating', ''),
                            total_ratings=extracted_data.get('total_ratings', ''),
                            main_image=extracted_data.get('main_image', ''),
                            features=extracted_data.get('features', []),
                            feature_highlights=extracted_data.get('feature_highlights', []),
                            thumbnail_images=extracted_data.get('thumbnail_images', []),
                            delivery_date=extracted_data.get('delivery_date', ''),
                            delivery_zip=extracted_data.get('delivery_zip', ''),
                            delivery_heading=extracted_data.get('delivery_heading', ''),
                            delivery_suffix=extracted_data.get('delivery_suffix', ''),
                            delivery_disclaimer=extracted_data.get('delivery_disclaimer', ''),
                            raw_extracted_data=extracted_data
                        )
                        
                        products.append(info)
                        print(f"✅ Extracted: {info.name}")
                    else:
                        print(f"❌ Failed to extract product details: {product_data['url']}")
                    
                    # Small delay to be respectful
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    print(f"❌ Error processing {product_data['url']}: {e}")
                finally:
                    elapsed = time.perf_counter() - start_ts
                    print(f"⏱️ Product {idx+1} done in {elapsed:.2f}s")
        
        # Spawn workers
        tasks = [asyncio.create_task(_worker(i, product_data)) for i, product_data in enumerate(product_urls)]
        await asyncio.gather(*tasks)
        
        overall_elapsed = time.perf_counter() - overall_start
        print(f"⏱️ Detailed extraction for {len(product_urls)} products finished in {overall_elapsed:.2f}s")
        
        return products

    async def _save_to_json(self, products: List[ProductInfo], query: str, search_url: str):
        """Save results to JSON file."""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"results/costco_scrape_{query.replace(' ', '_')}_{timestamp}.json"
            
            # Ensure results directory exists
            os.makedirs("results", exist_ok=True)
            
            # Convert ProductInfo objects to dictionaries
            products_data = []
            for product in products:
                product_dict = {
                    'name': product.name,
                    'sku': product.sku,
                    'price': product.price,
                    'product_url': product.product_url,
                    'model': product.model,
                    'average_rating': product.average_rating,
                    'total_ratings': product.total_ratings,
                    'main_image': product.main_image,
                    'features': product.features,
                    'feature_highlights': product.feature_highlights,
                    'thumbnail_images': product.thumbnail_images,
                    'delivery_date': product.delivery_date,
                    'delivery_zip': product.delivery_zip,
                    'delivery_heading': product.delivery_heading,
                    'delivery_suffix': product.delivery_suffix,
                    'delivery_disclaimer': product.delivery_disclaimer,
                    'raw_extracted_data': product.raw_extracted_data
                }
                products_data.append(product_dict)
            
            content = {
                'query': query,
                'search_url': search_url,
                'scraped_at': datetime.now().isoformat(),
                'total_products': len(products),
                'products': products_data
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(content, f, indent=2, ensure_ascii=False)
                
            print(f"📝 Results saved to: {filename}")
            
        except Exception as e:
            print(f"⚠️ Error saving to JSON: {e}")

    async def close(self):
        """Cleanup method (kept for compatibility)"""
        # ScrapingBee client doesn't need explicit cleanup
        pass 