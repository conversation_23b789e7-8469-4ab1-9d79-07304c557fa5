---
description: 
globs: 
alwaysApply: true
---
----------
MUST FOLLOW RULE


i know this task sound little vague so i added the script @ask_user.py you have to ask me questions about stuff that you confused about by executing that script and reading my response. you are alowed to ask me as much as 25 times before ending the converstation you can ask me to give you different parts of the hrml of uber eats, selectors xpaths ... even docs of different modules


You must not finish your answer without asking for my approval using the ask user script 

to run @ask_user.py use the command:
/Users/<USER>/code/new_tools/costco/.venv/bin/python ask_user.py --llm_message 'your question'
