#!/usr/bin/env python3
"""Minimal entry-point for running the Costco scraper from the command line."""

import logging

from utils.logger_config import setup_logging
from core.application_controller import ApplicationController


def main() -> None:
    """Run scraper with predefined parameters (non-interactive)."""

    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Costco scraper started – non-interactive mode")

    params = {
        "query": "tv",
        "max_pages": 3,  # Number of search pages to crawl
        "limit": 5,      # Number of products to scrape in detail
    }

    print("=" * 60)
    print("                 🚀 ASYNC SCRAPER – AUTO MODE                 ")
    print("=" * 60)

    for k, v in params.items():
        print(f"{k.title()}: {v}")

    print("\nInitializing scraper...\n")

    controller = ApplicationController()

    import asyncio

    result = asyncio.run(
        controller.run_async_scraper(
            query=params["query"],
            max_pages=params["max_pages"],
            limit=params["limit"],
        )
    )

    # Simple summary
    if result.get("success"):
        print(f"\n✅ Finished – scraped {result.get('total_found')} products.")
    else:
        print(f"\n❌ Error: {result.get('error')}")


if __name__ == "__main__":
    main() 