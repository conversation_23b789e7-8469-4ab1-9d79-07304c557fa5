# 🚀 Costco Master Scraper - Professional API Edition

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![Async](https://img.shields.io/badge/async-supported-green.svg)]()
[![FastAPI](https://img.shields.io/badge/API-FastAPI-orange.svg)]()
[![ScrapingBee](https://img.shields.io/badge/proxy-ScrapingBee-purple.svg)]()

> **The Most Advanced Costco Scraper Ever Built** - Built following professional scraping architecture with async processing, FastAPI endpoints, and modern Docker deployment.

## 🌟 **What Makes This Special**

### ✅ **Professional-Grade Features**
- **🎯 Async Search & Scraping**: Lightning-fast concurrent processing with intelligent rate limiting
- **🔗 FastAPI Integration**: RESTful API endpoints designed for LLM agents and search tools
- **🛡️ ScrapingBee Integration**: Professional proxy service with stealth capabilities
- **📊 Dual Response Mode**: Returns top 20 listing + detailed top K products in parallel
- **🐳 Docker Ready**: Complete containerization with docker-compose for easy deployment
- **💎 Production Architecture**: Clean OOP design following industry best practices

### 🏗️ **Professional Architecture**
```
costco/
├── core/                           # 🎯 Business Logic
│   ├── models.py                   # 📊 Data models
│   └── application_controller.py   # 🎮 Main controller
├── scrapers/                       # ⚡ Scraping Engine
│   └── async_scraper_service.py    # 🔄 Async scraper
├── utils/                          # 🛠️ Utilities
│   ├── console_ui.py              # 🖥️ CLI interface
│   ├── logger_config.py           # 📝 Logging
│   └── scrapingbee_client.py      # 🕷️ API wrapper
├── api.py                         # 🌐 FastAPI endpoints
├── main.py                        # 🚀 CLI entry point
├── Dockerfile                     # 🐳 Container config
└── docker-compose.yml            # 📦 Deployment config
```

## 🚀 **Quick Start**

### Method 1: Docker Deployment (Recommended)

```bash
# 1. Clone and navigate
cd costco

# 2. Set your ScrapingBee API key
export SCRAPINGBEE_API_KEY="your_api_key_here"

# 3. Deploy with Docker
docker-compose up --build

# 4. API is now available at http://localhost:8000
```

### Method 2: Local Development

```bash
# 1. Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# 2. Install dependencies
pip install -r requirements.txt

# 3. Set API key
export SCRAPINGBEE_API_KEY="your_api_key_here"

# 4. Run API server
uvicorn api:app --reload

# 5. Or run CLI version
python main.py
```

## 🌐 **API Endpoints**

### **GET /** - API Documentation
Returns machine-readable API manual for LLM agents.

### **GET /search** - Main Search Endpoint
Search Costco and return structured product data.

**Parameters:**
- `query` (required): Search keywords (e.g., "tv", "laptop", "appliances")
- `scrapingbee_api_key` (required): Your ScrapingBee API key
- `max_pages` (optional): Number of search pages to crawl (1-10, default: 3)
- `limit` (optional): Number of products to scrape in detail (1-50, default: 5)

**Example Request:**
```bash
curl "http://localhost:8000/search?query=tv&scrapingbee_api_key=YOUR_KEY&max_pages=2&limit=5"
```

**Example Response:**
```json
{
  "success": true,
  "total_found": 5,
  "total_listing": 18,
  "search_url": "https://www.costco.com/CatalogSearch?dept=All&keyword=tv",
  "products": [
    {
      "name": "Samsung 75\" Class QN90C Neo QLED 4K Smart TV",
      "sku": "1558724",
      "price": "2499.99",
      "model": "QN75QN90CAFXZA",
      "average_rating": "4.6",
      "total_ratings": "(245)",
      "main_image": "https://richmedia.ca-richimage.com/...",
      "features": ["75-inch display", "4K UHD", "Smart TV"],
      "product_url": "https://www.costco.com/samsung-75-class-qn90c..."
    }
  ],
  "listing_products": [
    {
      "url": "https://www.costco.com/samsung-75-class-qn90c...",
      "name": "Samsung 75 Class Qn90c Neo Qled 4k Smart Tv",
      "price": "N/A",
      "found_on_page": 1
    }
  ]
}
```

## 🔧 **Configuration**

### **Environment Variables**
```bash
# Required
SCRAPINGBEE_API_KEY=your_scrapingbee_api_key

# Optional
WEB_CONCURRENCY=4              # Number of workers for production
```

### **ScrapingBee API Key**
Get your API key from [ScrapingBee](https://www.scrapingbee.com/):
1. Sign up for free account (1,000 requests/month)
2. Get your API key from dashboard
3. Set as environment variable

## 🐳 **Docker Deployment**

### **Single Container**
```bash
# Build and run
docker build -t costco-scraper .
docker run -p 8000:8000 -e SCRAPINGBEE_API_KEY=your_key costco-scraper
```

### **Docker Compose (Production)**
```bash
# Set environment variable
echo "SCRAPINGBEE_API_KEY=your_key" > .env

# Deploy stack
docker-compose up -d

# Scale workers
docker-compose up -d --scale costco_api=3
```

## 🧪 **Testing**

### **Test API Endpoints**
```bash
# Install test dependencies
pip install requests

# Run API tests
python test_api.py

# Or test with curl
curl "http://localhost:8000/search?query=laptop&scrapingbee_api_key=YOUR_KEY&limit=3"
```

### **CLI Testing**
```bash
# Run console version
python main.py
```

## 📊 **Data Models**

### **Product Model**
```python
@dataclass
class Product:
    name: str                    # Product name
    sku: str                     # Costco SKU
    price: str                   # Current price
    product_url: str             # Product page URL
    model: str                   # Model number
    average_rating: str          # Average rating (e.g., "4.6")
    total_ratings: str           # Total ratings (e.g., "(245)")
    main_image: str              # Main product image URL
    features: List[str]          # Product features list
    feature_highlights: List[str] # Key features
    thumbnail_images: List[str]   # Image gallery URLs
    delivery_date: str           # Estimated delivery
    # ... additional fields
```

## 🔄 **Workflow**

1. **Search Phase**: Crawls Costco search pages to find product URLs
2. **Filter Phase**: Removes services, gift cards, and non-products
3. **Listing Phase**: Returns top 20 products for listing display
4. **Detail Phase**: Scrapes full product details for top K products (async/parallel)
5. **Response Phase**: Returns structured JSON with both listing and detailed data

## 🚀 **Performance**

- **Async Processing**: Up to 5 concurrent product scrapes
- **Smart Filtering**: Automatically excludes non-product items
- **Rate Limiting**: Respectful delays between requests
- **Caching**: Scraper instance caching for repeated requests
- **Scalability**: Docker-based horizontal scaling

## 🎯 **LLM Integration**

This API is designed for seamless integration with LLM agents and search tools:

```python
# Example LLM integration
import requests

def search_costco(query: str, api_key: str, limit: int = 5):
    """Search Costco for products - LLM-friendly function"""
    response = requests.get(
        "http://your-api-url/search",
        params={
            "query": query,
            "scrapingbee_api_key": api_key,
            "limit": limit
        }
    )
    return response.json()

# Usage in LLM tool
result = search_costco("wireless headphones", "your_key", 3)
```

## ⚠️ **Important Notes**

- **API Limits**: ScrapingBee free tier provides 1,000 requests/month
- **Rate Limiting**: Built-in delays to respect Costco's servers
- **Legal Compliance**: Use responsibly and follow Costco's terms of service
- **Error Handling**: Comprehensive error handling with graceful degradation

## 🔧 **Troubleshooting**

### **Common Issues**
1. **API Key Error**: Ensure SCRAPINGBEE_API_KEY is set correctly
2. **No Products Found**: Try different search terms or check selectors
3. **Docker Issues**: Ensure port 8000 is available
4. **Import Errors**: Verify all dependencies are installed

### **Debug Mode**
```bash
# Enable verbose logging
export LOG_LEVEL=DEBUG
python main.py
```

---

**🏆 Built with Professional Architecture** | **⚡ Powered by Async Python & FastAPI** | **🎯 Optimized for LLM Integration** | **🐳 Production-Ready Docker Deployment** 