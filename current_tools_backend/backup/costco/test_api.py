import requests, pprint, sys, os, json, time

BASE_URL = os.environ.get("API_URL", "http://localhost:8000")

params = {
    "query": "tv",
    "max_pages": 2,
    "limit": 3,
    "scrapingbee_api_key": os.getenv("SCRAPINGBEE_API_KEY") or "********************************************************************************",
}

print(f"Requesting {BASE_URL}/search with params {params}")
resp = requests.get(f"{BASE_URL}/search", params=params, timeout=120)
print(f"Status: {resp.status_code}")

data = resp.json()

if not data.get("success"):
    print("API reported failure:")
    pprint.pprint(data)
    raise SystemExit(1)

expected = params["limit"]
actual = data.get("total_found")
print(f"✔️  Success flag OK. Received {actual}/{expected} detailed products.")

# Pretty-print just product titles to keep output short
for i, prod in enumerate(data.get("products", []), 1):
    print(f"{i}. {prod.get('name')} – ${prod.get('price')} (SKU: {prod.get('sku')})")

print(f"Listing contains {len(data.get('listing_products', []))} basic products.")
for prod in data.get('listing_products', [])[:5]:
    print(f"- {prod['name']} ({prod.get('price', 'N/A')}) -> {prod['url']}")

# Ensure results directory exists
os.makedirs("results", exist_ok=True)

timestamp = int(time.time())
outfile = f"results/api_test_{params['query'].replace(' ', '_')}_{timestamp}.json"

with open(outfile, "w", encoding="utf-8") as f:
    json.dump(data, f, ensure_ascii=False, indent=2)

print(f"Full response written to {outfile}") 